import subprocess
import re

def run_cmake_and_filter_output():
    cmd = 'cmake -S . -B build -DARCPKG_AUTO_UPDATE=0'

    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, shell=True)
        all_output = []
        filtered_lines = []

        for line in process.stdout:
            line = line.rstrip()
            all_output.append(line)
            if line.startswith('-- ARCPKG/I: Installing') or line.startswith('-- ARCPKG/I: arcpkg_define ('):
                filtered_lines.append(line)

        process.wait()
        retcode = process.returncode

        if retcode != 0:
            print(f"Command failed with return code: {retcode}")
            print("Full output (including error messages):")
            print("\n".join(all_output))

        return filtered_lines, retcode

    except Exception as e:
        print(f"Exception occurred while running command: {e}")
        return [], -1


def transform_line(line):
    """
    处理两种行：
    - Installing开头的包，权限是 read
    - arcpkg_define开头的包，权限是 read,write
    输出格式：generic/<pkg_user>/<pkg_name> <permissions>
    """
    if line.startswith('-- ARCPKG/I: Installing'):
        m = re.search(r'Installing\s+(\S+)\s+for', line)
        if not m:
            return None

        pkg_full = m.group(1)
        if '@' not in pkg_full or '/' not in pkg_full:
            return None

        name_version, pkg_user = pkg_full.rsplit('@', 1)
        pkg_name = name_version.split('/')[0]
        permissions = 'read'
        return f"generic/{pkg_user}/{pkg_name} {permissions}"

    elif line.startswith('-- ARCPKG/I: arcpkg_define ('):
        m = re.search(r'arcpkg_define \(([^,]+)', line)
        if not m:
            return None

        definition = m.group(1).strip()
        if '/' not in definition or '@' not in definition:
            return None

        pkg_name = definition.split('/')[0]
        user_part = definition.split('@',1)[1]
        pkg_user = user_part.split(':')[0]
        permissions = 'read,write'
        return f"generic/{pkg_user}/{pkg_name} {permissions}"

    else:
        return None


if __name__ == '__main__':
    filtered, code = run_cmake_and_filter_output()
    if filtered:
        print("Required package permissions:")
        for line in filtered:
            tline = transform_line(line)
            if tline:
                print(tline)
    if code != 0:
        print(f"Command did not execute successfully, return code: {code}")

