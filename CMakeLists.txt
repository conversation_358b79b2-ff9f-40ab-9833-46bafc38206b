cmake_minimum_required(VERSION 3.10)
project(AdsPlanning)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# Global options
if (MSVC)
  add_definitions(-D_USE_MATH_DEFINES)
  add_definitions(-DNOMINMAX)
  add_definitions("/source-charset:utf-8")
endif()

if(DEFINED ENV{CI_COMMIT_TAG})
  set(PROJECT_VERSION $ENV{CI_COMMIT_TAG})
else()
  set(PROJECT_VERSION "main")
endif()

# if(CMAKE_BUILD_TYPE STREQUAL "ASan")
#   message(STATUS "ASan build type detected. Force-appending sanitizer flags.")
  
#   # 直接向全局编译标志追加ASan选项
#   set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address -fno-omit-frame-pointer -g")
#   set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address -fno-omit-frame-pointer -g")

#   # 直接向全局链接标志追加ASan选项
#   set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address")
#   set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -fsanitize=address")
#   set(CMAKE_MODULE_LINKER_FLAGS "${CMAKE_MODULE_LINKER_FLAGS} -fsanitize=address")
# endif()


include(cmake/arcpkg.cmake)
# include(cmake/opencv.cmake)
# include(cmake/overlook.cmake)

arcpkg_import(
  mlog/0.5.5@arcpkg
  dlog/0.1.0@arcpkg
  ghc-filesystem/1.5.14@arcpkg
)

include_directories(${PROJECT_SOURCE_DIR}/third_party)

add_subdirectory(third_party/osqp)
# add_subdirectory(third_party/glog)
# add_subdirectory(third_party/ruckig)
add_subdirectory(sdk)
add_subdirectory(testbed)