# Maintainer: <PERSON><PERSON><PERSON> <<EMAIL>>
# Homepage: https://git.arcsoft.com.cn/lny1856/arcpkg

cmake_minimum_required(VERSION 3.14) # 3.3

# CMake 3.0: Bracket argument
# CMake 3.13: cmake -S -B, NOT USED
# CMake 3.15: cmake --install
# CMake 3.15: cmake --install --strip
# CMake 3.3: support new if() IN_LIST operator
# CMake 3.16: correct INTERFACE_LINK_LIBRARIES for exported shared targets, use INTERFACE_LINK_LIBRARIES instead of LINK_INTERFACE_LIBRARIES
# CMake 3.1: target_sources()
# CMake 3.1: $<TARGET_PDB_FILE:tgt>
# CMake 3.14: ARCH_INDEPENDENT in write_basic_package_version_file()
# CMake 3.14: install(CODE) and install(SCRIPT) support generator expressions
# CMake 3.12: list(TRANSFORM)
# CMake 3.11: set_target_properties(IMPORTED_GLOBAL)
# CMake 3.12: string(JOIN)
# CMake 3.21: get_property(IMPORTED_TARGETS)
# CMake 3.18: An ALIAS can target a non-GLOBAL imported target

if(ARCPKG_INCLUDE_GUARD)
  return()
endif()
set(ARCPKG_INCLUDE_GUARD 1)

###########################################################
# Global CMake settings and CMake version requirement
###########################################################

set(ARCPKG_VERSION "0.20-WIP")

# CHANGELOG: https://git.arcsoft.com.cn/lny1856/arcpkg/blob/master/CHANGELOG.md

# Global settings
if(POLICY CMP0057)
  cmake_policy(SET CMP0057 NEW) # Support new if() IN_LIST operator, warns when CMake>=3.21.2
endif()
if(POLICY CMP0087)
  cmake_policy(SET CMP0087 NEW) # install(CODE) and install(SCRIPT) support generator expressions, warns when CMake>=3.21.2
endif()
# include(CMakeParseArguments) # cmake_parse_arguments() for CMake<3.5
include(CMakePackageConfigHelpers) # write_basic_package_version_file()

# set(CPACK_GENERATOR "ZIP")
# set(CPACK_ARCHIVE_COMPONENT_INSTALL ON)
# include(CPackComponent) # cpack_add_component()
# include(CPack)

# set global variables
macro(arcpkg_set VAR_NAME DEFAULT_VALUE)
  if(NOT DEFINED ${VAR_NAME} AND DEFINED ENV{${VAR_NAME}})
    set(${VAR_NAME} $ENV{${VAR_NAME}})
  endif()
  if(NOT DEFINED ${VAR_NAME})
    set(${VAR_NAME} ${DEFAULT_VALUE})
  endif()
endmacro(arcpkg_set)


###########################################################
# Global variables of ARCPKG
###########################################################

arcpkg_set(ARCPKG_VERBOSE 3)

arcpkg_set(ARTIFACTORY_URL "https://artifactory.arcsoft.com.cn/artifactory") # NOTE: use http as CMake SSL error in docker
arcpkg_set(ARTIFACTORY_REPO "generic")
arcpkg_set(ARTIFACTORY_USER "$ENV{ARTIFACTORY_USER}")
arcpkg_set(ARTIFACTORY_PSWD "$ENV{ARTIFACTORY_PSWD}")

# The default pkg search path, and pkgs are installed here.
arcpkg_set(ARCPKG_ROOT "~/.arcpkg")
get_filename_component(ARCPKG_ROOT "${ARCPKG_ROOT}" REALPATH)

# Custom pkg search paths used before default search path ARCPKG_ROOT.
arcpkg_set(ARCPKG_PATH "")

arcpkg_set(ARCPKG_DEFAULT_USER "arcpkg")
arcpkg_set(ARCPKG_DEFAULT_CHANNEL "stable")
arcpkg_set(ARCPKG_DEBUG_SUFFIX "_d")
arcpkg_set(ARCPKG_EXPORT_GLOBAL "GLOBAL") # "GLOBAL" OR "", whether to export global targets.

# Update pkg when CMake Configuration automatically.
arcpkg_set(ARCPKG_AUTO_UPDATE ON)

# Search deploy with exact hints (tags) matching.
arcpkg_set(ARCPKG_EXACT_MODE OFF)

# The format of local deploy.
# 1. <user>/<name>/<version>/<name>-<version>-<abi-name>
# 2. <user>/<name>/<version>/<abi-name>
# 3. <user>/<name>/<name>-<version>-<abi-name> (deprecated)
arcpkg_set(ARCPKG_LOCAL_PKG_FORMAT_VERSION 2)

# NOTE: to slow to use find_package
# find_package(Python COMPONENTS Interpreter QUIET)
arcpkg_set(ARCPKG_NO_PYTHON "$ENV{ARCPKG_NO_PYTHON}")
if(NOT ARCPKG_NO_PYTHON)
  find_program(Python_EXECUTABLE python NO_CMAKE_FIND_ROOT_PATH)
else()
  unset(Python_EXECUTABLE CACHE)
endif()
# message("Python_EXECUTABLE: ${Python_EXECUTABLE}")

set(ARCPKG_SCRIPT "${CMAKE_CURRENT_LIST_FILE}")
set(ARCPKG_COMMAND ${CMAKE_COMMAND} -P "${ARCPKG_SCRIPT}" -v ${ARCPKG_VERBOSE})
set(ARCPKG_DEFINE_SINGLE_VALUE_KEYS NAME VERSION USER CHANNEL AUTHOR MAINTAINER SUMMARY DESCRIPTION URL HOMEPAGE REQUIRES)
set(ARCPKG_DEFINE_SINGLE_VALUE_KEYS_UNPARSED COPYRIGHT LICENSE) # parsed from unparsed arguments (second pass)
set(ARCPKG_DEFINE_MULTIPLE_VALUES_KEYS AUTHORS MAINTAINERS HINTS)
set(ARCPKG_ATTRIBUTES_PARSED
  ${ARCPKG_DEFINE_SINGLE_VALUE_KEYS}
  ${ARCPKG_DEFINE_SINGLE_VALUE_KEYS_UNPARSED}
  ${ARCPKG_DEFINE_MULTIPLE_VALUES_KEYS}
  TYPE INSTALL_DIR INC_DIR LIB_DIR BIN_DIR
) # arcpkg.txt

# Package attributes, can be accessed by two methods:
# 1. By function: arcpkg_get_pkg_property(<prop-var> <target> ARCPKG_<attribute>), e.g. arcpkg_get_pkg_property(PKG_NAME arcpkg_test::foo ARCPKG_NAME).
# 2. By variable: <user>_<name>_<attribute>, e.g. arcpkg_test_foo_NAME.
set(ARCPKG_ATTRIBUTES ${ARCPKG_ATTRIBUTES_PARSED} DIR CONFIG_DIR LIBS BINS RECIPE)


###########################################################
# Message functions
###########################################################

function(arcpkg_debug)
  if(ARCPKG_VERBOSE GREATER 2)
    message(STATUS "ARCPKG/D: ${ARGN}")
  endif()
endfunction()

function(arcpkg_echo)
  if(ARCPKG_VERBOSE GREATER 1)
    message(STATUS "ARCPKG/I: ${ARGN}")
  endif()
endfunction()

function(arcpkg_warn)
  if(ARCPKG_VERBOSE GREATER 0)
    message(STATUS "ARCPKG/W: ${ARGN}")
  endif()
endfunction()

function(arcpkg_error)
  string(REPLACE "\n;" "\n" ARGN "${ARGN}")
  message(FATAL_ERROR "ARCPKG/E: ${ARGN}")
endfunction()


# Internal error.
function(arcpkg_error_i)
  arcpkg_error("${ARGN}" "\nPlease contact the maintainers and report bug to https://git.arcsoft.com.cn/lny1856/arcpkg/issues.")
endfunction(arcpkg_error_i)



###########################################################
# Global variables: ARCPKG_PLATFORM, ARCPKG_ARCH
###########################################################

function(arcpkg_guess_platform OUTPUT_VAR)
  if(ANDROID)
    set(PLATFORM "android")
  elseif(IOS)
    set(PLATFORM "ios")
  elseif(TIZEN)
    set(PLATFORM "tizen")
  elseif(EMSCRIPTEN)
    set(PLATFORM "emcc")
  elseif(QTEE)
    set(PLATFORM "qtee")
  elseif(OHOS)
    set(PLATFORM "ohos")
  elseif(RASPBERRY_PI)
    set(PLATFORM "pi")
  elseif(HIMIX100)
    set(PLATFORM "himix100")
  elseif(MINGW)
    set(PLATFORM "mingw")
  elseif(MSVC)
    # 1200      = VS  6.0
    # 1300      = VS  7.0
    # 1310      = VS  7.1
    # 1400      = VS  8.0 (v80 toolset)
    # 1500      = VS  9.0 (v90 toolset)
    # 1600      = VS 10.0 (v100 toolset)
    # 1700      = VS 11.0 (v110 toolset)
    # 1800      = VS 12.0 (v120 toolset)
    # 1900      = VS 14.0 (v140 toolset)
    # 1910-1919 = VS 15.0 (v141 toolset)
    # 1920-1929 = VS 16.0 (v142 toolset)
    # 1930-1949 = VS 17.0 (v143 toolset)
    if(MSVC_VERSION GREATER 1949)
      arcpkg_error("Unknown MSVC version: ${MSVC_VERSION}")
    elseif(MSVC_VERSION GREATER_EQUAL 1930)
      set(PLATFORM "vs2022")
    elseif(MSVC_VERSION GREATER_EQUAL 1920)
      set(PLATFORM "vs2019")
    elseif(MSVC_VERSION GREATER_EQUAL 1910)
      set(PLATFORM "vs2017")
    elseif(MSVC14) # vs2015
      set(PLATFORM "vs2015")
    elseif(MSVC12) # vs2013
      set(PLATFORM "vs2013")
    elseif(MSVC11) # vs2012
      set(PLATFORM "vs2012")
    elseif(MSVC10) # vs2010
      set(PLATFORM "vs2010")
    elseif(MSVC90) # vs2008
      set(PLATFORM "vs2008")
    elseif(MSVC80) # vs2005
      set(PLATFORM "vs2005")
    elseif(MSVC60) # vc6
      set(PLATFORM "vc6")
    else()
      arcpkg_error_i("Unknown MSVC version (MSVC_VERSION=${MSVC_VERSION}), please define ARCPKG_PLATFORM.")
    endif()
    if(WINDOWS_STORE)
      set(PLATFORM "${PLATFORM}-uwp")
    endif()
  elseif(APPLE AND NOT CMAKE_CROSSCOMPILING)
    set(PLATFORM "mac")
  elseif(ARCBUILD_PLATFORM) # arcbuild
    # aliases of platforms: shares same compiler
    if(ARCBUILD_PLATFORM STREQUAL "am62a")
      set(PLATFORM "tda4")
    else()
      set(PLATFORM ${ARCBUILD_PLATFORM})
    endif()
  elseif(UNIX AND NOT CMAKE_CROSSCOMPILING)
    set(PLATFORM "linux")
  else()
    arcpkg_error("Fail to guess the target platform, please define ARCPKG_PLATFORM.")
  endif()
  set(${OUTPUT_VAR} ${PLATFORM} PARENT_SCOPE)
endfunction()


function(arcpkg_guess_arch OUTPUT_VAR)
  if(EMSCRIPTEN)
    set(ARCH)
  elseif(CMAKE_OSX_ARCHITECTURES)
    set(ARCH ${CMAKE_OSX_ARCHITECTURES})
  elseif(DEFINED ARCBUILD_ARCH) # arcbuild>1.5
    set(ARCH ${ARCBUILD_ARCH})
  elseif(ARCBUILD AND DEFINED SDK_ARCH) # arcbuild<=1.5
    set(ARCH ${SDK_ARCH})
  elseif(NOT CMAKE_CROSSCOMPILING)
    if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "^(arm64|ARM64)$") # Windows
      set(ARCH "arm64")
    elseif(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "aarch64") # Linux, Android, etc
      set(ARCH "aarch64")
    elseif(CMAKE_CXX_COMPILER_ARCHITECTURE_ID MATCHES "^(ARM64)$")
      set(ARCH "arm64")
    elseif(CMAKE_SIZEOF_VOID_P EQUAL 4)
      set(ARCH "x86")
    elseif(CMAKE_SIZEOF_VOID_P EQUAL 8)
      if(APPLE)
        set(ARCH "x86_64")
      else()
        set(ARCH "x64")
      endif()
    else()
      arcpkg_error_i("Fail to guess the target architecture, please define ARCPKG_ARCH.\n"
                     "- CMAKE_HOST_SYSTEM_PROCESSOR: ${CMAKE_HOST_SYSTEM_PROCESSOR}\n"
                     "- CMAKE_SIZEOF_VOID_P: ${CMAKE_SIZEOF_VOID_P}")
    endif()
  elseif(ANDROID_ABI MATCHES "^armeabi-v7a")
    set(ARCH "armv7-a")
  elseif(ANDROID_ABI STREQUAL "arm64-v8a")
    set(ARCH "armv8-a")
  elseif(ANDROID_ABI)
    set(ARCH ${ANDROID_ABI})
  elseif(OHOS_ARCH)
    set(ARCH ${OHOS_ARCH})
  else()
    if(CMAKE_C_FLAGS MATCHES "-march=([^ ]+)")
      set(ARCH ${CMAKE_MATCH_1})
    elseif(CMAKE_C_FLAGS MATCHES "--target=([^-]+)")
      set(ARCH ${CMAKE_MATCH_1})
    elseif(CMAKE_C_FLAGS MATCHES "-target ([^-]+)")
      set(ARCH ${CMAKE_MATCH_1})
    else()
      arcpkg_error("Fail to guess the target architecture, please define ARCPKG_ARCH.\n"
                   "- CMAKE_C_FLAGS: ${CMAKE_C_FLAGS}")
    endif()
  endif()
  set(${OUTPUT_VAR} ${ARCH} PARENT_SCOPE)
endfunction()

function(arcpkg_get_linux_distro_impl CONTENT OUTPUT_VAR)
  # message("CONTENT: ${CONTENT}")
  if(CONTENT MATCHES "\nID=\"?([a-z0-9]+)\"?")
    set(ID ${CMAKE_MATCH_1})
  else()
    arcpkg_error_i("Can not parse distro ID from ${DISTRO_FILE}: ${CONTENT}")
  endif()
  if(CONTENT MATCHES "VERSION_ID=\"?([^\" ]+)\"?")
    set(${OUTPUT_VAR} "${ID}-${CMAKE_MATCH_1}" PARENT_SCOPE)
  else()
    set(${OUTPUT_VAR} ${ID} PARENT_SCOPE)
  endif()
endfunction()

function(arcpkg_get_linux_distro OUTPUT_VAR)
  set(DISTRO_FILE "/etc/os-release")
  if(CMAKE_HOST_UNIX AND CMAKE_HOST_SYSTEM_NAME STREQUAL "Linux" AND EXISTS ${DISTRO_FILE})
    # message("CMAKE_HOST_UNIX: ${CMAKE_HOST_UNIX}")
    # message("CMAKE_HOST_SYSTEM_NAME: ${CMAKE_HOST_SYSTEM_NAME}")
    file(READ ${DISTRO_FILE} CONTENT)
    arcpkg_get_linux_distro_impl(${CONTENT} DISTRO_DETECTED)
    set(${OUTPUT_VAR} ${DISTRO_DETECTED} PARENT_SCOPE)
  else()
    set(${OUTPUT_VAR} PARENT_SCOPE)
  endif()
endfunction(arcpkg_get_linux_distro)


function(arcpkg_get_host_platform OUTPUT_VAR)
  if(CMAKE_HOST_SYSTEM_NAME STREQUAL "Linux")
    set(${OUTPUT_VAR} "linux" PARENT_SCOPE)
  elseif(CMAKE_HOST_SYSTEM_NAME STREQUAL "Windows")
    set(${OUTPUT_VAR} "windows" PARENT_SCOPE)
  elseif(CMAKE_HOST_SYSTEM_NAME STREQUAL "Darwin")
    set(${OUTPUT_VAR} "mac" PARENT_SCOPE)
  else()
    arcpkg_error_i("Unknown host platform: CMAKE_HOST_SYSTEM_NAME=${CMAKE_HOST_SYSTEM_NAME}!")
  endif()
endfunction(arcpkg_get_host_platform)


if(NOT DEFINED CMAKE_SCRIPT_MODE_FILE)
  # Enable solution folder for MSVC
  set_property(GLOBAL PROPERTY USE_FOLDERS ON)
  # platform and arch
  if(NOT ARCPKG_PLATFORM)
    arcpkg_guess_platform(ARCPKG_PLATFORM)
  endif()
  if(NOT ARCPKG_ARCH)
    arcpkg_guess_arch(ARCPKG_ARCH)
  endif()
  if(ARCPKG_ARCH)
    list(SORT ARCPKG_ARCH)
  endif()
  # Linux distro
  if(NOT ARCPKG_LINUX_DISTRO)
    arcpkg_get_linux_distro(ARCPKG_LINUX_DISTRO)
  endif()
endif()


###########################################################
# Global checks
###########################################################

# Check remote variables
function(arcpkg_check_remote_variables)
  set(PASSED ON)
  foreach(X ARTIFACTORY_URL ARTIFACTORY_REPO ARTIFACTORY_USER ARTIFACTORY_PSWD)
    if(NOT ${X})
      arcpkg_warn("The variable ${X} is required for remote operation. Please define it or set the system environment variable (${X}).")
      set(PASSED OFF)
    endif()
  endforeach()
  if(NOT PASSED)
    arcpkg_warn([[Please set ARTIFACTORY_USER/ARTIFACTORY_PSWD environment variables or CMake variables]])
    arcpkg_warn([[- ARTIFACTORY_USER: the user name.]])
    arcpkg_warn([[- ARTIFACTORY_PSWD: the "API Key", "Identity Token" or "Encrypted Password" in "User Profile" page (https://artifactory.arcsoft.com.cn/ui/admin/artifactory/user_profile)]])
    arcpkg_error("Please define the required variables before using remote operations!")
  endif()
endfunction(arcpkg_check_remote_variables)


if(NOT CMAKE_SCRIPT_MODE_FILE)
  arcpkg_debug("ARCPKG_VERSION: ${ARCPKG_VERSION}")
  arcpkg_debug("ARTIFACTORY_URL: ${ARTIFACTORY_URL}")
  arcpkg_debug("ARTIFACTORY_REPO: ${ARTIFACTORY_REPO}")
  # arcpkg_debug("ARTIFACTORY_USER: ${ARTIFACTORY_USER}")
  arcpkg_debug("Python_EXECUTABLE: ${Python_EXECUTABLE}")
  if(NOT ARCPKG_AUTO_UPDATE)
    arcpkg_warn("Disable package updating when CMake Configuration!")
  endif()
  if(ARCPKG_LINUX_DISTRO)
    arcpkg_debug("ARCPKG_LINUX_DISTRO: ${ARCPKG_LINUX_DISTRO}")
  endif()
  arcpkg_debug("ARCPKG_PLATFORM: ${ARCPKG_PLATFORM}")
  arcpkg_debug("ARCPKG_ARCH: ${ARCPKG_ARCH}")

  # Install root directory
  # https://cmake.org/cmake/help/latest/variable/CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT.html
  if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
    set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install" CACHE PATH "Installation Directory" FORCE)
  endif()
  if(NOT CMAKE_INSTALL_RPATH)
    set(CMAKE_INSTALL_RPATH "$ORIGIN:$ORIGIN/../lib")
  endif()
  if(CMAKE_CROSSCOMPILING)
    set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
  endif()

  # Save libs and executables in the same place
  if(NOT EXECUTABLE_OUTPUT_PATH)
    set(EXECUTABLE_OUTPUT_PATH "${CMAKE_BINARY_DIR}" CACHE PATH "Output directory for applications")
    set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${EXECUTABLE_OUTPUT_PATH} CACHE INTERNAL "") # static
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${EXECUTABLE_OUTPUT_PATH} CACHE INTERNAL "") # shared
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${EXECUTABLE_OUTPUT_PATH} CACHE INTERNAL "") # exe
  endif()
endif()


###########################################################
# Common functions
###########################################################


# Get VCS (Version Control System) revision of given source directory.
function(arcpkg_get_vcs_revision REVISION_VAR SOURCE_DIR)
  unset(REVISION)
  if(NOT Git_FOUND)
    find_package(Git QUIET)
  endif()
  if(Git_FOUND)
    execute_process(
      COMMAND ${GIT_EXECUTABLE} rev-parse ${extra_git_args} HEAD
      WORKING_DIRECTORY "${SOURCE_DIR}"
      OUTPUT_VARIABLE REVISION
      RESULT_VARIABLE RET
    )
    if(RET STREQUAL 0)
      set(VCS_NAME git)
    else()
      unset(REVISION)
    endif()
  endif()
  if(REVISION)
    # skip
  elseif(EXISTS "${SOURCE_DIR}/.svn")
    if(NOT Subversion_FOUND)
      find_package(Subversion QUIET)
    endif()
    if(Subversion_FOUND)
      Subversion_WC_INFO("${SOURCE_DIR}" Project)
      set(REVISION ${Project_WC_REVISION})
      set(VCS_NAME svn)
    endif()
  elseif(EXISTS "${SOURCE_DIR}/.hg")
    if(NOT HG_FOUND)
      find_package(Hg QUIET)
    endif()
    if(HG_FOUND)
      HG_WC_INFO("${SOURCE_DIR}" Project)
      set(REVISION ${Project_WC_REVISION})
      set(VCS_NAME hg)
    endif()
  endif()
  if(REVISION)
    string(STRIP ${REVISION} REVISION)
    if(NOT A_SHORT)
      set(REVISION "${VCS_NAME}:${REVISION}")
    endif()
  endif()
  set(${REVISION_VAR} ${REVISION} PARENT_SCOPE)
endfunction(arcpkg_get_vcs_revision)


# Get VCS (Version Control System) revision of project directory.
function(arcpkg_get_project_vcs_revision REVISION_VAR)
  if(DEFINED ENV{CI_COMMIT_SHA})
    set(REVISION $ENV{CI_COMMIT_SHA})
  else()
    arcpkg_get_vcs_revision(REVISION "${PROJECT_SOURCE_DIR}")
    if(NOT REVISION)
      arcpkg_get_vcs_revision(REVISION "${CMAKE_SOURCE_DIR}")
    endif()
  endif()
  set(${REVISION_VAR} ${REVISION} PARENT_SCOPE)
endfunction(arcpkg_get_project_vcs_revision)


# Escape special characters in the string for regex matching.
# Usage: arcpkg_escape_for_regex(<in-str> <out-var>)
function(arcpkg_escape_for_regex VAR OUTPUT_VAR)
  string(REGEX REPLACE "([][+.*()^])" "\\\\\\1" NEW_VAR "${VAR}")
  set(${OUTPUT_VAR} ${NEW_VAR} PARENT_SCOPE)
endfunction()


# Replace list items by regex.
# Usage: arcpkg_list_replace(<match-regex> <replace-expr> <out-var> [input...])
function(arcpkg_list_replace REGEX REGEX_REPLACE OUTPUT_VAR)
  set(NEW_LIST ${ARGN})
  list(TRANSFORM NEW_LIST REPLACE "${REGEX}" "${REGEX_REPLACE}")
  set(${OUTPUT_VAR} ${NEW_LIST} PARENT_SCOPE)
endfunction()


# Filter list items by regex.
# Usage: arcpkg_list_filter(<match-regex> <out-var> <input>...)
function(arcpkg_list_filter REGEX OUTPUT_VAR)
  set(NEW_LIST ${ARGN})
  list(FILTER NEW_LIST INCLUDE REGEX "${REGEX}")
  set(${OUTPUT_VAR} ${NEW_LIST} PARENT_SCOPE)
endfunction(arcpkg_list_filter)


# Get the list item by regex matching.
# Usage: arcpkg_list_get_first_by_regex(<match-regex> <out-var> <input>...)
function(arcpkg_list_get_first_by_regex REGEX OUTPUT_VAR)
  set(RESULT)
  foreach(X ${ARGN})
    if(X MATCHES "${REGEX}")
      set(RESULT ${X})
      break()
    endif()
  endforeach()
  set(${OUTPUT_VAR} ${RESULT} PARENT_SCOPE)
endfunction(arcpkg_list_get_first_by_regex)


# Retrieve first regex group.
# Usage: arcpkg_retrieve_regex_group({MATCH | MATCHALL} <match-regex> <out-var> <input>...)
function(arcpkg_retrieve_regex_group MODE REGEX OUTPUT_VAR)
  string(REGEX ${MODE} "${REGEX}" OUTPUT "${ARGN}")
  set(MATCH_GROUPS)
  foreach(OUT ${OUTPUT})
    string(REGEX MATCH "${REGEX}" _ "${OUT}")
    if(CMAKE_MATCH_COUNT LESS 1)
      arcpkg_error_i("No regex group captured with regex (${REGEX}) with input (${OUT})")
    endif()
    list(APPEND MATCH_GROUPS ${CMAKE_MATCH_1})
  endforeach()
  set(${OUTPUT_VAR} ${MATCH_GROUPS} PARENT_SCOPE)
endfunction()


# Get filename without last extension.
# e.g. a/b/c.e.f -> a/b/c.e
function(arcpkg_get_filename_wle FILENAME_WE_VAR PATH)
  get_filename_component(DIR ${PATH} DIRECTORY)
  get_filename_component(NAME ${PATH} NAME)
  string(REGEX REPLACE "\\.[^.]+" "" NAME "${NAME}")
  set(${FILENAME_WE_VAR} "${DIR}/${NAME}" PARENT_SCOPE)
endfunction(arcpkg_get_filename_wle)


# Parse checksum from JSON.
# Usage: arcpkg_parse_checksum_from_json(<out-var> <format> <input>...)
# <format>: sha256 or md5
function(arcpkg_parse_checksum_from_json OUTPUT_VAR FORMAT)
  arcpkg_retrieve_regex_group(MATCH "\"${FORMAT}\" : \"([^\"]+)\"" OUTPUT "${ARGN}")
  set(${OUTPUT_VAR} ${OUTPUT} PARENT_SCOPE)
endfunction()


# Compute checksum.
# Usage: arcpkg_checksum(<out-var> <format> <path>)
function(arcpkg_checksum OUTPUT_VAR FORMAT PATH)
  execute_process(
    COMMAND ${CMAKE_COMMAND} -E ${FORMAT}sum "${PATH}"
    RESULT_VARIABLE RET
    OUTPUT_VARIABLE CHECKSUM
  )
  if(NOT RET EQUAL 0)
    arcpkg_error_i("Fail to compute ${FORMAT} for ${PATH}")
  endif()
  string(REGEX MATCH "^[^ ]+" CHECKSUM ${CHECKSUM})
  # message("${FORMAT}: ${RET} ${CHECKSUM}")
  set(${OUTPUT_VAR} ${CHECKSUM} PARENT_SCOPE)
endfunction(arcpkg_checksum)


# Run python code
macro(arcpkg_py_run CODE)
  # HACK: macro() expand the bracket argument ${CODE}
  # message("CODE: ${CODE}")
  if(NOT Python_EXECUTABLE)
    arcpkg_error("No python executable was found in system!")
  endif()
  cmake_parse_arguments(A ""
    "ERROR_VARIABLE;OUTPUT_VARIABLE;RESULT_VARIABLE"
    ""
    ${ARGN}
  )
  string(REPLACE "\n" "\\n" CODE_FORMATTED "${CODE}")
  set(EXECUTE_PROCESS_ARGS)
  if(A_OUTPUT_VARIABLE)
    list(APPEND EXECUTE_PROCESS_ARGS OUTPUT_VARIABLE ${A_OUTPUT_VARIABLE})
  endif()
  execute_process(
    COMMAND "${Python_EXECUTABLE}" -c "exec(\"${CODE_FORMATTED}\")"
    ERROR_VARIABLE ERROR
    RESULT_VARIABLE RET
    ${EXECUTE_PROCESS_ARGS}
  )
  if(NOT RET EQUAL 0 AND ERROR STREQUAL "")
    set(ERROR "RET: ${RET}")
  else()
    string(REPLACE "\\n" "\n" ERROR "${ERROR}")
  endif()
  if(A_ERROR_VARIABLE)
    set(${A_ERROR_VARIABLE} ${ERROR} PARENT_SCOPE)
  endif()
  if(A_OUTPUT_VARIABLE)
    set(${A_OUTPUT_VARIABLE} ${${A_OUTPUT_VARIABLE}} PARENT_SCOPE)
  endif()
  if(A_RESULT_VARIABLE)
    set(${A_RESULT_VARIABLE} ${RET} PARENT_SCOPE)
  endif()
  if(NOT A_RESULT_VARIABLE AND NOT A_ERROR_VARIABLE AND NOT RET EQUAL 0)
    # mask secret messages
    string(REPLACE "${ARTIFACTORY_USER}" "*******" CODE_MASKED "${CODE}")
    string(REPLACE "${ARTIFACTORY_PSWD}" "*******" CODE_MASKED "${CODE_MASKED}")
    arcpkg_warn("CODE:\n${CODE_MASKED}\nOUTPUT: ${${A_OUTPUT_VARIABLE}}\nERROR: ${ERROR}")
    arcpkg_error_i("RET: ${RET}")
  endif()
endmacro(arcpkg_py_run)


# arcpkg_py_zip_dir(<zip-path> <data-dir>)
function(arcpkg_py_zip_dir ZIP_PATH DATA_DIR)
  set(CODE [=[
from __future__ import print_function
import os
import zipfile

def zipdir(ziph, data_dir):
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            path = os.path.join(root, file)
            print(path)
            ziph.write(path)

zip_path = r'${ZIP_PATH}'
data_dir = r'${DATA_DIR}'
with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
    zipdir(zipf, data_dir)
]=])
  arcpkg_py_run("${CODE}" ${ARGN})
endfunction(arcpkg_py_zip_dir)


# Escape special characters in url.
function(arcpkg_urlparse_quote URL_VAR URL)
  string(REPLACE "#" "%23" URL "${URL}")
  set(${URL_VAR} ${URL} PARENT_SCOPE)
endfunction(arcpkg_urlparse_quote)


# arcpkg_report_http_error(<error-code> <url> <error-msg> <ignored-errors>)
function(arcpkg_report_http_error ERROR_CODE URL ERROR IGNORE_ERRORS)
  # message("ERROR: ${ERROR}")
  # message("ERROR_CODE: ${ERROR_CODE}")
  foreach(IGNORE_ERROR ${IGNORE_ERRORS})
    if(IGNORE_ERROR MATCHES "[0-9]+")
      if(ERROR_CODE MATCHES "^${IGNORE_ERROR}$")
        return()
      endif()
    elseif(ERROR_CODE MATCHES "${IGNORE_ERROR}")
      return()
    endif()
  endforeach()
  if(ERROR_CODE STREQUAL "403")
    arcpkg_error("User ${ARTIFACTORY_USER} is forbidden as no permission to access ${URL}\nERROR: ${ERROR}")
  elseif(ERROR_CODE STREQUAL "401")
    arcpkg_error("The password of user ${ARTIFACTORY_USER} is wrong: ${ARTIFACTORY_PSWD}\nERROR: ${ERROR}")
  else()
    arcpkg_error("Error happened when access ${URL}\nERROR_CODE: ${ERROR_CODE}\nERROR: ${ERROR}")
  endif()
endfunction(arcpkg_report_http_error)


# arcpkg_py_http_request(<url>)
function(arcpkg_py_http_request URL)
  cmake_parse_arguments(A ""
    "METHOD;DATA;DATA_PATH;ERROR_VARIABLE;ERROR_CODE_VARIABLE;OUTPUT_VARIABLE"
    "IGNORE_ERRORS"
    ${ARGN}
  )
  if(NOT A_METHOD)
    set(A_METHOD "GET")
  endif()
  if(A_DATA)
    string(REPLACE "\"" "\\\"" A_DATA "${A_DATA}")
  endif()
  set(CODE [=[
from __future__ import print_function
from base64 import b64encode
import sys
import ssl
import hashlib
try:
    ssl._create_default_https_context = ssl._create_unverified_context
except AttributeError:
    pass # Legacy Python that doesn't verify HTTPS certificates by default
if sys.version_info[0] == 3:
    from urllib.request import urlopen, Request
    from urllib.error import URLError
    from urllib.parse import quote as url_quote
else:
    from urllib2 import urlopen, Request, URLError
    from urllib import quote as url_quote

url = r'${URL}'
url = url_quote(url, safe=':/=?&*')
ARTIFACTORY_USER = '${ARTIFACTORY_USER}'
ARTIFACTORY_PSWD = '${ARTIFACTORY_PSWD}'
user_pass = b64encode('{}:{}'.format(ARTIFACTORY_USER, ARTIFACTORY_PSWD).encode('ascii')).decode('ascii')
headers = { 'Authorization': 'Basic ' + user_pass }
data_path = r'${A_DATA_PATH}'
data = r'''${A_DATA}'''
if data_path:
    data = open(data_path, 'rb').read()
    headers['X-Checksum'] = '${MD5}' or hashlib.md5(data).hexdigest()
    headers['X-Checksum-Sha1'] = '${SHA1}' or hashlib.sha1(data).hexdigest()
elif len(data) == 0:
    data = None
else:
    data = data.encode('utf-8')
    headers['Content-Type'] = 'text/plain'
if sys.version_info[0] == 3:
    req = Request(url, data, headers=headers, method='${A_METHOD}')
else:
    req = Request(url, data, headers=headers)
    req.get_method = lambda: '${A_METHOD}'
try:
    response = urlopen(req)
    print(response.read().decode('utf-8'))
    # print(response.status)
except URLError as e:
    sys.stderr.write(str(e))
]=])
  arcpkg_py_run("${CODE}" ${A_UNPARSED_ARGUMENTS} OUTPUT_VARIABLE OUTPUT ERROR_VARIABLE ERROR)
  if(A_OUTPUT_VARIABLE)
    set(${A_OUTPUT_VARIABLE} ${OUTPUT} PARENT_SCOPE)
  endif()
  if(A_ERROR_VARIABLE)
    set(${A_ERROR_VARIABLE} ${ERROR} PARENT_SCOPE)
  endif()
  if(ERROR)
    if(ERROR MATCHES "^HTTP Error ([0-9][0-9][0-9])")
      set(ERROR_CODE ${CMAKE_MATCH_1})
    else()
      set(ERROR_CODE ${ERROR})
    endif()
    arcpkg_report_http_error("${ERROR_CODE}" ${URL} "${ERROR}\n${OUTPUT}" "${A_IGNORE_ERRORS}")
  else()
    set(ERROR_CODE)
  endif()
  if(A_ERROR_CODE_VARIABLE)
    set(${A_ERROR_CODE_VARIABLE} ${ERROR_CODE} PARENT_SCOPE)
  endif()
endfunction(arcpkg_py_http_request)


# Usage: arcpkg_http_request(<output-var> <url>)
function(arcpkg_http_request OUTPUT_VAR URL)
  cmake_parse_arguments(A ""
    "ERROR_VARIABLE;ERROR_CODE_VARIABLE"
    "IGNORE_ERRORS"
    ${ARGN}
  )
  arcpkg_check_remote_variables()
  if(Python_EXECUTABLE)
    # use RESULT_VARIABLE to ignore error
    arcpkg_py_http_request("${URL}"
      OUTPUT_VARIABLE OUTPUT
      ERROR_VARIABLE ERROR
      ERROR_CODE_VARIABLE ERROR_CODE
      RESULT_VARIABLE RET
      IGNORE_ERRORS "${A_IGNORE_ERRORS}")
  else()
    set(LOCAL_PATH "${ARCPKG_ROOT}/http_request.txt")
    file(WRITE "${LOCAL_PATH}" "") # empty
    file(LOCK "${LOCAL_PATH}.lock")
    arcpkg_urlparse_quote(URL "${URL}")
    file(DOWNLOAD "${URL}" "${LOCAL_PATH}"
      USERPWD "${ARTIFACTORY_USER}:${ARTIFACTORY_PSWD}"
      STATUS DOWNLOAD_STATUS
      LOG DOWNLOAD_LOG
    )
    file(READ "${LOCAL_PATH}" OUTPUT)
    file(LOCK "${LOCAL_PATH}.lock" RELEASE)
    if(DOWNLOAD_STATUS EQUAL 22 AND DOWNLOAD_LOG MATCHES "HTTP/[0-9.]+ ([0-9][0-9][0-9])")
      set(ERROR_CODE ${CMAKE_MATCH_1})
    elseif(NOT DOWNLOAD_STATUS EQUAL 0)
      set(ERROR_CODE "${DOWNLOAD_STATUS}")
    else()
      set(ERROR_CODE)
    endif()
    if(NOT DOWNLOAD_STATUS EQUAL 0)
      arcpkg_report_http_error("${ERROR_CODE}" ${URL} "DOWNLOAD_STATUS: ${DOWNLOAD_STATUS}\nDOWNLOAD_LOG: ${DOWNLOAD_LOG}" "${A_IGNORE_ERRORS}")
    endif()
    set(ERROR ${DOWNLOAD_STATUS} ${DOWNLOAD_LOG})
  endif()
  set(${OUTPUT_VAR} "${OUTPUT}" PARENT_SCOPE)
  if(A_ERROR_VARIABLE)
    set(${A_ERROR_VARIABLE} ${ERROR} PARENT_SCOPE)
  endif()
  if(A_ERROR_CODE_VARIABLE)
    set(${A_ERROR_CODE_VARIABLE} ${ERROR_CODE} PARENT_SCOPE)
  endif()
endfunction(arcpkg_http_request)


# File info.
# For virtual use the virtual repository returns the resolved file. Supported by local, local-cached and virtual repositories.
# https://www.jfrog.com/confluence/display/JFROG/Artifactory+REST+API#ArtifactoryRESTAPI-FileInfo
# Usage: arcpkg_remote_file_info(<output-var> <remote-path>)
function(arcpkg_remote_file_info OUTPUT_VAR REMOTE_PATH)
  arcpkg_http_request(OUTPUT "${ARTIFACTORY_URL}/api/storage/${ARTIFACTORY_REPO}/${REMOTE_PATH}" IGNORE_ERRORS "404")
  set(${OUTPUT_VAR} "${OUTPUT}" PARENT_SCOPE)
endfunction(arcpkg_remote_file_info)


# Get pkg versions and error.
function(arcpkg_get_pkg_versions ERROR_CODE_VAR VERSIONS_VAR PKG_USER PKG_NAME)
  arcpkg_http_request(OUTPUT "${ARTIFACTORY_URL}/api/storage/${ARTIFACTORY_REPO}/${PKG_USER}/${PKG_NAME}"
    ERROR_VARIABLE ERROR
    ERROR_CODE_VARIABLE ERROR_CODE
    IGNORE_ERRORS ".*")
  if(OUTPUT)
    set(REGEX "\"uri\" *: *\"/([^\"]+)\"")
    string(REGEX MATCHALL "${REGEX}" OUTPUT "${OUTPUT}")
    string(REPLACE ".zip\"" "\"" OUTPUT "${OUTPUT}")
    string(REPLACE "\"/${PKG_NAME}-" "\"/" OUTPUT "${OUTPUT}")
    set(VERSIONS)
    foreach(OUT ${OUTPUT})
      if(OUT MATCHES "${REGEX}")
        list(APPEND VERSIONS ${CMAKE_MATCH_1})
      endif()
    endforeach()
  else()
    set(VERSIONS)
  endif()
  set(${ERROR_CODE_VAR} "${ERROR_CODE}" PARENT_SCOPE)
  set(${VERSIONS_VAR} "${VERSIONS}" PARENT_SCOPE)
endfunction(arcpkg_get_pkg_versions)


# Get all artifacts matching the given Ant path pattern.
# https://www.jfrog.com/confluence/display/JFROG/Artifactory+REST+API#ArtifactoryRESTAPI-PatternSearch
function(arcpkg_remote_pattern_search OUTPUT_VAR PATTERN)
  arcpkg_http_request(OUTPUT "${ARTIFACTORY_URL}/api/search/pattern?pattern=${ARTIFACTORY_REPO}:${PATTERN}" IGNORE_ERRORS "404")
  set(${OUTPUT_VAR} "${OUTPUT}" PARENT_SCOPE)
endfunction(arcpkg_remote_pattern_search)


# Flexible and high performance search using Artifactory Query Language (AQL).
# https://www.jfrog.com/confluence/display/JFROG/Artifactory+REST+API#ArtifactoryRESTAPI-ArtifactoryQueryLanguage(AQL)
function(arcpkg_jfrog_artifactory_aql_ OUTPUT_VAR QUERY_CODE)
  # message("QUERY_CODE: ${QUERY_CODE}")
  set(URL "${ARTIFACTORY_URL}/api/search/aql")
  arcpkg_py_http_request("${URL}" METHOD "POST" DATA "${QUERY_CODE}" OUTPUT_VARIABLE OUTPUT RESULT_VARIABLE RET IGNORE_ERRORS "404;502;^<urlopen")
  set(${OUTPUT_VAR} "${OUTPUT}" PARENT_SCOPE)
endfunction(arcpkg_jfrog_artifactory_aql_)

macro(arcpkg_jfrog_artifactory_aql OUTPUT_VAR QUERY_CODE)
  # HACK: macro() expand the bracket argument ${QUERY_CODE}
  arcpkg_check_remote_variables()
  arcpkg_jfrog_artifactory_aql_(${OUTPUT_VAR} ${QUERY_CODE})
endmacro(arcpkg_jfrog_artifactory_aql)

# Upload file.
# Usage: arcpkg_upload_file(<local-path> <remote-path>)
function(arcpkg_upload_file LOCAL_PATH REMOTE_PATH)
  arcpkg_check_remote_variables()
  # checksum
  arcpkg_checksum(SHA256 sha256 "${LOCAL_PATH}")
  arcpkg_remote_file_info(FILE_INFO_JSON "${REMOTE_PATH}")
  if(FILE_INFO_JSON)
    arcpkg_parse_checksum_from_json(REMOTE_SHA256 sha256 ${FILE_INFO_JSON})
    if(REMOTE_SHA256 STREQUAL SHA256)
      arcpkg_echo("No upload as same sha256 of ${LOCAL_PATH} and remote ${REMOTE_PATH}")
      return()
    endif()
  endif()
  # https://www.jfrog.com/confluence/display/JFROG/Artifactory+REST+API#ArtifactoryRESTAPI-DeployArtifactbyChecksum
  set(URL "${ARTIFACTORY_URL}/${ARTIFACTORY_REPO}/${REMOTE_PATH}")
  if(Python_EXECUTABLE)
    # use OUTPUT_VARIABLE to ignore output
    file(TO_CMAKE_PATH "${LOCAL_PATH}" LOCAL_PATH)
    arcpkg_py_http_request("${URL}" DATA_PATH "${LOCAL_PATH}" METHOD "PUT" OUTPUT_VARIABLE OUTPUT)
  else()
    arcpkg_checksum(MD5 md5 "${LOCAL_PATH}")
    arcpkg_urlparse_quote(URL "${URL}")
    file(UPLOAD "${LOCAL_PATH}" "${URL}"
      USERPWD "${ARTIFACTORY_USER}:${ARTIFACTORY_PSWD}"
      HTTPHEADER "X-Checksum:${MD5}"
      HTTPHEADER "X-Checksum-Sha256:${SHA256}"
      STATUS UPLOAD_STATUS
      LOG UPLOAD_LOG
    )
    if(NOT UPLOAD_STATUS EQUAL 0)
      arcpkg_error_i("UPLOAD_STATUS: ${UPLOAD_STATUS}\n" "UPLOAD_LOG: ${UPLOAD_LOG}")
    endif()
  endif()
  arcpkg_echo("Uploaded ${LOCAL_PATH} to ${URL}")
endfunction(arcpkg_upload_file)


# Download file.
# Usage: arcpkg_download_file(<remote-path> <local-path>)
function(arcpkg_download_file REMOTE_PATH LOCAL_PATH)
  arcpkg_check_remote_variables()
  arcpkg_echo("  Downloading remote ${REMOTE_PATH} to ${LOCAL_PATH}")
  # https://www.jfrog.com/confluence/display/JFROG/Artifactory+REST+API#ArtifactoryRESTAPI-RetrieveArtifact
  set(URL "${ARTIFACTORY_URL}/${ARTIFACTORY_REPO}/${REMOTE_PATH}")
  arcpkg_urlparse_quote(URL "${URL}")
  file(DOWNLOAD "${URL}" "${LOCAL_PATH}"
    USERPWD "${ARTIFACTORY_USER}:${ARTIFACTORY_PSWD}"
    STATUS DOWNLOAD_STATUS
    LOG DOWNLOAD_LOG
  )
  if(NOT DOWNLOAD_STATUS EQUAL 0)
    arcpkg_error_i("DOWNLOAD_STATUS: ${DOWNLOAD_STATUS}\n" "DOWNLOAD_LOG: ${DOWNLOAD_LOG}")
  endif()
endfunction(arcpkg_download_file)


macro(arcpkg_checksum_ SOURCE)
  set(CHECKSUM_UNMATCHED 1)
  set(CHECKSUM_PATH "${INSTALL_DIR_ABS}.ok")
  if(EXISTS "${CHECKSUM_PATH}" AND EXISTS "${INSTALL_DIR_ABS}")
    file(READ "${CHECKSUM_PATH}" LOCAL_CHECKSUM)
    if(LOCAL_CHECKSUM STREQUAL CHECKSUM)
      arcpkg_echo("  Matched sha256 and no update for local cache ${INSTALL_DIR_ABS}")
      set(CHECKSUM_UNMATCHED 0)
    else()
      arcpkg_warn("  sha256 (${CHECKSUM}) of ${SOURCE}")
      arcpkg_warn("  sha256 (${LOCAL_CHECKSUM}) of local cache ${INSTALL_DIR_ABS}")
      arcpkg_warn("  Unmatched sha256 between ${SOURCE} and local cache ${INSTALL_DIR_ABS}, re-install it.")
    endif()
  endif()
endmacro(arcpkg_checksum_)


macro(arcpkg_unzip_)
  # remove old install
  file(REMOVE "${CHECKSUM_PATH}")
  file(REMOVE_RECURSE "${INSTALL_DIR_ABS}")
  # unzip
  arcpkg_echo("  Unzipping to ${INSTALL_DIR_ABS}")
  file(MAKE_DIRECTORY "${INSTALL_DIR_ABS}")
  execute_process(
    COMMAND ${CMAKE_COMMAND} -E tar zxf "${ZIP_PATH}"
    WORKING_DIRECTORY ${INSTALL_DIR_ABS}
    RESULT_VARIABLE UNZIP_RET
    OUTPUT_VARIABLE OUTPUT
    ERROR_VARIABLE ERROR_OUTPUT)
  if(OUTPUT)
    message("${OUTPUT}")
  endif()
  if(ERROR_OUTPUT)
    message("${ERROR_OUTPUT}")
    if(CMAKE_CROSSCOMPILING AND CMAKE_HOST_WIN32)
      string(REPLACE "\n" ";" LINES "${ERROR_OUTPUT}")
      set(CREATE_LINK_SCRIPT_CONTENT "")
      foreach(LINE ${LINES})
        if(LINE MATCHES "^cmake -E tar: warning: skipping symbolic link \"(.+)\" -> \"(.+)\"\\.$")
          # e.g. cmake -E tar: warning: skipping symbolic link "lib/libswscale.so" -> "libswscale.so.8.1.100".
          get_filename_component(subdir "${CMAKE_MATCH_1}" DIRECTORY)
          set(link_target "${INSTALL_DIR_ABS}/${CMAKE_MATCH_1}")
          set(link_source "${INSTALL_DIR_ABS}/${subdir}/${CMAKE_MATCH_2}")
          unset(subdir)
          string(APPEND CREATE_LINK_SCRIPT_CONTENT
            "execute_process(COMMAND \"\${CMAKE_COMMAND}\" -E create_hardlink \"${link_source}\" \"${link_target}\")\n"
            "message(\"  Create hard link: ${link_target} -> ${link_source}\")\n"
          )
        endif()
      endforeach()

      # if length of CREATE_LINK_SCRIPT_CONTENT is not 0, then create a script to create hard links
      if(CREATE_LINK_SCRIPT_CONTENT)
        # write script to create hard links
        set(CREATE_LINK_SCRIPT_PATH "${INSTALL_DIR_ABS}/create_hark_links.cmake")
        file(WRITE "${CREATE_LINK_SCRIPT_PATH}" "${CREATE_LINK_SCRIPT_CONTENT}")
        arcpkg_echo("  Creating hard links with ${CREATE_LINK_SCRIPT_PATH}")
        execute_process(COMMAND ${CMAKE_COMMAND} -P "${CREATE_LINK_SCRIPT_PATH}")
        unset(CREATE_LINK_SCRIPT_CONTENT)
      endif()
    endif()

  endif()
  if(NOT UNZIP_RET EQUAL 0)
    arcpkg_warn("  Unzipping failed, try to remove old unzip directory (${INSTALL_DIR_ABS})!")
    file(REMOVE_RECURSE ${INSTALL_DIR_ABS})
  endif()
  # change modification time of include files
  file(GLOB_RECURSE ALL_FILES "${INSTALL_DIR_ABS}/*")
  if(ALL_FILES)
    arcpkg_echo("  Updating modification time of all installed files")
    execute_process(COMMAND ${CMAKE_COMMAND} -E touch ${ALL_FILES})
  endif()
  # touch checksum flag file
  file(WRITE "${CHECKSUM_PATH}" "${CHECKSUM}")
endmacro(arcpkg_unzip_)


# Install local ZIP file to local cache.
# Usage: arcpkg_install_local_zip(<from-path> <cache-path>)
function(arcpkg_install_local_zip ZIP_PATH INSTALL_DIR)
  set(INSTALL_DIR_ABS "${ARCPKG_ROOT}/${INSTALL_DIR}")
  file(LOCK "${INSTALL_DIR_ABS}.lock")
  # check sha256
  arcpkg_checksum(CHECKSUM sha256 "${ZIP_PATH}")
  arcpkg_checksum_(${ZIP_PATH})
  # install
  if(CHECKSUM_UNMATCHED)
    arcpkg_echo("  Installing ${ZIP_PATH}")
    arcpkg_unzip_()
  endif()
  file(LOCK "${INSTALL_DIR_ABS}.lock" RELEASE)
endfunction(arcpkg_install_local_zip)


# Install remote zip file to local cache.
# Usage: arcpkg_install_remote_zip(<from-path> <cache-path>)
function(arcpkg_install_remote_zip REMOTE_PATH INSTALL_DIR)
  if(NOT INSTALL_DIR)
    arcpkg_get_filename_wle(INSTALL_DIR "${REMOTE_PATH}")
  endif()
  set(INSTALL_DIR_ABS "${ARCPKG_ROOT}/${INSTALL_DIR}")
  file(LOCK "${INSTALL_DIR_ABS}.lock")
  # check sha256
  arcpkg_remote_file_info(FILE_INFO_JSON "${REMOTE_PATH}")
  if(NOT FILE_INFO_JSON)
    arcpkg_error("Fail to find ${REMOTE_PATH} in remote!")
  endif()
  arcpkg_parse_checksum_from_json(CHECKSUM sha256 ${FILE_INFO_JSON})
  arcpkg_checksum_("remote ${REMOTE_PATH}")
  # download and install
  if(CHECKSUM_UNMATCHED)
    arcpkg_echo("  Installing remote ${REMOTE_PATH}")
    set(ZIP_PATH "${INSTALL_DIR_ABS}.zip")
    arcpkg_download_file(${REMOTE_PATH} ${ZIP_PATH})
    arcpkg_unzip_()
  endif()
  file(LOCK "${INSTALL_DIR_ABS}.lock" RELEASE)
endfunction(arcpkg_install_remote_zip)


# Generate arcpkg.txt.
function(arcpkg_generate_info_file TXT_PATH PKG PLATFORM_DEPENDENT)
  set(CONTENT "Pkg Version: ${ARCPKG_VERSION}")
  set(KEYS
    "VCS Version" "Name" "Version" "User" "Channel" "Hints"
    "Authors" "Maintainers"
    "Summary" "Description" "URL" "HomePage" "Copyright" "License"
    "LIBS" "BINS" "Requires" "Map Files" "Install Dir"
  )
  foreach(K ${KEYS})
    string(REPLACE " " "_" KK "${K}")
    string(TOUPPER "${KK}" KK)
    if(${PKG}_${KK})
      set(CONTENT "${CONTENT}\n${K}: ${${PKG}_${KK}}")
    endif()
  endforeach()
  set(CONTENT "${CONTENT}\nCMake Version: ${CMAKE_VERSION}")
  if(PLATFORM_DEPENDENT)
    set(CONTENT "${CONTENT}\nHost System: ${CMAKE_HOST_SYSTEM_NAME} ${CMAKE_HOST_SYSTEM_VERSION} ${CMAKE_HOST_SYSTEM_PROCESSOR}")
    set(CONTENT "${CONTENT}\nTarget System: ${CMAKE_SYSTEM_NAME} ${CMAKE_SYSTEM_VERSION} ${CMAKE_SYSTEM_PROCESSOR}")
    set(CONTENT "${CONTENT}\nPlatform: ${${PKG}_PLATFORM}")
    set(CONTENT "${CONTENT}\nArch: ${${PKG}_ARCH}")
    set(CONTENT "${CONTENT}\nC Compiler: ${CMAKE_C_COMPILER} (ver ${CMAKE_C_COMPILER_VERSION})")
    set(CONTENT "${CONTENT}\nC Flags: ${CMAKE_C_FLAGS}")
    set(CONTENT "${CONTENT}\nC Flags (Debug): ${CMAKE_C_FLAGS_DEBUG}")
    set(CONTENT "${CONTENT}\nC Flags (Release): ${CMAKE_C_FLAGS_RELEASE}")
    set(CONTENT "${CONTENT}\nC++ Compiler: ${CMAKE_CXX_COMPILER} (ver ${CMAKE_CXX_COMPILER_VERSION})")
    set(CONTENT "${CONTENT}\nC++ Flags: ${CMAKE_CXX_FLAGS}")
    set(CONTENT "${CONTENT}\nC++ Flags (Debug): ${CMAKE_CXX_FLAGS_DEBUG}")
    set(CONTENT "${CONTENT}\nC++ Flags (Release): ${CMAKE_CXX_FLAGS_RELEASE}")
  endif()
  arcpkg_debug("Write ${TXT_PATH}")
  file(GENERATE OUTPUT "${TXT_PATH}" CONTENT "${CONTENT}")
endfunction(arcpkg_generate_info_file)


# Parse properties in arcpkg.txt
function(arcpkg_parse_info_file TXT_PATH PKG)
  file(STRINGS "${TXT_PATH}" LINES)
  set(LINE_NO 1)
  foreach(LINE ${LINES})
    string(REGEX REPLACE "^#.*$" "" LINE "${LINE}") # remove whole-line comments
    string(STRIP "${LINE}" LINE) # remove leading and trailing spaces
    if(LINE STREQUAL "" OR LINE MATCHES "^#") # ignore empty line or line startswith '#'
      continue()
    endif()
    if(LINE MATCHES "^([^:]+) *: *(.*)$")
      set(ITEM_NAME ${CMAKE_MATCH_1})
      set(ITEM_VALUE ${CMAKE_MATCH_2})
      string(STRIP "${ITEM_NAME}" ITEM_NAME)
      string(REPLACE "C++" "CXX" ITEM_NAME "${ITEM_NAME}")
      string(REPLACE "(" "" ITEM_NAME "${ITEM_NAME}")
      string(REPLACE ")" "" ITEM_NAME "${ITEM_NAME}")
      string(REPLACE " " "_" ITEM_NAME "${ITEM_NAME}")
      string(TOUPPER "${ITEM_NAME}" ITEM_NAME)
      if(ITEM_NAME MATCHES "^CMAKE_(C|CXX)_FLAGS")
        string(REGEX REPLACE "^CMAKE_" "" ITEM_NAME "${ITEM_NAME}")
      endif()
      set(${PKG}_${ITEM_NAME} "${ITEM_VALUE}" PARENT_SCOPE)
      # message("arcpkg.txt - ${PKG}_${ITEM_NAME}: ${ITEM_VALUE}")
    else()
      arcpkg_error("Invalid format in ${TXT_PATH}:${LINE_NO}, content: ${LINE}")
    endif()
    math(EXPR LINE_NO "${LINE_NO}+1")
  endforeach()
  # For compatibility.
  list(APPEND ${PKG}_HINTS ${${PKG}_TYPE}) # For old pkg
  list(APPEND ${PKG}_AUTHORS ${${PKG}_AUTHOR}) # For old pkg
  list(APPEND ${PKG}_MAINTAINERS ${${PKG}_MAINTAINER}) # For old pkg
  set(${PKG}_HINTS "${${PKG}_HINTS}" PARENT_SCOPE)
  set(${PKG}_AUTHORS "${${PKG}_AUTHORS}" PARENT_SCOPE)
  set(${PKG}_MAINTAINERS "${${PKG}_AUTHORS}" PARENT_SCOPE)
  unset(${PKG}_AUTHOR PARENT_SCOPE)
  unset(${PKG}_MAINTAINER PARENT_SCOPE)
  if(${PKG}_PKGVERSION)
    set(${PKG}_PKG_VERSION "${${PKG}_PKGVERSION}" PARENT_SCOPE)
    unset(${PKG}_PKGVERSION PARENT_SCOPE)
  endif()
endfunction(arcpkg_parse_info_file)


# <AUTHOR> <EMAIL>[:<hint>]..
# Ignore <channel> as it's not necessary for dependency check.
# https://docs.conan.io/en/latest/reference/conanfile/attributes.html
# https://docs.conan.io/en/latest/faq/using.html#faq-recommendation-user-channel
# The <user> term in is basically a namespace to avoid collisions of libraries
# with the same name and version in the local cache and in the same remote.
# This field is usually populated with the author's name of the package recipe
# (which could be different from the author of the library itself)
# or with the name of the organization creating it.
function(arcpkg_make_recipe PKG_RECIPE_VAR PKG)
  set(PKG_RECIPE "${${PKG}_NAME}/${${PKG}_VERSION}")
  if(${PKG}_USER)
    set(PKG_RECIPE "${PKG_RECIPE}@${${PKG}_USER}")
  endif()
  # if(${PKG}_CHANNEL)
  #   set(PKG_RECIPE "${PKG_RECIPE}/${${PKG}_CHANNEL}")
  # endif()
  if(${PKG}_DISTRO)
    set(PKG_RECIPE "${PKG_RECIPE}:distro")
  endif()
  if(${PKG}_HINTS)
    string(JOIN ":" PKG_RECIPE ${PKG_RECIPE} ${${PKG}_HINTS})
  endif()
  set(${PKG_RECIPE_VAR} "${PKG_RECIPE}" PARENT_SCOPE)
endfunction(arcpkg_make_recipe)


# <AUTHOR> <EMAIL>
function(arcpkg_make_lite_recipe PKG_RECIPE_VAR PKG)
  set(PKG_RECIPE "${${PKG}_NAME}/${${PKG}_VERSION}@${${PKG}_USER}")
  set(${PKG_RECIPE_VAR} ${PKG_RECIPE} PARENT_SCOPE)
endfunction(arcpkg_make_lite_recipe)


# Parse package recipe
# <name>/<version>
# <name>/<version>@<user>
# <name>/<version>@<user>[:<hints>]
# <hints>: static|shared|distro|host|bitcode|mt
#   - ${PKG}_HINTS
#   - ${PKG}_DISTRO
#   - ${PKG}_HOST
function(arcpkg_parse_recipe PKG RECIPE)
  if(RECIPE MATCHES "^([^:]+):(.+)$")
    set(RECIPE ${CMAKE_MATCH_1})
    string(REPLACE ":" ";" PKG_HINTS "${CMAKE_MATCH_2}")
  else()
    set(PKG_HINTS)
  endif()
  # distro
  if("distro" IN_LIST PKG_HINTS)
    set(${PKG}_DISTRO ON  PARENT_SCOPE)
  else()
    set(${PKG}_DISTRO OFF PARENT_SCOPE)
  endif()
  # host
  if("host" IN_LIST PKG_HINTS)
    set(${PKG}_HOST ON  PARENT_SCOPE)
  else()
    set(${PKG}_HOST OFF PARENT_SCOPE)
  endif()
  # message("RECIPE: ${RECIPE}")
  # message("RECIPE_HINTS: ${RECIPE_HINTS}")
  list(REMOVE_ITEM PKG_HINTS "distro" "host")
  set(${PKG}_HINTS ${PKG_HINTS} PARENT_SCOPE)
  # main part
  set(${PKG}_USER ${ARCPKG_DEFAULT_USER} PARENT_SCOPE)
  set(${PKG}_CHANNEL ${ARCPKG_DEFAULT_CHANNEL} PARENT_SCOPE)
  if(RECIPE MATCHES "^([^/@]+)/([^/@]+)@([^/@]+)/([^/@]+)$")
    set(${PKG}_NAME    ${CMAKE_MATCH_1} PARENT_SCOPE)
    set(${PKG}_VERSION ${CMAKE_MATCH_2} PARENT_SCOPE)
    set(${PKG}_USER    ${CMAKE_MATCH_3} PARENT_SCOPE)
    set(${PKG}_CHANNEL ${CMAKE_MATCH_4} PARENT_SCOPE)
  elseif(RECIPE MATCHES "^([^/@]+)/([^/@]+)@([^/@]+)")
    set(${PKG}_NAME    ${CMAKE_MATCH_1} PARENT_SCOPE)
    set(${PKG}_VERSION ${CMAKE_MATCH_2} PARENT_SCOPE)
    set(${PKG}_USER    ${CMAKE_MATCH_3} PARENT_SCOPE)
  elseif(RECIPE MATCHES "^([^/@]+)/([^/@]+)/([^/@]+)$")
    set(${PKG}_NAME    ${CMAKE_MATCH_1} PARENT_SCOPE)
    set(${PKG}_VERSION ${CMAKE_MATCH_2} PARENT_SCOPE)
    set(${PKG}_CHANNEL ${CMAKE_MATCH_3} PARENT_SCOPE)
  elseif(RECIPE MATCHES "^([^/@]+)/([^/@]+)$")
    set(${PKG}_NAME    ${CMAKE_MATCH_1} PARENT_SCOPE)
    set(${PKG}_VERSION ${CMAKE_MATCH_2} PARENT_SCOPE)
  else()
    arcpkg_error("Invalid package recipe (${RECIPE}), should be <name>[/<version>[@<user>]][/<channel>][:static|:shared].")
  endif()
endfunction(arcpkg_parse_recipe)


function(arcpkg_get_pkg_prefix_from_recipe PKG_VAR RECIPE)
  arcpkg_parse_recipe(PKG ${RECIPE})
  set(${PKG_VAR} "${PKG_USER}_${PKG_NAME}" PARENT_SCOPE)
endfunction(arcpkg_get_pkg_prefix_from_recipe)


# Get property target.
# Note: return `<name>-properties` for INTERFACE target when CMake<3.19.
function(arcpkg_get_property_target PROP_TARGET_VAR TARGET)
  get_target_property(ALIASED_TARGET ${TARGET} ALIASED_TARGET)
  if(ALIASED_TARGET)
    if(ALIASED_TARGET STREQUAL TARGET)
      arcpkg_error_i("The ALIASED_TARGET of ${TARGET} is itself."
                     "It may be caused by imported ${TARGET} before arcpkg_define() which calling add_library(${TARGET} ALIAS).")
    endif()
    arcpkg_get_property_target(PROP_TARGET ${ALIASED_TARGET})
  else()
    get_target_property(TARGET_TYPE ${TARGET} TYPE)
    if(CMAKE_VERSION VERSION_LESS "3.19" AND TARGET_TYPE STREQUAL "INTERFACE_LIBRARY")
      string(REPLACE "::" "-" TARGET_NAME ${TARGET})
      set(PROP_TARGET ${TARGET_NAME}-properties)
    else()
      get_target_property(ALIAS_TARGET ${TARGET} ALIASED_TARGET)
      set(PROP_TARGET ${TARGET})
    endif()
  endif()
  set(${PROP_TARGET_VAR} ${PROP_TARGET} PARENT_SCOPE)
endfunction(arcpkg_get_property_target)

# Get PKG property.
# Usage: arcpkg_get_pkg_property(<prop-var> <target> <prop>)
function(arcpkg_get_pkg_property PROP_VAR TARGET PROP)
  arcpkg_get_property_target(PROP_TARGET ${TARGET})
  # message("PROP_TARGET of ${TARGET}: ${PROP_TARGET}")
  if(TARGET ${PROP_TARGET})
    get_target_property(PROP_VAL ${PROP_TARGET} ${PROP})
  else()
    set(PROP_VAL ${PROP_VAR}-NOTFOUND)
  endif()
  set(${PROP_VAR} ${PROP_VAL} PARENT_SCOPE)
endfunction(arcpkg_get_pkg_property)


# Set PKG properties.
# Usage: arcpkg_set_pkg_properties(<target1> <target2>... PROPERTIES <prop1> <value1> <prop2> <value2> ...)
function(arcpkg_set_pkg_properties TARGET PKG)
  cmake_parse_arguments(A "" "" "PROPERTIES" ${ARGN})
  set(PROP_TARGETS)
  foreach(TARGET ${A_UNPARSED_ARGUMENTS})
    arcpkg_get_property_target(PROP_TARGET ${TARGET})
    if(NOT TARGET ${PROP_TARGET})
      # message("Creating property target ${PROP_TARGET} for ${TARGET}")
      add_custom_target(${PROP_TARGET})
      set_target_properties(${PROP_TARGET} PROPERTIES FOLDER "arcpkg")
    endif()
    list(APPEND PROP_TARGETS ${PROP_TARGET})
  endforeach()
  set_target_properties(${PROP_TARGETS} PROPERTIES ${A_PROPERTIES})
endfunction(arcpkg_set_pkg_properties)


# Set all PKG properties in ARCPKG_ATTRIBUTES.
# Usage: arcpkg_set_all_pkg_properties(<target> <pkg> <attr>...)
function(arcpkg_set_all_pkg_properties TARGET PKG)
  arcpkg_get_property_target(PROP_TARGET ${TARGET})
  if(NOT TARGET ${PROP_TARGET})
    # message("Creating property target ${PROP_TARGET} for ${TARGET}")
    add_custom_target(${PROP_TARGET})
    set_target_properties(${PROP_TARGET} PROPERTIES FOLDER "arcpkg")
  endif()
  foreach(ATTR ${ARCPKG_ATTRIBUTES} ${ARGN})
    set_property(TARGET ${PROP_TARGET} PROPERTY ARCPKG_${ATTR} "${${PKG}_${ATTR}}")
    # message("set ${PROP_TARGET} - ${PKG}_${ATTR}: ${${PKG}_${ATTR}}")
  endforeach()
endfunction(arcpkg_set_all_pkg_properties)


# Export all PKG properties as ${PKG}
macro(arcpkg_export_all_pkg_properties TARGET PKG)
  arcpkg_echo("Exporting properties for ${${PKG}_RECIPE}")
  arcpkg_get_property_target(PROP_TARGET ${TARGET})
  foreach(ATTR ${ARCPKG_ATTRIBUTES} ${ARGN})
    get_property(${PKG}_${ATTR} TARGET ${PROP_TARGET} PROPERTY ARCPKG_${ATTR})
    # message("export ${PROP_TARGET} - ${PKG}_${ATTR}: ${${PKG}_${ATTR}}")
  endforeach()
  unset(PROP_TARGET)
  unset(ATTR)
endmacro(arcpkg_export_all_pkg_properties)


# Is MT on MSVC.
function(arcpkg_is_mt IS_MT_VAR)
  set(C_FLAGS "${CMAKE_C_FLAGS} ${CMAKE_CXX_FLAGS} ${CMAKE_C_FLAGS_RELEASE} ${CMAKE_CXX_FLAGS_RELEASE}")
  string(REPLACE " " ";" C_FLAGS ${C_FLAGS})
  if("/MT" IN_LIST C_FLAGS)
    set(${IS_MT_VAR} TRUE PARENT_SCOPE)
  else()
    set(${IS_MT_VAR} FALSE PARENT_SCOPE)
  endif()
endfunction(arcpkg_is_mt)


function(arcpkg_is_bitcode IS_BITCODE_VAR)
  if(CMAKE_C_FLAGS MATCHES "-fembed-bitcode" OR CMAKE_CXX_FLAGS MATCHES "-fembed-bitcode")
    set(${IS_BITCODE_VAR} TRUE PARENT_SCOPE)
  else()
    set(${IS_BITCODE_VAR} FALSE PARENT_SCOPE)
  endif()
endfunction(arcpkg_is_bitcode)


# Get ABI parts: {<platform>, <arch>..., <distro>, bitcode, <hints>...}
function(arcpkg_get_abi_parts OUTPUT_VAR PLATFORM ARCH HINTS)
  if(ARCPKG_LINUX_DISTRO AND NOT CMAKE_CROSSCOMPILING AND "distro" IN_LIST HINTS)
    set(ABI_PARTS "${ARCPKG_LINUX_DISTRO}")
  else()
    set(ABI_PARTS "${PLATFORM}")
  endif()
  list(REMOVE_ITEM HINTS "distro")
  if(ARCH)
    list(APPEND ABI_PARTS ${ARCH})
  endif()
  list(APPEND ABI_PARTS ${HINTS})
  if(PLATFORM STREQUAL "ios")
    arcpkg_is_bitcode(IS_BITCODE)
    if(IS_BITCODE)
      list(APPEND ABI_PARTS "bitcode")
    endif()
  endif()
  if(MSVC)
    arcpkg_is_mt(IS_MT)
    if(IS_MT)
      list(APPEND ABI_PARTS "mt")
    endif()
  endif()
  set(${OUTPUT_VAR} ${ABI_PARTS} PARENT_SCOPE)
endfunction(arcpkg_get_abi_parts)


function(arcpkg_target_append_flags TARGET PROP)
  if(NOT ARGN)
    return()
  endif()
  get_target_property(PROP_VAL ${TARGET} ${PROP})
  if(NOT PROP_VAL)
    set(PROP_VAL)
  endif()
  string(REPLACE " " ";" PROP_VAL_LIST "${PROP_VAL}")
  set(EXTRA_PROP_VAL)
  foreach(F ${ARGN})
    if(F IN_LIST PROP_VAL_LIST)
      continue()
    endif()
    list(APPEND EXTRA_PROP_VAL ${F})
  endforeach()
  if(EXTRA_PROP_VAL)
    string(REPLACE ";" " " EXTRA_PROP_VAL "${EXTRA_PROP_VAL}")
    # message("${PROP} for ${TARGET}: ${PROP_VAL} ${EXTRA_PROP_VAL}")
    set_target_properties(${TARGET} PROPERTIES ${PROP} "${PROP_VAL} ${EXTRA_PROP_VAL}")
  endif()
endfunction(arcpkg_target_append_flags)


function(arcpkg_hide_symbols_set_compile_flags TARGET)
  # COMPILE_FLAGS
  if(CMAKE_C_FLAGS MATCHES "-fvisibility=hidden")
    return()
  endif()
  set(HIDDEN_C_FLAGS -fvisibility=hidden)
  arcpkg_is_bitcode(IS_BITCODE)
  if(NOT IS_BITCODE)
    # clang: error: -fdata-sections is not supported with -fembed-bitcode
    # clang: error: -ffunction-sections is not supported with -fembed-bitcode
    list(APPEND HIDDEN_C_FLAGS -fdata-sections -ffunction-sections)
  endif()
  set(HIDDEN_CXX_FLAGS -fvisibility-inlines-hidden)
  arcpkg_target_append_flags(${TARGET} COMPILE_FLAGS ${HIDDEN_C_FLAGS})
endfunction(arcpkg_hide_symbols_set_compile_flags)


function(arcpkg_hide_symbols_set_link_flags TARGET DISABLE_EXCLUDE_LIBS_ALL)
  # LINK_FLAGS
  # -Wl,--exclude-libs,ALL: hide symbols from static libraries
  if(CMAKE_C_COMPILER_ID STREQUAL "GNU")
    if(DISABLE_EXCLUDE_LIBS_ALL)
      set(LINK_FLAGS -Wl,--gc-sections -Wl,--as-needed -Wl,--strip-all -Wl,--strip-debug)
    else()
      set(LINK_FLAGS -Wl,--exclude-libs,ALL -Wl,--gc-sections -Wl,--as-needed -Wl,--strip-all -Wl,--strip-debug)
    endif()
  elseif(CMAKE_C_COMPILER_ID STREQUAL "AppleClang" OR EMSCRIPTEN)
    set(LINK_FLAGS -Wl,-s -Wl,-S)
  elseif(CMAKE_C_COMPILER_ID STREQUAL "Clang")
    # http://releases.llvm.org/2.9/docs/CommandGuide/html/llvm-ld.html
    # list(APPEND LINK_FLAGS "-Wl,-dead_strip -Wl,-s -Wl,-S")
    if(DISABLE_EXCLUDE_LIBS_ALL)
      set(LINK_FLAGS -Wl,-s -Wl,-S)
    else()
      set(LINK_FLAGS -Wl,--exclude-libs,ALL -Wl,-s -Wl,-S)
    endif()
  else()
    arcpkg_error("Unsupported compiler: ${CMAKE_C_COMPILER_ID}")
  endif()
  arcpkg_target_append_flags(${TARGET} LINK_FLAGS ${LINK_FLAGS})
endfunction(arcpkg_hide_symbols_set_link_flags)


function(arcpkg_hide_symbols_set_ld_version_script TARGET)
  # has done!
  get_target_property(LINK_VERSION_SCRIPT_FILE ${TARGET} LINK_VERSION_SCRIPT_FILE)
  if(LINK_VERSION_SCRIPT_FILE)
    return()
  endif()

  get_target_property(SOURCE_DIR ${TARGET} SOURCE_DIR)
  if(NOT SOURCE_DIR STREQUAL CMAKE_CURRENT_SOURCE_DIR)
    arcpkg_error("Please call arcpkg_hide_symbols() in the same source directory as ${TARGET} defined in ${CMAKE_CURRENT_SOURCE_DIR}!")
  endif()

  # LINK_VERSION_SCRIPT_FILE
  file(GLOB A_INCS ${ARGN})
  set(LINK_VERSION_SCRIPT_FILE "${CMAKE_CURRENT_BINARY_DIR}/${TARGET}.ver")
  arcpkg_echo("Add LD version script to ${LINK_VERSION_SCRIPT_FILE}")
  add_custom_command(OUTPUT "${LINK_VERSION_SCRIPT_FILE}"
    COMMAND ${ARCPKG_COMMAND} generate_ld_version_script "${LINK_VERSION_SCRIPT_FILE}" ${A_INCS}
    COMMENT "Generating ${LINK_VERSION_SCRIPT_FILE}"
    DEPENDS ${A_INCS} ${ARCPKG_SCRIPT}
  )
  target_sources(${TARGET} PRIVATE ${LINK_VERSION_SCRIPT_FILE})
  set_target_properties(${TARGET} PROPERTIES
    LINK_VERSION_SCRIPT_FILE "${LINK_VERSION_SCRIPT_FILE}"
  )

  # LINK_FLAGS
  arcpkg_target_append_flags(${TARGET} LINK_FLAGS "-Wl,--version-script=\"${LINK_VERSION_SCRIPT_FILE}\"")
endfunction(arcpkg_hide_symbols_set_ld_version_script)


function(arcpkg_hide_symbols_set_ld_map TARGET)
  # has done!
  get_target_property(LINK_MAP_FILE ${TARGET} LINK_MAP_FILE)
  if(LINK_MAP_FILE)
    return()
  endif()

  # LINK_MAP_FILE and LINK_FLAGS
  arcpkg_echo("Add LD map for ${TARGET}")
  if(MSVC)
    # NOTE: no map file is specified to adapt multiple configurations
    set_target_properties(${TARGET} PROPERTIES LINK_MAP_FILE ON)
    arcpkg_target_append_flags(${TARGET} LINK_FLAGS "/MAP")
  else()
    set(LINK_MAP_FILE "${CMAKE_CURRENT_BINARY_DIR}/${TARGET}.map")
    set_target_properties(${TARGET} PROPERTIES LINK_MAP_FILE "${LINK_MAP_FILE}")
    if(APPLE)
      set(LINK_MAP_PREFIX "-Wl,-map,")
    else()
      set(LINK_MAP_PREFIX "-Wl,-Map=")
    endif()
    arcpkg_target_append_flags(${TARGET} LINK_FLAGS "${LINK_MAP_PREFIX}\"${LINK_MAP_FILE}\"")
  endif()
endfunction(arcpkg_hide_symbols_set_ld_map)


function(arcpkg_strip_target_impl TARGET)
  if(NOT CMAKE_STRIP)
    arcpkg_error("Can not find strip command, stripping is disabled!")
  endif()
  if(CMAKE_STRIP MATCHES "(llvm-strip|LLVM-STRIP)")
    set(STRIP_FLAGS --strip-all)
  elseif(APPLE)
    set(STRIP_FLAGS)
  else()
    set(STRIP_FLAGS --strip-all  --discard-all)
  endif()
  # arcpkg_target_append_flags(${TARGET} LINK_FLAGS "-s") # not work
  arcpkg_echo("Strip ${TARGET} when post build")
  add_custom_command(TARGET ${TARGET} POST_BUILD
    COMMAND ${CMAKE_STRIP} ${STRIP_FLAGS} $<TARGET_FILE:${TARGET}>
    COMMENT "Stripping ${TARGET}"
    VERBATIM
  )
endfunction(arcpkg_strip_target_impl)


function(arcpkg_strip_target TARGET)
  if(MSVC)
    return()
  endif()
  get_target_property(TYPE ${TARGET} TYPE)
  if(NOT TYPE MATCHES "^(SHARED_LIBRARY|EXECUTABLE)$")
    arcpkg_warn("The function arcpkg_strip_target() only support shared library and executable")
  endif()

  arcpkg_strip_target_impl(${TARGET})
endfunction(arcpkg_strip_target)


###########################################################
# Hide symbols of shared library or executable.
# Usage: arcpkg_hide_symbols(
#   <target>
#   [INCS <inc> [inc...]]
#   [DISABLE_EXCLUDE_LIBS_ALL]  # Default is OFF
# )
function(arcpkg_hide_symbols TARGET)
  get_target_property(TYPE ${TARGET} TYPE)
  if(NOT TYPE MATCHES "^(SHARED_LIBRARY|EXECUTABLE)$")
    arcpkg_warn("The function arcpkg_hide_symbols() only support shared library and executable")
    return()
  endif()

  if(EMSCRIPTEN OR QTEE)
    return()
  endif()

  if(MSVC OR MINGW)
    arcpkg_hide_symbols_set_ld_map(${TARGET})
    return()
  endif()

  cmake_parse_arguments(A "DISABLE_EXCLUDE_LIBS_ALL" "" "INCS" ${ARGN})
  if(A_INCS)
    arcpkg_hide_symbols_set_ld_version_script(${TARGET} ${A_INCS})
    arcpkg_hide_symbols_set_link_flags(${TARGET} ${A_DISABLE_EXCLUDE_LIBS_ALL})
  else()
    arcpkg_hide_symbols_set_compile_flags(${TARGET})
    arcpkg_hide_symbols_set_link_flags(${TARGET} ${A_DISABLE_EXCLUDE_LIBS_ALL})
  endif()
  arcpkg_hide_symbols_set_ld_map(${TARGET})
  if(CMAKE_VERSION VERSION_LESS "3.15")
    arcpkg_strip_target_impl(${TARGET})
  else()
    arcpkg_echo("Strip ${TARGET} when install")
  endif()
endfunction(arcpkg_hide_symbols)


# arcpkg_is_pure_interface_library(<result> <target>)
# Returns TRUE if the target is pure interface library without any dependencies.
function(arcpkg_is_pure_interface_library RESULT_VAR TARGET)
  get_target_property(INTERFACE_LINK_LIBRARIES ${TARGET} INTERFACE_LINK_LIBRARIES)
  if(INTERFACE_LINK_LIBRARIES)
    foreach(D ${INTERFACE_LINK_LIBRARIES})
      # Handle $<LINK_ONLY:name>
      # message("D: ${D}")
      if(D MATCHES "^\\$<LINK_ONLY:([^>]+)>$")
        set(D ${CMAKE_MATCH_1})
      endif()
      if(TARGET ${D})
        get_target_property(D_TYPE ${D} TYPE)
        if(NOT D_TYPE STREQUAL "INTERFACE_LIBRARY")
          set(${RESULT_VAR} FALSE PARENT_SCOPE)
          # message("${TARGET}: ${D} is not pure interface library")
          return()
        else()
          arcpkg_is_pure_interface_library(D_IS_PURE_ILIB ${D})
          if(NOT D_IS_PURE_ILIB)
            set(${RESULT_VAR} FALSE PARENT_SCOPE)
            return()
          endif()
        endif()
      else()
        set(${RESULT_VAR} FALSE PARENT_SCOPE)
        # message("${TARGET}: ${D} is not target")
        return()
      endif()
    endforeach()
  endif()
  set(${RESULT_VAR} TRUE PARENT_SCOPE)
endfunction()


# arcpkg_get_dependencies_impl(<depends-var> <target> [STOP_PKG] [IS_MAIN_LIB])
# Append dependencies of <target> to <depends-var>.
function(arcpkg_get_dependencies_impl DEPENDS_VAR TARGET STOP_PKG IS_MAIN_LIB)
  set(DEPENDS ${${DEPENDS_VAR}})
  # get dependencies
  get_target_property(TYPE ${TARGET} TYPE)
  get_target_property(IMPORTED ${TARGET} IMPORTED)
  if(TYPE STREQUAL "INTERFACE_LIBRARY")
    set(IMPORTED TRUE) # Handle INTERFACE_LIBRARY as IMPORTED library.
  endif()
  if(IMPORTED OR STOP_PKG)
    get_target_property(LINK_LIBRARIES ${TARGET} INTERFACE_LINK_LIBRARIES)
    # message("${TARGET} INTERFACE_LINK_LIBRARIES: ${LINK_LIBRARIES}")
  else()
    get_target_property(LINK_LIBRARIES ${TARGET} LINK_LIBRARIES)
    # message("${TARGET} LINK_LIBRARIES: ${LINK_LIBRARIES}")
  endif()
  if(NOT LINK_LIBRARIES)
    set(LINK_LIBRARIES)
  endif()
  # scan dependencies
  # message("${TARGET} LINK_LIBRARIES (${STOP_PKG}): ${LINK_LIBRARIES} -> ${DEPENDS}")
  foreach(D ${LINK_LIBRARIES})
    # $<LINK_ONLY:name>
    if(D MATCHES "^\\$<LINK_ONLY:([^>]+)>$")
      set(D ${CMAKE_MATCH_1})
      set(D_LINK_ONLY 1)
    else()
      set(D_LINK_ONLY 0)
    endif()
    if(D IN_LIST DEPENDS)
      continue()
    endif()
    # message("- ${D} -> ${DEPENDS}")
    if(NOT TARGET ${D})
      if(D MATCHES "^[^\\/]+$")
        list(APPEND DEPENDS ${D}) # system library, e.g. pthread
      elseif(IS_MAIN_LIB)
        list(APPEND DEPENDS ${D}) # manually exported
      elseif(NOT (STOP_PKG AND IMPORTED))
        list(APPEND DEPENDS ${D})
      else()
        arcpkg_warn("Ignore link library of ${TARGET}: ${D}")
      endif()
    else()
      get_target_property(D_ALIASED_TARGET ${D} ALIASED_TARGET) # handle ALIASED target
      set(D0 ${D})
      if(D_ALIASED_TARGET)
        set(D ${D_ALIASED_TARGET})
      endif()
      get_target_property(D_TYPE ${D} TYPE)
      get_target_property(D_IMPORTED ${D} IMPORTED)
      if(D_LINK_ONLY AND D_TYPE STREQUAL "INTERFACE_LIBRARY")
        arcpkg_is_pure_interface_library(D_IS_PURE_ILIB ${D})
        # message("${D} is pure interface library: ${D_IS_PURE_ILIB}")
        if(D_IS_PURE_ILIB)
          arcpkg_warn("Ignore link-only interface library for ${TARGET}: ${D0}")
        else()
          arcpkg_get_dependencies_impl(DEPENDS ${D} ${STOP_PKG} FALSE)
          continue()
        endif()
      elseif(STOP_PKG AND D_IMPORTED AND D0 MATCHES "::")
        arcpkg_warn("Stop scanning of imported target with namespace for ${TARGET}: ${D0}")
        list(APPEND DEPENDS ${D0}) # Keep alias name of imported targets
        continue()
      else()
        set(APPEND_D TRUE)
        if(TARGET ${D})
          get_target_property(D_TYPE ${D} TYPE)
          if(D_TYPE STREQUAL "OBJECT_LIBRARY")
            set(APPEND_D FALSE)
          endif()
        endif()
        if(APPEND_D)
          list(APPEND DEPENDS ${D})
        endif()
      endif()
      if(STOP_PKG)
        # Stop tracking of arcpkg targets
        arcpkg_get_pkg_property(D_ARCPKG_USER ${D} ARCPKG_USER)
        if(D_ARCPKG_USER)
          continue()
        endif()
      endif()
      arcpkg_get_dependencies_impl(DEPENDS ${D} ${STOP_PKG} FALSE)
    endif()
  endforeach()
  set(${DEPENDS_VAR} ${DEPENDS} PARENT_SCOPE)
endfunction(arcpkg_get_dependencies_impl)


# Collect dependencies of targets.
# arcpkg_get_dependencies(<depends-var> [target...] [STOP_PKG])
#   STOP_PKG - scanning only INTERFACE_LINK_LIBRARIES and stop recursive scanning of arcpkg targets.
function(arcpkg_get_dependencies DEPENDS_VAR)
  cmake_parse_arguments(A "STOP_PKG" "" "" ${ARGN})
  set(DEPENDS)
  foreach(D ${A_UNPARSED_ARGUMENTS})
    if(TARGET ${D})
      arcpkg_get_dependencies_impl(DEPENDS ${D} "${A_STOP_PKG}" TRUE)
    endif()
  endforeach()
  set(${DEPENDS_VAR} ${DEPENDS} PARENT_SCOPE)
endfunction(arcpkg_get_dependencies)


# Get short name of system library.
function(arcpkg_get_system_lib_name DNAME_VAR D)
  if(UNIX AND D MATCHES "^/usr/lib/[^/]+/lib([^/]+)\\.(a|so)$")
    set(DNAME ${CMAKE_MATCH_1}) # e.g. /usr/lib/x86_64-linux-gnu/libGL.so
  elseif(APPLE AND D MATCHES "^/usr/lib/lib([^/]+)\\.(a|dylib|tbd)$")
    set(DNAME ${CMAKE_MATCH_1})
    # TODO: /Applications/xcode/Xcode13.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX11.3.sdk/System/Library/Frameworks/OpenGL.framework
  elseif(UNIX AND CMAKE_CROSSCOMPILING AND D MATCHES "/usr/lib/lib([^/]+)\\.(a|so)$")
    set(DNAME ${CMAKE_MATCH_1}) # NDK<r22
  elseif(ANDROID AND D MATCHES "/sysroot/usr/lib/[^/]+/[0-9]+/lib([^/]+)\\.(a|so)$")
    set(DNAME ${CMAKE_MATCH_1}) # e.g. <prebuilt-root>/sysroot/usr/lib/arm-linux-androideabi/21/liblog.so
  elseif(UNIX AND D MATCHES "^-l(.+)$") # -lpthread
    set(DNAME ${CMAKE_MATCH_1})
  elseif(D MATCHES "^[^\\/]+$")
    # 1. short name for system library, e.g. pthread, opengl32;
    # 2. system imported libraries, e.g. OpenGL::GL;
    set(DNAME ${D})
  else()
    arcpkg_error("Could not use absolute library path (${D}) directly in target_link_libraries().\n"
                  "- Pls link short name instead of full absolute path for system library, e.g. pthread, dl, opengl32 etc.\n"
                  "- Pls define 3rdparty prebuilt libraries as imported libraries:\n"
                  "  add_library(foo STATIC IMPORTED [GLOBAL])\n"
                  "  set_target_properties(foo PROPERTIES INTERFACE_INCLUDE_DIRECTORIES <inc-dir-of-foo>)\n"
                  "  set_target_properties(foo PROPERTIES IMPORTED_LOCATION <path-to-foo-lib>)")
  endif()
  set(${DNAME_VAR} ${DNAME} PARENT_SCOPE)
endfunction(arcpkg_get_system_lib_name)


# arcpkg_group_dependencies()
function(arcpkg_group_dependencies PKG_SYSTEM_LIBS_VAR PKG_IMPORTED_LIBS_VAR PKG_INTERFACE_LIBS_VAR PKG_BUILD_LIBS_VAR PKG_REQUIRED_IMPORTED_LIBS_VAR PKG_LIBS PKG_DEPENDS)
  set(VARS PKG_SYSTEM_LIBS PKG_IMPORTED_LIBS PKG_INTERFACE_LIBS PKG_BUILD_LIBS PKG_REQUIRED_IMPORTED_LIBS)
  # reset
  foreach(X ${VARS})
    set(${X})
  endforeach()
  # scan
  foreach(D ${PKG_DEPENDS} ${PKG_LIBS})
    if(NOT TARGET ${D})
      arcpkg_get_system_lib_name(DNAME ${D})
      list(APPEND PKG_SYSTEM_LIBS ${DNAME})
    else()
      arcpkg_get_pkg_property(D_ARCPKG_USER ${D} ARCPKG_USER)
      if(D_ARCPKG_USER)
        list(APPEND PKG_REQUIRED_IMPORTED_LIBS ${D})
      else()
        get_target_property(D_IMPORTED ${D} IMPORTED)
        if(D_IMPORTED)
          if(D IN_LIST PKG_LIBS OR NOT D MATCHES "::")
            list(APPEND PKG_IMPORTED_LIBS ${D})
          else()
            list(APPEND PKG_REQUIRED_IMPORTED_LIBS ${D})
          endif()
        else()
          get_target_property(D_TYPE ${D} TYPE)
          if(D_TYPE STREQUAL "INTERFACE_LIBRARY")
            list(APPEND PKG_INTERFACE_LIBS ${D})
          else()
            list(APPEND PKG_BUILD_LIBS ${D})
          endif()
        endif()
      endif()
    endif()
  endforeach()
  # save
  foreach(X ${VARS})
    list(REMOVE_DUPLICATES ${X})
    set(${${X}_VAR} ${${X}} PARENT_SCOPE)
  endforeach()
endfunction(arcpkg_group_dependencies)


# Check if it's system library.
function(arcpkg_is_system_library RESULT_VAR D)
  if(CMAKE_CROSSCOMPILING AND ARCBUILD_ROOT AND D MATCHES "^${ARCBUILD_ROOT}/")
    # toolchain build-in library
    set(${RESULT_VAR} TRUE PARENT_SCOPE)
  elseif(UNIX AND D MATCHES "^/usr/lib/[^/]+/lib[^/]+\\.(a|so)$")
    # e.g. /usr/lib/x86_64-linux-gnu/libGL.so
    set(${RESULT_VAR} TRUE PARENT_SCOPE)
  elseif(UNIX AND D MATCHES "^/usr/lib/gcc/[^/]+/[0-9]+/lib[^/]+\\.(a|so)$")
    # e.g. /usr/lib/gcc/x86_64-linux-gnu/9/libgomp.so
    set(${RESULT_VAR} TRUE PARENT_SCOPE)
  elseif(APPLE AND D MATCHES "^/usr/lib/lib[^/]+\\.(a|dylib|tbd)$")
    # e.g. /usr/lib/libpthread.dylib
    set(${RESULT_VAR} TRUE PARENT_SCOPE)
  elseif(ANDROID AND D MATCHES "/usr/lib/lib[^/]+\\.(a|so)$")
    # e.g. D:/ndk/platforms/android-16/arch-arm/usr/lib/liblog.so (NDK<r22)
    set(${RESULT_VAR} TRUE PARENT_SCOPE)
  elseif(ANDROID AND D MATCHES "/sysroot/usr/lib/[^/]+/[0-9]+/lib[^/]+\\.(a|so)$")
    # e.g. D:/ndk/sysroot/usr/lib/arm-linux-androideabi/21/liblog.so
    set(${RESULT_VAR} TRUE PARENT_SCOPE)
  elseif(D MATCHES "^[^:\\/]+$") # short name for system library, e.g. pthread, opengl32
    set(${RESULT_VAR} TRUE PARENT_SCOPE)
  elseif(D MATCHES "^-[^:\\/]+$") # -lpthread or link flags passed by target_link_libraries()
    set(${RESULT_VAR} TRUE PARENT_SCOPE)
  else()
    set(${RESULT_VAR} FALSE PARENT_SCOPE)
  endif()
endfunction(arcpkg_is_system_library)


# arcpkg_filter_shared_libraries(<shared-targets-var> <shared-libs-var> <shared-sys-libs-var> [target...])
# <shared-libs-var>: including imported shared library locations ($<TARGET_FILE:X>).
function(arcpkg_filter_shared_libraries SHARED_TARGETS_VAR SHARED_PATHS_VAR SHARED_SYSTEM_PATHS_VAR)
  set(SHARED_TARGETS)
  set(SHARED_PATHS)
  set(SHARED_SYS_PATHS)
  foreach(D ${ARGN})
    if(NOT TARGET ${D})
      if(D MATCHES "\\.(so|dylib|tbd|dll|DLL)$")
        arcpkg_is_system_library(IS_SYS_LIB ${D})
        if(IS_SYS_LIB)
          list(APPEND SHARED_SYS_PATHS ${D})
        else()
          list(APPEND SHARED_PATHS ${D})
        endif()
      endif()
    else()
      get_target_property(TYPE ${D} TYPE)
      if(TYPE MATCHES "^(SHARED|MODULE)_LIBRARY$")
        list(APPEND SHARED_TARGETS ${D})
      endif()
    endif()
  endforeach()
  set(${SHARED_TARGETS_VAR} ${SHARED_TARGETS} PARENT_SCOPE)
  set(${SHARED_PATHS_VAR} ${SHARED_PATHS} PARENT_SCOPE)
  set(${SHARED_SYSTEM_PATHS_VAR} ${SHARED_SYS_PATHS} PARENT_SCOPE)
endfunction(arcpkg_filter_shared_libraries)


# Collect paths of shared libraries, including $<TARGET_FILE:X> generator-expressions.
# arcpkg_get_shared_library_locations(<shared-paths> [target...])
function(arcpkg_get_shared_library_locations SHARED_PATHS_VAR)
  arcpkg_filter_shared_libraries(SHARED_TARGETS SHARED_PATHS SHARED_SYS_PATHS ${ARGN})
  foreach(D ${SHARED_TARGETS})
    list(APPEND SHARED_PATHS $<TARGET_FILE:${D}>)
  endforeach()
  set(${SHARED_PATHS_VAR} ${SHARED_PATHS} PARENT_SCOPE)
endfunction(arcpkg_get_shared_library_locations)


# Collect paths of shared dependencies, return generator-expressions.
# arcpkg_get_dependency_locations(<shared-paths> [target...])
function(arcpkg_get_dependency_locations SHARED_PATHS_VAR)
  arcpkg_get_dependencies(DEPENDS ${ARGN})
  arcpkg_get_shared_library_locations(SHARED_PATHS ${DEPENDS})
  set(${SHARED_PATHS_VAR} ${SHARED_PATHS} PARENT_SCOPE)
endfunction(arcpkg_get_dependency_locations)

# Generate export targets to <name>-targets.cmake and <name>-targets-<config>.cmake files.
# arcpkg_export_targets(
#   TARGETS <target>...
#   NAMESPACE <namespace>
#   FILE <name>-targets.cmake
#   INC_DIR <install-inc-dir>
#   LIB_DIR <install-lib-dir>
#   BIN_DIR <install-bin-dir>
#   [WITH_CONFIG]
#   [HAVE_INC_DIR]
# )
function(arcpkg_export_targets)
  cmake_parse_arguments(A
    "WITH_CONFIG;HAVE_INC_DIR"
    "NAMESPACE;FILE;INC_DIR;LIB_DIR;BIN_DIR"
    "TARGETS"
    ${ARGN}
  )
  set(CFGS FALSE)
  if(A_WITH_CONFIG)
    list(APPEND CFGS TRUE)
  endif()
  foreach(CFG ${CFGS})
    set(CONTENT [[# Generated by arcpkg.cmake
]])
    if(NOT CFG)
      set(NS_TRGETS ${A_TARGETS})
      list(TRANSFORM NS_TRGETS PREPEND ${A_NAMESPACE})
      string(JOIN " " NS_TRGETS ${NS_TRGETS})
      string(APPEND CONTENT "
# Protect against multiple inclusion, which would fail when already imported targets are added once more.
set(_cmake_expected_targets ${NS_TRGETS})
")
      string(APPEND CONTENT [[
set(_cmake_targets_defined)
set(_cmake_targets_not_defined)
foreach(_cmake_expected_target ${_cmake_expected_targets})
  if(NOT TARGET ${_cmake_expected_target})
    list(APPEND _cmake_targets_not_defined ${_cmake_expected_target})
  else()
    list(APPEND _cmake_targets_defined ${_cmake_expected_target})
  endif()
endforeach()
if("${_cmake_targets_defined}" STREQUAL "${_cmake_expected_targets}")
  unset(_cmake_expected_targets)
  unset(_cmake_targets_defined)
  unset(_cmake_targets_not_defined)
  return()
endif()
if(NOT "${_cmake_targets_defined}" STREQUAL "")
  string(REPLACE ";" ", " _cmake_targets_defined_text "${_cmake_targets_defined}")
  string(REPLACE ";" ", " _cmake_targets_not_defined_text "${_cmake_targets_not_defined}")
  message(FATAL_ERROR "Some (but not all) targets in this export set were already defined.\nTargets Defined: ${_cmake_targets_defined_text}\nTargets not yet defined: ${_cmake_targets_not_defined_text}\n")
endif()
unset(_cmake_expected_targets)
unset(_cmake_targets_defined)
unset(_cmake_targets_not_defined)

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)
get_filename_component(_IMPORT_PREFIX "${CMAKE_CURRENT_LIST_DIR}" PATH)
if(_IMPORT_PREFIX STREQUAL "/")
  set(_IMPORT_PREFIX "")
endif()
]])
    endif()
    foreach(X ${A_TARGETS})
      set(TARGET ${A_NAMESPACE}${X})
      get_target_property(X_TYPE ${X} TYPE)
      string(REPLACE "_LIBRARY" "" X_TYPE "${X_TYPE}")
      # message("${X}: ${X_TYPE}")
      set(PROPERTIES)
      set(CHECK_FILES)
      if(NOT CFG)
        string(APPEND CONTENT "\n# Create imported target ${TARGET}\n")
        if(X_TYPE STREQUAL "EXECUTABLE")
          string(APPEND CONTENT "add_executable(${TARGET} IMPORTED ${ARCPKG_EXPORT_GLOBAL})\n")
        else()
          string(APPEND CONTENT "add_library(${TARGET} ${X_TYPE} IMPORTED ${ARCPKG_EXPORT_GLOBAL})\n")
        endif()
        # INTERFACE_INCLUDE_DIRECTORIES: set when INCS is not empty.
        # get_target_property(INTERFACE_INCLUDE_DIRECTORIES ${X} INTERFACE_INCLUDE_DIRECTORIES)
        if(A_HAVE_INC_DIR)
          string(APPEND PROPERTIES "\n  INTERFACE_INCLUDE_DIRECTORIES \"\${_IMPORT_PREFIX}/${A_INC_DIR}\"")
        endif()
        # INTERFACE_* properties
        foreach(P AUTOUIC_OPTIONS COMPILE_DEFINITIONS COMPILE_FEATURES COMPILE_OPTIONS
          LINK_DEPENDS LINK_DIRECTORIES LINK_OPTIONS
          POSITION_INDEPENDENT_CODE PRECOMPILE_HEADERS SYSTEM_INCLUDE_DIRECTORIES)
          get_target_property(P_VAL ${X} INTERFACE_${P})
          if(P_VAL)
            string(APPEND PROPERTIES "\n  INTERFACE_${P} \"${P_VAL}\"")
          endif()
        endforeach(P)
        # IMPORTED_LIBNAME
        foreach(P IMPORTED_LIBNAME)
          get_target_property(P_VAL ${X} ${P})
          if(P_VAL)
            string(APPEND PROPERTIES "\n  ${P} \"${P_VAL}\"")
          endif()
        endforeach()
        # LIBRARIES properties
        foreach(P INTERFACE_LINK_LIBRARIES)
          get_target_property(P_VAL ${X} ${P})
          if(NOT P_VAL)
            continue()
          endif()
          set(NEW_P_VAL)
          foreach(V ${P_VAL})
            # $<LINK_ONLY:name>
            if(V MATCHES "^\\$<LINK_ONLY:([^>]+)>$")
              set(V ${CMAKE_MATCH_1})
              if(TARGET ${V})
                get_target_property(V_TYPE ${V} TYPE)
                if(V_TYPE STREQUAL "OBJECT_LIBRARY")
                  continue()
                endif()
              endif()
              set(V_LINK_ONLY 1)
            else()
              set(V_LINK_ONLY)
            endif()
            # Ignore link-only interface library
            if(TARGET ${V})
              get_target_property(V_TYPE ${V} TYPE)
              if(V_TYPE STREQUAL "INTERFACE_LIBRARY" AND V_LINK_ONLY)
                arcpkg_is_pure_interface_library(V_IS_PURE_ILIB ${V})
                if(V_IS_PURE_ILIB)
                  continue()
                endif()
              endif()
            endif()
            if(V IN_LIST A_TARGETS)
              # Prepend namespace
              set(V ${A_NAMESPACE}${V})
            elseif(TARGET ${V})
              # Prepend ${ARCPKG_USER}::
              arcpkg_get_pkg_property(V_USER ${V} ARCPKG_USER)
              if(V_USER AND NOT V MATCHES "${V_USER}::")
                set(V "${V_USER}::${V}")
              endif()
            else()
              arcpkg_get_system_lib_name(V ${V})
            endif()
            if(V_LINK_ONLY)
              set(V \$\<LINK_ONLY:${V}$<ANGLE-R>)
            endif()
            list(APPEND NEW_P_VAL ${V})
          endforeach()
          # message("${P}: ${P_VAL} -> ${NEW_P_VAL}")
          set(P_VAL ${NEW_P_VAL})
          if(P_VAL)
            string(APPEND PROPERTIES "\n  ${P} \"${P_VAL}\"")
          endif()
        endforeach(P)
      else(NOT CFG)
        if(X_TYPE STREQUAL "INTERFACE")
          continue()
        endif()
        string(APPEND CONTENT "
# Import target \"${TARGET}\" for configuration \"$<CONFIG>\"
set_property(TARGET ${TARGET} APPEND PROPERTY IMPORTED_CONFIGURATIONS $<CONFIG>)\n")
        set(SUFFIX _$<UPPER_CASE:$<CONFIG>>)
        # IMPORTED_* properties
        get_target_property(X_IMPORTED ${X} IMPORTED)
        if(X_IMPORTED)
          foreach(P LINK_INTERFACE_LANGUAGES LINK_DEPENDENT_LIBRARIES NO_SONAME SONAME)
            set(P IMPORTED_${P})
            set(PS ${P}${SUFFIX})
            string(APPEND PROPERTIES "$<IF:$<STREQUAL:$<TARGET_PROPERTY:${X},${PS}>,>,$<IF:$<STREQUAL:$<TARGET_PROPERTY:${X},${P}>,>,,\n  ${PS} \"$<TARGET_PROPERTY:${X},${P}>\">,\n  ${PS} \"$<TARGET_PROPERTY:${X},${PS}>\">")
          endforeach(P)
        else()
          # LINK_INTERFACE_LANGUAGES
          set(P LINKER_LANGUAGE)
          set(PS IMPORTED_LINK_INTERFACE_LANGUAGES${SUFFIX})
          string(APPEND PROPERTIES "$<IF:$<STREQUAL:$<TARGET_PROPERTY:${X},${P}>,>,,\n  ${PS} \"$<TARGET_PROPERTY:${X},${P}>\">")
        endif()
        # TARGET_FILE and TARGET_LINK_FILE
        if(MSVC AND X_TYPE STREQUAL "SHARED")
          set(INSTALL_PATH \"\${_IMPORT_PREFIX}/${A_LIB_DIR}/$<TARGET_LINKER_FILE_NAME:${X}>\")
          string(APPEND PROPERTIES "\n  IMPORTED_IMPLIB${SUFFIX} ${INSTALL_PATH}")
          list(APPEND CHECK_FILES ${INSTALL_PATH})
          set(INSTALL_PATH \"\${_IMPORT_PREFIX}/${A_BIN_DIR}/$<TARGET_FILE_NAME:${X}>\")
          string(APPEND PROPERTIES "\n  IMPORTED_LOCATION${SUFFIX} ${INSTALL_PATH}")
          list(APPEND CHECK_FILES ${INSTALL_PATH})
        elseif(X_TYPE MATCHES "^(STATIC|SHARED|MODULE)$")
          set(INSTALL_PATH \"\${_IMPORT_PREFIX}/${A_LIB_DIR}/$<TARGET_FILE_NAME:${X}>\")
          string(APPEND PROPERTIES "\n  IMPORTED_LOCATION${SUFFIX} ${INSTALL_PATH}")
          list(APPEND CHECK_FILES ${INSTALL_PATH})
        elseif(X_TYPE MATCHES "^(EXECUTABLE)$")
          set(INSTALL_PATH \"\${_IMPORT_PREFIX}/${A_BIN_DIR}/$<TARGET_FILE_NAME:${X}>\")
          string(APPEND PROPERTIES "\n  IMPORTED_LOCATION${SUFFIX} ${INSTALL_PATH}")
          list(APPEND CHECK_FILES ${INSTALL_PATH})
        endif()
      endif(NOT CFG)
      if(NOT "${PROPERTIES}" STREQUAL "")
        string(APPEND CONTENT "set_target_properties(${TARGET} PROPERTIES ${PROPERTIES}\n)\n")
        set(PROPERTIES)
      endif()
      if(CHECK_FILES)
        list(JOIN CHECK_FILES " " CHECK_FILES)
        string(APPEND CONTENT "list(APPEND _cmake_import_check_targets ${TARGET})
list(APPEND _cmake_import_check_files_for_${TARGET} ${CHECK_FILES})
")
        unset(CHECK_FILES)
      endif(CHECK_FILES)
    endforeach(X)
    if(CFG)
      string(REGEX REPLACE "\\.cmake$" "-$<LOWER_CASE:$<CONFIG>>.cmake" CFG_FILE ${A_FILE})
    else()
      set(CFG_FILE ${A_FILE})
      get_filename_component(CDG_FILE_BASE_NAME "${CFG_FILE}" NAME_WE)
      string(APPEND CONTENT "
# Load information for each installed configuration.
file(GLOB _cmake_config_files \"\${CMAKE_CURRENT_LIST_DIR}/${CDG_FILE_BASE_NAME}-*.cmake\")
")
      string(APPEND CONTENT [[
foreach(_cmake_config_file ${_cmake_config_files})
  include(${_cmake_config_file})
endforeach()
unset(_cmake_config_file)
unset(_cmake_config_files)

# Cleanup temporary variables.
set(_IMPORT_PREFIX)

# Loop over all imported files and verify that they actually exist
foreach(_cmake_target ${_cmake_import_check_targets})
  foreach(_cmake_file ${_cmake_import_check_files_for_${_cmake_target}})
    if(NOT EXISTS "${_cmake_file}")
      message(FATAL_ERROR "The imported target \"${_cmake_target}\" references the file
   \"${_cmake_file}\"
but this file does not exist. Possible reasons include:
* The file was deleted, renamed, or moved to another location.
* An install or uninstall procedure did not complete successfully.
* The installation package was faulty and contained
   \"${CMAKE_CURRENT_LIST_FILE}\"
but not all the files it references.
")
    endif()
  endforeach()
  unset(_cmake_file)
  unset(_cmake_import_check_files_for_${_cmake_target})
endforeach()
unset(_cmake_target)
unset(_cmake_import_check_targets)
]])
    endif()
    if(NOT CFG)
      string(APPEND CONTENT [[

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
]])
    endif()
    # message("${CONTENT}")
    file(GENERATE OUTPUT "${CFG_FILE}" CONTENT "${CONTENT}")
  endforeach(CFG)
endfunction(arcpkg_export_targets)


# Usage: arcpkg_split_libs_bins(<libs-var> <bins-var> <target>...)
function(arcpkg_split_libs_bins LIBS_VAR BINS_VAR)
  set(LIBS)
  set(BINS)
  foreach(X ${ARGN})
    get_target_property(X_TYPE ${X} TYPE)
    if(X_TYPE MATCHES "_LIBRARY$")
      list(APPEND LIBS ${X})
    elseif(X_TYPE STREQUAL "EXECUTABLE")
      list(APPEND BINS ${X})
    else()
      arcpkg_error("Unknown target type for ${X}: ${X_TYPE}")
    endif()
  endforeach()
  set(${LIBS_VAR} ${LIBS} PARENT_SCOPE)
  set(${BINS_VAR} ${BINS} PARENT_SCOPE)
endfunction(arcpkg_split_libs_bins)


# Usage: arcpkg_get_first_lib_type(<type-var> <target>...)
function(arcpkg_get_first_lib_type TYPE_VAR)
  foreach(X ${ARGN})
    get_target_property(X_TYPE ${X} TYPE)
    if(X_TYPE STREQUAL "STATIC_LIBRARY")
      set(${TYPE_VAR} "static" PARENT_SCOPE)
      return()
    elseif(X_TYPE MATCHES "^(SHARED_LIBRARY|MODULE_LIBRARY)")
      set(${TYPE_VAR} "shared" PARENT_SCOPE)
      return()
    elseif(X_TYPE STREQUAL "SHARED_LIBRARY")
      set(${TYPE_VAR} "shared" PARENT_SCOPE)
      return()
    elseif(X_TYPE STREQUAL "INTERFACE_LIBRARY")
      continue() # ignore
    else()
      arcpkg_error("Unknown library type for ${X}: ${X_TYPE}")
    endif()
  endforeach()
  set(${TYPE_VAR} PARENT_SCOPE) # header-only or data-only
endfunction(arcpkg_get_first_lib_type)


# Usage: arcpkg_is_platform_dependent(<res-var> <target>...)
function(arcpkg_is_platform_dependent RES_VAR)
  foreach(X ${ARGN})
    get_target_property(X_TYPE ${X} TYPE)
    if(X_TYPE MATCHES "^(STATIC_LIBRARY|SHARED_LIBRARY|MODULE_LIBRARY|EXECUTABLE)")
      set(${RES_VAR} ON PARENT_SCOPE)
      return()
    elseif(X_TYPE STREQUAL "INTERFACE_LIBRARY")
      continue() # ignore
    else()
      arcpkg_error("Unknown library type for ${X}: ${X_TYPE}")
    endif()
  endforeach()
  set(${RES_VAR} OFF PARENT_SCOPE) # header-only or data-only
endfunction(arcpkg_is_platform_dependent)


# Parse deploy, outputs:
# - PKG_USER
# - PKG_NAME
# - PKG_VERSION
# - PKG_ABI_NAME, e.g. android-armv7-a-static
function(arcpkg_parse_deploy PKG DEPLOY)
  string(REPLACE "/" ";" PARTS ${DEPLOY})
  list(GET PARTS 0 PKG_USER)
  list(GET PARTS 1 PKG_NAME)
  list(LENGTH PARTS NUM_PARTS)
  if(NUM_PARTS LESS 4)
    list(GET PARTS 2 PKG_ZIP_NAME)
    if(PKG_ZIP_NAME MATCHES "^${PKG_NAME}[-_](.*)$") # e.g. foo/foo-1.0 or foo/foo_1.0
      set(PKG_VERSION ${CMAKE_MATCH_1})
      set(PKG_ABI_NAME)
    else()
      arcpkg_error("Fail to parse deploy: '${DEPLOY}'!")
    endif()
  else()
    list(GET PARTS 2 PKG_VERSION)
    list(GET PARTS 3 PKG_ZIP_NAME)
    arcpkg_escape_for_regex("${PKG_VERSION}" PKG_VERSION_REGEX)
    if(PKG_ZIP_NAME MATCHES "^${PKG_NAME}[-_]${PKG_VERSION_REGEX}$") # e.g. foo/1.0/foo_1.0
      set(PKG_ABI_NAME)
    elseif(PKG_ZIP_NAME MATCHES "^${PKG_NAME}-${PKG_VERSION_REGEX}-(.*)$")
      set(PKG_ABI_NAME ${CMAKE_MATCH_1})
    else()
      if(PKG_ZIP_NAME MATCHES "^${PKG_NAME}_${PKG_VERSION_REGEX}_(.*)$")
        set(PKG_ZIP_NAME ${CMAKE_MATCH_1})
      endif()
      # e.g. PKG_ABI_NAME is android_armv7-a_static or ios-x86_64-static
      string(REPLACE "_" "-" PKG_ABI_NAME "${PKG_ZIP_NAME}") # step 1
      string(REPLACE "-x86-64" "-x86_64" PKG_ABI_NAME "${PKG_ABI_NAME}") # fix step 1
    endif()
  endif()
  set(${PKG}_USER ${PKG_USER} PARENT_SCOPE)
  set(${PKG}_NAME ${PKG_NAME} PARENT_SCOPE)
  set(${PKG}_VERSION ${PKG_VERSION} PARENT_SCOPE)
  set(${PKG}_ABI_NAME ${PKG_ABI_NAME} PARENT_SCOPE)
endfunction(arcpkg_parse_deploy)


# Returns remote INSTALL_DIR.
function(arcpkg_get_remote_install_dir INSTALL_DIR_VAR PKG)
  set(PKG_USER ${${PKG}_USER})
  set(PKG_NAME ${${PKG}_NAME})
  set(PKG_VERSION ${${PKG}_VERSION})
  set(PKG_ABI_NAME ${${PKG}_ABI_NAME})
  if(PKG_ABI_NAME)
    set(INSTALL_DIR "${PKG_USER}/${PKG_NAME}/${PKG_VERSION}/${PKG_NAME}-${PKG_VERSION}-${PKG_ABI_NAME}")
  else()
    set(INSTALL_DIR "${PKG_USER}/${PKG_NAME}/${PKG_NAME}-${PKG_VERSION}")
  endif()
  set(${INSTALL_DIR_VAR} "${INSTALL_DIR}" PARENT_SCOPE)
endfunction(arcpkg_get_remote_install_dir)


# Returns local INSTALL_DIR.
function(arcpkg_get_local_install_dir INSTALL_DIR_VAR PKG)
  set(PKG_USER ${${PKG}_USER})
  set(PKG_NAME ${${PKG}_NAME})
  set(PKG_VERSION ${${PKG}_VERSION})
  set(PKG_ABI_NAME ${${PKG}_ABI_NAME})
  if(PKG_ABI_NAME)
    if(ARCPKG_LOCAL_PKG_FORMAT_VERSION EQUAL 1)
      set(INSTALL_DIR "${PKG_USER}/${PKG_NAME}/${PKG_VERSION}/${PKG_NAME}-${PKG_VERSION}-${PKG_ABI_NAME}")
    elseif(ARCPKG_LOCAL_PKG_FORMAT_VERSION EQUAL 2)
      set(INSTALL_DIR "${PKG_USER}/${PKG_NAME}/${PKG_VERSION}/${PKG_ABI_NAME}")
    else()
      arcpkg_error_i("Unsupported ARCPKG_LOCAL_PKG_FORMAT_VERSION=${ARCPKG_LOCAL_PKG_FORMAT_VERSION}")
    endif()
  else()
    set(INSTALL_DIR "${PKG_USER}/${PKG_NAME}/${PKG_NAME}-${PKG_VERSION}")
  endif()
  set(${INSTALL_DIR_VAR} "${INSTALL_DIR}" PARENT_SCOPE)
endfunction(arcpkg_get_local_install_dir)


# Usage: arcpkg_define_install_general_files(<dst-dirs-var> <install> <install-dir> <target>)
function(arcpkg_define_install_general_files DST_DIRS_VAR PKG_INSTALL INSTALL_DIR PKG_TARGET)
  unset(SRC_ITEMS)
  set(DST_DIRS)
  set(DESTINATION_FLAG 0)
  foreach(X ${PKG_INSTALL} "<EOF>")
    #message(${X})
    if(DESTINATION_FLAG OR X STREQUAL "<EOF>")
      if(X STREQUAL "." OR X STREQUAL "" OR X STREQUAL "<EOF>")
        set(DST_DIR "${INSTALL_DIR}")
      else()
        set(DST_DIR "${INSTALL_DIR}/${X}")
      endif()
      list(APPEND DST_DIRS ${DST_DIR})
      foreach(S ${SRC_ITEMS})
        get_filename_component(S_ABS "${S}" REALPATH)
        if(IS_DIRECTORY ${S_ABS})
          install(DIRECTORY ${S} DESTINATION "${DST_DIR}" COMPONENT ${PKG_TARGET})
          # arcpkg_debug("Install ${S} -> ${DST_DIR}")
        else()
          file(GLOB SS ${S})
          if(SS)
            install(FILES ${SS} DESTINATION "${DST_DIR}" COMPONENT ${PKG_TARGET})
            # arcpkg_debug("Install ${S} -> ${DST_DIR}  ${INC_DIR}")
          endif()
        endif()
      endforeach()
      unset(SRC_ITEMS)
      set(DESTINATION_FLAG 0)
    elseif(X STREQUAL "DESTINATION")
      set(DESTINATION_FLAG 1)
    else()
      list(APPEND SRC_ITEMS ${X})
    endif()
  endforeach()
  if(DEFINED SRC_ITEMS)
    arcpkg_error("Please add DESTINATION after \"INSTALL ${SRC_ITEMS}\"")
  endif()
  if(DST_DIRS)
    list(REMOVE_DUPLICATES DST_DIRS)
  endif()
  set(${DST_DIRS_VAR} ${DST_DIRS} PARENT_SCOPE)
endfunction(arcpkg_define_install_general_files)


# Usage: arcpkg_define_install_includes(<inc-globed-items> <inc-dir> <target>)
function(arcpkg_define_install_includes PKG_INCS_GLOBED INC_DIR PKG_TARGET)
  foreach(X ${PKG_INCS_GLOBED})
  if(IS_DIRECTORY ${X})
    set(X_TYPE DIRECTORY)
  else()
    set(X_TYPE FILES)
  endif()
    install(${X_TYPE} ${X} DESTINATION ${INC_DIR} COMPONENT ${PKG_TARGET})
  endforeach()
endfunction(arcpkg_define_install_includes)


# Usage: arcpkg_define_install_binaries(<install-targets> <lib-dir> <bin-dir> <target>)
function(arcpkg_define_install_binaries PKG_INSTALL_TARGETS LIB_DIR BIN_DIR PKG_TARGET)
  foreach(X ${PKG_INSTALL_TARGETS})
    get_target_property(X_TYPE ${X} TYPE)
    get_target_property(X_IMPORTED ${X} IMPORTED)
    if(UNIX AND NOT X_IMPORTED AND X_TYPE MATCHES "^(STATIC_LIBRARY|SHARED_LIBRARY|MODULE_LIBRARY|EXECUTABLE)$")
      install(TARGETS ${X}
        LIBRARY DESTINATION ${LIB_DIR} COMPONENT ${PKG_TARGET}
        ARCHIVE DESTINATION ${LIB_DIR} COMPONENT ${PKG_TARGET}
        RUNTIME DESTINATION ${BIN_DIR} COMPONENT ${PKG_TARGET})
      continue()
    endif()
    if(MSVC AND X_TYPE STREQUAL "SHARED_LIBRARY")
      install(FILES $<TARGET_FILE:${X}> DESTINATION ${BIN_DIR} COMPONENT ${PKG_TARGET})
      install(FILES $<TARGET_LINKER_FILE:${X}> DESTINATION ${LIB_DIR} COMPONENT ${PKG_TARGET})
    elseif(X_TYPE MATCHES "^(STATIC_LIBRARY|SHARED_LIBRARY|MODULE_LIBRARY)$")
      install(FILES $<TARGET_FILE:${X}> DESTINATION ${LIB_DIR} COMPONENT ${PKG_TARGET})
    elseif(X_TYPE STREQUAL "EXECUTABLE")
      install(FILES $<TARGET_FILE:${X}> DESTINATION ${BIN_DIR} COMPONENT ${PKG_TARGET})
    endif()
  endforeach()
endfunction(arcpkg_define_install_binaries)


# Usage: arcpkg_define_install_bin_dependencies(<bin-targets> <bin-deps> <lib-dir> <bin-dir> <target>)
function(arcpkg_define_install_bin_dependencies PKG_BINS_DEP_TARGETS PKG_BINS_DEP_LIBS LIB_DIR BIN_DIR PKG_TARGET)
  if(MSVC)
    set(DEP_DIR ${BIN_DIR})
  else()
    set(DEP_DIR ${LIB_DIR})
  endif()
  foreach(X ${PKG_BINS_DEP_TARGETS})
    get_target_property(X_IMPORTED ${X} IMPORTED)
    if(UNIX AND NOT X_IMPORTED)
      install(TARGETS ${X}
        LIBRARY DESTINATION ${DEP_DIR} COMPONENT ${PKG_TARGET}
        ARCHIVE DESTINATION ${DEP_DIR} COMPONENT ${PKG_TARGET}
        RUNTIME DESTINATION ${DEP_DIR} COMPONENT ${PKG_TARGET})
    else()
      if(CMAKE_HOST_WIN32)
        install(FILES $<TARGET_FILE:${X}> DESTINATION ${DEP_DIR} COMPONENT ${PKG_TARGET})
      else()
        install(CODE "
# Get actual path of a symbolic file recursively.
function(arcpkg_resolve_symlink RESOLVED_PATH_VAR PATH)
  set(RESOLVED_PATH \"\${PATH}\")
  while(EXISTS \"\${RESOLVED_PATH}\" AND IS_SYMLINK \"\${RESOLVED_PATH}\")
    file(READ_SYMLINK \"\${RESOLVED_PATH}\" ACTUAL_PATH)
    if(NOT IS_ABSOLUTE \"\${ACTUAL_PATH}\")
      get_filename_component(DIR \"\${RESOLVED_PATH}\" DIRECTORY)
      set(RESOLVED_PATH \"\${DIR}/\${ACTUAL_PATH}\")
    endif()
  endwhile()
  # message(STATUS \"Resolves \${PATH} to \${RESOLVED_PATH}\")
  set(\${RESOLVED_PATH_VAR} \${RESOLVED_PATH} PARENT_SCOPE)
endfunction()

arcpkg_resolve_symlink(library_path $<TARGET_FILE:${X}>)
file(INSTALL \"\${library_path}\" DESTINATION \"${CMAKE_INSTALL_PREFIX}/${DEP_DIR}\")

# e.g. libavcodec.so.61.3.100 -> libavcodec.so.61 -> libavcodec.so
while(library_path MATCHES \"\\\\.[0-9]+\$\")
  string(REGEX REPLACE \"\\\\.[0-9]+\$\" \"\" new_library_path \"\${library_path}\")
  set(library_path \"\${new_library_path}\")
  if(EXISTS \"\${library_path}\")
    file(INSTALL \"\${library_path}\" DESTINATION \"${CMAKE_INSTALL_PREFIX}/${DEP_DIR}\")
  endif()
endwhile()
" COMPONENT ${PKG_TARGET})
      endif()
    endif()
  endforeach()
  if(PKG_BINS_DEP_LIBS)
    install(FILES ${PKG_BINS_DEP_LIBS} DESTINATION ${DEP_DIR} COMPONENT ${PKG_TARGET})
  endif()
endfunction(arcpkg_define_install_bin_dependencies)


# Usage: arcpkg_define_install_pdbs(<build-targets> <lib-dir> <bin-dir> <target>)
function(arcpkg_define_install_pdbs PKG_BUILD_TARGETS LIB_DIR BIN_DIR PKG_TARGET)
  foreach(LIB ${PKG_BUILD_TARGETS})
    get_target_property(LIB_TYPE ${LIB} TYPE)
    if(LIB_TYPE STREQUAL "SHARED_LIBRARY")
      install(FILES $<TARGET_PDB_FILE:${LIB}> DESTINATION ${BIN_DIR} COMPONENT ${PKG_TARGET} CONFIGURATIONS Debug RelWithDebInfo)
    # elseif(LIB_TYPE STREQUAL "EXECUTABLE")
    #   install(FILES $<TARGET_PDB_FILE:${LIB}> DESTINATION ${BIN_DIR} COMPONENT ${PKG_TARGET} CONFIGURATIONS Debug RelWithDebInfo)
    elseif(LIB_TYPE STREQUAL "STATIC_LIBRARY")
      install(CODE "
set(LIB_FILE \"$<TARGET_FILE:${LIB}>\")
string(REGEX REPLACE \"\\\\.lib$\" \".pdb\" PDB_FILE \"\${LIB_FILE}\")
#message(\"PDB_FILE: \${PDB_FILE}\")
if(EXISTS \${PDB_FILE})
file(INSTALL \${PDB_FILE} DESTINATION \"${CMAKE_INSTALL_PREFIX}/${LIB_DIR}\")
endif()
" COMPONENT ${PKG_TARGET})
    endif()
  endforeach()
endfunction(arcpkg_define_install_pdbs)


# Usage: arcpkg_define_install_js_wasm(<bins> <bin-dir> <target>)
function(arcpkg_define_install_js_wasm PKG_BINS0 BIN_DIR PKG_TARGET)
  # Install .js/.wasm files
  foreach(BIN ${PKG_BINS0})
    install(CODE "
set(BIN_FILE \"$<TARGET_FILE:${BIN}>\")
string(REGEX REPLACE \"\\\\.html$\" \"\" PREFIX \"\${BIN_FILE}\")
#message(\"BIN_FILE: \${BIN_FILE}\")
file(INSTALL \"\${PREFIX}.js\" DESTINATION \"${CMAKE_INSTALL_PREFIX}/${BIN_DIR}\")
file(INSTALL \"\${PREFIX}.wasm\" DESTINATION \"${CMAKE_INSTALL_PREFIX}/${BIN_DIR}\")
" COMPONENT ${PKG_TARGET})
  endforeach()
endfunction(arcpkg_define_install_js_wasm)


# Usage: arcpkg_define_install_map_files(<build-targets> <map-install-dir> <target>)
function(arcpkg_define_install_map_files PKG_MAP_FILES_VAR PKG_BUILD_TARGETS MAP_INSTALL_DIR PKG_TARGET)
  set(PKG_MAP_FILES)
  foreach(LIB ${PKG_BUILD_TARGETS})
    get_target_property(LINK_MAP_FILE ${LIB} LINK_MAP_FILE)
    # message("LINK_MAP_FILE of ${LIB}: ${LINK_MAP_FILE}")
    if(LINK_MAP_FILE)
      list(APPEND PKG_MAP_FILES ${LIB})
      install(CODE "
if(\"${MSVC}\")
  set(LIB_FILE \"$<TARGET_FILE:${LIB}>\")
  string(REGEX REPLACE \"\\\\.(dll|exe)$\" \".map\" LINK_MAP_FILE \"\${LIB_FILE}\")
else()
  set(LINK_MAP_FILE \"${LINK_MAP_FILE}\")
endif()
# message(\"LINK_MAP_FILE: \${LINK_MAP_FILE}\")
file(INSTALL \${LINK_MAP_FILE} DESTINATION \"${CMAKE_INSTALL_PREFIX}/${MAP_INSTALL_DIR}\")
" COMPONENT ${PKG_TARGET})
    endif()
  endforeach()
  set(${PKG_MAP_FILES_VAR} ${PKG_MAP_FILES} PARENT_SCOPE)
endfunction(arcpkg_define_install_map_files)


# Usage: arcpkg_define_install_config_files(<install-targets> <install-dir> <have-inc-dir> <user> <name> <version> <target>)
function(arcpkg_define_install_config_files PKG_INSTALL_TARGETS INSTALL_DIR HAVE_INC_DIR PKG_USER PKG_NAME PKG_VERSION PKG_TARGET)
  string(TOLOWER "${PKG_NAME}" LOWERCASE_PKG_NAME)
  # <name>-config-version.cmake
  set(PKG_CONFIG_VERSION_FILE "${CMAKE_BINARY_DIR}/${LOWERCASE_PKG_NAME}-config-version.cmake")
  set(PKG_VERSION_FILE_FLAGS COMPATIBILITY SameMajorVersion)
  if(NOT PKG_BUILD_LIBS OR "${CMAKE_OSX_ARCHITECTURES}" MATCHES "\;")
    if(CMAKE_VERSION LESS "3.14")
      arcpkg_error("Require CMake>=3.14 to support architecture-independent (e.g. header-only, universal Apple binaries) packages!")
    else()
      arcpkg_echo("Generating ${PKG_NAME} as an architecture-independent package")
      list(APPEND PKG_VERSION_FILE_FLAGS ARCH_INDEPENDENT)
    endif()
  endif()
  write_basic_package_version_file(${PKG_CONFIG_VERSION_FILE} VERSION ${PKG_VERSION} ${PKG_VERSION_FILE_FLAGS})

  # <name>-config.cmake
  set(PKG_CONFIG_FILE "${CMAKE_BINARY_DIR}/${LOWERCASE_PKG_NAME}-config.cmake")
  set(CONFIG_CONTENT "# Generated by arcpkg.cmake
${PKG_CONFIG_FILE_PREFIX}
include(\"\${CMAKE_CURRENT_LIST_DIR}/${LOWERCASE_PKG_NAME}-targets.cmake\")
set(${PKG_NAME}_FOUND TRUE)

# Set IMPORTED_LINK_INTERFACE_LANGUAGES, IMPORTED_IMPLIB and IMPORTED_LOCATION for imported targets
function(_arcpkg_fix_imported_target TARGET)
  get_target_property(TYPE \${TARGET} TYPE)
  if(TYPE STREQUAL \"INTERFACE_LIBRARY\")
    return()
  endif()
  if(TYPE STREQUAL \"STATIC_LIBRARY\")
    set(REQUIRE_LINK_LANGUAGES ON)
  else()
    set(REQUIRE_LINK_LANGUAGES OFF)
  endif()
  get_target_property(IMPORTED_IMPLIB \${TARGET} IMPORTED_IMPLIB)
  get_target_property(IMPORTED_LOCATION \${TARGET} IMPORTED_LOCATION)
  get_target_property(IMPORTED_LINK_INTERFACE_LANGUAGES \${TARGET} IMPORTED_LINK_INTERFACE_LANGUAGES)
  if((NOT REQUIRE_LINK_LANGUAGES OR IMPORTED_LINK_INTERFACE_LANGUAGES) AND (IMPORTED_IMPLIB OR IMPORTED_LOCATION))
    return()
  endif()
  set(PREFER_CFG)
  foreach(CFG RELEASE MINSIZEREL RELWITHDEBINFO DEBUG)
    get_target_property(IMPORTED_IMPLIB_\${CFG} \${TARGET} IMPORTED_IMPLIB_\${CFG})
    get_target_property(IMPORTED_LOCATION_\${CFG} \${TARGET} IMPORTED_LOCATION_\${CFG})
    get_target_property(IMPORTED_LINK_INTERFACE_LANGUAGES_\${CFG} \${TARGET} IMPORTED_LINK_INTERFACE_LANGUAGES_\${CFG})
    if(IMPORTED_IMPLIB_\${CFG} OR IMPORTED_LOCATION_\${CFG})
      if(NOT PREFER_CFG)
        set(PREFER_CFG \${CFG})
      endif()
    endif()
  endforeach()
  if(NOT (IMPORTED_IMPLIB OR IMPORTED_LOCATION))
    if(NOT PREFER_CFG)
      message(FATAL_ERROR \"Can not find IMPORTED_IMPLIB or IMPORTED_LOCATION of imported target \\\"\${TARGET}\\\"!\")
    endif()
    if(NOT IMPORTED_IMPLIB_\${PREFER_CFG})
      set(IMPORTED_IMPLIB_\${PREFER_CFG})
    endif()
    if(NOT IMPORTED_LOCATION_\${PREFER_CFG})
      set(IMPORTED_LOCATION_\${PREFER_CFG})
    endif()
    set_target_properties(\${TARGET} PROPERTIES
      IMPORTED_IMPLIB \"\${IMPORTED_IMPLIB_\${PREFER_CFG}}\"
      IMPORTED_LOCATION \"\${IMPORTED_LOCATION_\${PREFER_CFG}}\"
    )
  endif()
  if(REQUIRE_LINK_LANGUAGES AND NOT IMPORTED_LINK_INTERFACE_LANGUAGES AND IMPORTED_LINK_INTERFACE_LANGUAGES_\${PREFER_CFG})
    set_target_properties(\${TARGET} PROPERTIES
      IMPORTED_LINK_INTERFACE_LANGUAGES \"\${IMPORTED_LINK_INTERFACE_LANGUAGES_\${PREFER_CFG}}\"
    )
  endif()
endfunction()
foreach(X ${PKG_INSTALL_TARGETS})
  _arcpkg_fix_imported_target(${PKG_USER}::\${X})
endforeach()
unset(X)
${PKG_CONFIG_FILE_SUFFIX}")
  string(STRIP "${CONFIG_CONTENT}" CONFIG_CONTENT)
  file(GENERATE OUTPUT ${PKG_CONFIG_FILE} CONTENT "${CONFIG_CONTENT}")

  # WITH_CONFIG
  set(WITH_CONFIG)
  foreach(X ${PKG_INSTALL_TARGETS})
    get_target_property(X_TYPE ${X} TYPE)
    if(NOT X_TYPE STREQUAL "INTERFACE_LIBRARY")
      set(WITH_CONFIG "WITH_CONFIG")
      break()
    endif()
  endforeach(X)

  # <name>-targets-imported.cmake
  set(PKG_TARGETS_FILE "${CMAKE_BINARY_DIR}/${LOWERCASE_PKG_NAME}-targets.cmake")
  arcpkg_export_targets(TARGETS ${PKG_INSTALL_TARGETS} NAMESPACE ${PKG_USER}:: FILE ${PKG_TARGETS_FILE}
    INC_DIR "${PKG_INC_DIR}"
    LIB_DIR "${PKG_LIB_DIR}"
    BIN_DIR "${PKG_BIN_DIR}"
    ${WITH_CONFIG}
    ${HAVE_INC_DIR}
  )

  # Install
  install(FILES ${PKG_CONFIG_VERSION_FILE} ${PKG_CONFIG_FILE} ${PKG_TARGETS_FILE}
    DESTINATION ${INSTALL_DIR}/cmake COMPONENT ${PKG_TARGET}
  )
  if(WITH_CONFIG)
    install(FILES "${CMAKE_BINARY_DIR}/${LOWERCASE_PKG_NAME}-targets-$<LOWER_CASE:$<CONFIG>>.cmake"
      DESTINATION ${INSTALL_DIR}/cmake COMPONENT ${PKG_TARGET}
    )
  endif()
endfunction(arcpkg_define_install_config_files)


###########################################################
# Define a package and generate INSTALL commands.
# Usage: arcpkg_define(
#   NAME <name>
#   VERSION <version>
#   [USER <user>]
#   [CHANNEL <channel>]
#
#   [SUMMARY <summary>] # A one-line summary of what the pkg does.
#   [DESCRIPTION <description>] # A longer description of the pkg that can run to several paragraphs.
#   [URL <url>]
#   [HOMEPAGE <homepage>] # A string containing the URL for the pkg's home page.
#   [COPYRIGHT <copyright>] # A string named the copyright.
#   [LICENSE <license>] # A string named the license name.
#
#   [AUTHORS <author>...] # <AUTHOR> <EMAIL>
#   [MAINTAINERS <maintainer>...]
#
#   [INCS <inc>...]
#   [LIBS <target>...]
#   [BINS <target>...] # same as LIBS
#   [REQUIRES <recipes>...]
#
#   [INC_DIR <inc-dir>]
#   [LIB_DIR <lib-dir>]
#   [BIN_DIR <bin-dir>]
#
#   [HINTS <hint>...]
#   [CONFIG_FILE_PREFIX <config-file-prefix>]
#   [CONFIG_FILE_SUFFIX <config-file-suffix>]
#
#   [INSTALL <source>... DESTINATION <dir>]...
#
#   [NO_EXPORT] # do not export targets
#   [NO_CONFIG_FILE] # do not generate config files
#   [NO_INSTALL_TARGETS] # do not install targets
#   [NO_INSTALL_DIR_PREFIX] # do not add install dir prefix
# )
# Targets:
#   - arcpkg
#   - arcpkg-(clean|pack|upload|export)
#   - <user>-<name>-(clean|pack|upload|export)
function(arcpkg_define)
  if(CMAKE_VERSION LESS 3.16)
    arcpkg_warn("Please use CMake>=3.16 to export correct INTERFACE_LINK_LIBRARIES for shared libraries!")
  endif()
  # First pass argument parsing
  cmake_parse_arguments(PKG "NO_CONFIG_FILE;NO_EXPORT;NO_INSTALL_TARGETS;NO_INSTALL_DIR_PREFIX"
    "${ARCPKG_DEFINE_SINGLE_VALUE_KEYS};CONFIG_FILE_PREFIX;CONFIG_FILE_SUFFIX;TYPE;INC_DIR;LIB_DIR;BIN_DIR"
    "INCS;LIBS;BINS;INSTALL;${ARCPKG_DEFINE_MULTIPLE_VALUES_KEYS}"
    ${ARGN}
  )
  # Second pass argument parsing
  cmake_parse_arguments(PKG "" "${ARCPKG_DEFINE_SINGLE_VALUE_KEYS_UNPARSED}" "" "${PKG_UNPARSED_ARGUMENTS}")

  # Fill attributes
  if(NOT PKG_USER)
    #arcpkg_warn("No USER is defined, use \"${ARCPKG_DEFAULT_USER}\".")
    set(PKG_USER ${ARCPKG_DEFAULT_USER})
  endif()
  if(NOT PKG_CHANNEL)
    #arcpkg_warn("No CHANNEL is defined, use \"${ARCPKG_DEFAULT_CHANNEL}\".")
    set(PKG_CHANNEL ${ARCPKG_DEFAULT_CHANNEL})
  endif()
  set(HAVE_INC_DIR)
  if(NOT PKG_INC_DIR)
    set(PKG_INC_DIR inc)
  else()
    set(HAVE_INC_DIR "HAVE_INC_DIR")
  endif()
  if(NOT PKG_LIB_DIR)
    set(PKG_LIB_DIR lib)
  endif()
  if(NOT PKG_BIN_DIR)
    set(PKG_BIN_DIR bin)
  endif()
  arcpkg_get_project_vcs_revision(PKG_VCS_REVISION)
  list(APPEND PKG_AUTHORS ${PKG_AUTHOR})
  list(APPEND PKG_MAINTAINERS ${PKG_MAINTAINER})
  unset(PKG_AUTHOR)
  unset(PKG_MAINTAINER)

  # Check required items
  foreach(X NAME VERSION)
    set(X_VAR PKG_${X})
    if(NOT DEFINED ${X_VAR})
      arcpkg_error("No ${X_VAR} is defined in arcpkg_define()")
    endif()
  endforeach()

  arcpkg_split_libs_bins(PKG_LIBS PKG_BINS ${PKG_LIBS} ${PKG_BINS})
  arcpkg_get_first_lib_type(FIRST_LIB_TYPE ${PKG_LIBS})
  if(NOT ("static" IN_LIST PKG_HINTS OR "shared" IN_LIST PKG_HINTS))
    list(APPEND PKG_HINTS ${FIRST_LIB_TYPE})
  endif()

  # Collect PKG_*_LIBS
  arcpkg_get_dependencies(PKG_DEPENDS ${PKG_LIBS} STOP_PKG)
  arcpkg_group_dependencies(PKG_SYSTEM_LIBS PKG_IMPORTED_LIBS PKG_INTERFACE_LIBS PKG_BUILD_LIBS PKG_REQUIRED_IMPORTED_LIBS "${PKG_LIBS}" "${PKG_DEPENDS}")
  # message("PKG_DEPENDS: ${PKG_DEPENDS}")
  # message("PKG_LIBS: ${PKG_LIBS}")
  # message("PKG_SYSTEM_LIBS: ${PKG_SYSTEM_LIBS}")
  # message("PKG_IMPORTED_LIBS: ${PKG_IMPORTED_LIBS}")
  # message("PKG_INTERFACE_LIBS: ${PKG_INTERFACE_LIBS}")
  # message("PKG_BUILD_LIBS: ${PKG_BUILD_LIBS}")
  # message("PKG_REQUIRED_IMPORTED_LIBS: ${PKG_REQUIRED_IMPORTED_LIBS}")

  # PKG_REQUIRES
  foreach(D ${PKG_REQUIRED_IMPORTED_LIBS})
    if(TARGET ${D})
      arcpkg_get_pkg_property(D_RECIPE ${D} ARCPKG_RECIPE)
      if(D_RECIPE)
        list(APPEND PKG_REQUIRES ${D_RECIPE})
        continue()
      endif()
    endif()
    list(APPEND PKG_REQUIRES ${D})
  endforeach()
  if(PKG_REQUIRES)
    list(REMOVE_DUPLICATES PKG_REQUIRES)
  endif()
  if(PKG_SYSTEM_LIBS)
    list(APPEND PKG_REQUIRES ${PKG_SYSTEM_LIBS})
  endif()
  set(PKG_LIBS ${PKG_IMPORTED_LIBS} ${PKG_INTERFACE_LIBS} ${PKG_BUILD_LIBS}) # libs to be exported
  set(PKG_INSTALL_TARGETS ${PKG_LIBS} ${PKG_BINS}) # targets to be installed
  set(PKG_BUILD_TARGETS ${PKG_BUILD_LIBS} ${PKG_BINS}) # targets to be built

  # Scan dependencies of BINS
  arcpkg_get_dependencies(BINS_DEPENDS ${PKG_BINS})
  # message("PKG_INSTALL_TARGETS: ${PKG_INSTALL_TARGETS}")
  # message("PKG_BINS: ${PKG_BINS}")
  # message("BINS_DEPENDS: ${BINS_DEPENDS}")
  list(REMOVE_ITEM BINS_DEPENDS ${PKG_LIBS} "")
  if(BINS_DEPENDS)
    arcpkg_filter_shared_libraries(PKG_BINS_DEP_TARGETS PKG_BINS_DEP_LIBS PKG_BINS_SYSTEM_LIBS ${BINS_DEPENDS})
  endif()

  # INSTALL_DIR and REMOTE_INSTALL_DIR
  if(PKG_HINTS)
    set(PLATFORM_DEPENDENT ON)
  else()
    arcpkg_is_platform_dependent(PLATFORM_DEPENDENT ${PKG_LIBS} ${PKG_BINS})
  endif()
  set(PKG_PLATFORM ${ARCPKG_PLATFORM})
  set(PKG_ARCH ${ARCPKG_ARCH})
  if(PLATFORM_DEPENDENT)
    arcpkg_get_abi_parts(ABI_PARTS "${PKG_PLATFORM}" "${PKG_ARCH}" "${PKG_HINTS}")
    string(JOIN "-" PKG_ABI_NAME ${ABI_PARTS})
  else()
    set(PKG_ABI_NAME)
  endif()
  arcpkg_get_local_install_dir(INSTALL_DIR PKG)
  arcpkg_get_remote_install_dir(REMOTE_INSTALL_DIR PKG)

  # Directories
  set(PKG_INSTALL_DIR "${INSTALL_DIR}") # for arcpkg.txt and export target
  set(REMOTE_MAP_INSTALL_DIR "${REMOTE_INSTALL_DIR}-map")
  if(PKG_NO_INSTALL_DIR_PREFIX)
    get_filename_component(INSTALL_DIRNAME ${REMOTE_INSTALL_DIR} NAME)
    set(MAP_INSTALL_DIR "${CMAKE_BINARY_DIR}/${INSTALL_DIRNAME}-map")
    set(ZIP_PATH_ABS "${CMAKE_BINARY_DIR}/${INSTALL_DIRNAME}.zip")
    set(MAP_ZIP_PATH_ABS "${CMAKE_BINARY_DIR}/${MAP_INSTALL_DIR}.zip")
    set(INSTALL_DIR ".")
    set(INSTALL_DIR_ABS "${CMAKE_INSTALL_PREFIX}")
  else()
    set(MAP_INSTALL_DIR "${INSTALL_DIR}-map")
    set(ZIP_PATH_ABS "${CMAKE_INSTALL_PREFIX}/${INSTALL_DIR}.zip")
    set(MAP_ZIP_PATH_ABS "${CMAKE_INSTALL_PREFIX}/${MAP_INSTALL_DIR}.zip")
    set(INSTALL_DIR_ABS "${CMAKE_INSTALL_PREFIX}/${INSTALL_DIR}")
  endif()
  set(MAP_INSTALL_DIR_ABS "${CMAKE_INSTALL_PREFIX}/${MAP_INSTALL_DIR}")
  set(INC_DIR "${INSTALL_DIR}/${PKG_INC_DIR}")
  set(LIB_DIR "${INSTALL_DIR}/${PKG_LIB_DIR}")
  set(BIN_DIR "${INSTALL_DIR}/${PKG_BIN_DIR}")

  #########################################################
  # Setup properties for pkg targets
  #########################################################

  # Change DEBUG_POSTFIX and COMPILE_PDB_NAME_DEBUG for LIBS
  foreach(X ${PKG_BUILD_LIBS})
    get_target_property(X_DEBUG_POSTFIX ${X} DEBUG_POSTFIX)
    if(NOT X_DEBUG_POSTFIX)
      set_target_properties(${X} PROPERTIES DEBUG_POSTFIX "${ARCPKG_DEBUG_SUFFIX}")
      set_target_properties(${X} PROPERTIES COMPILE_PDB_NAME_DEBUG "${X}${ARCPKG_DEBUG_SUFFIX}")
    endif()
  endforeach()

  # Change INSTALL_RPATH for LIBS
  foreach(X ${PKG_BINS})
    get_target_property(X_INSTALL_RPATH ${X} INSTALL_RPATH)
    if(NOT X_INSTALL_RPATH OR X_INSTALL_RPATH STREQUAL "$ORIGIN")
      set_target_properties(${X} PROPERTIES INSTALL_RPATH "$ORIGIN;$ORIGIN/../lib")
    endif()
  endforeach()

  #########################################################
  # Setup ARCPKG_* properties for pkg targets
  #########################################################

  # Define more variables
  set(EXTRA_ATTRIBUTES BINS_DEPENDS)
  foreach(X ${EXTRA_ATTRIBUTES})
    set(PKG_${X} ${${X}})
  endforeach()
  set(PKG_DIR ${CMAKE_CURRENT_SOURCE_DIR})
  arcpkg_make_recipe(PKG_RECIPE PKG) # recipe

  # Prepend namespace to LIBS and BINS
  set(PKG_LIBS0 ${PKG_LIBS})
  set(PKG_BINS0 ${PKG_BINS})
  if(NOT PKG_NO_INSTALL_TARGETS)
    list(TRANSFORM PKG_LIBS PREPEND "${PKG_USER}::")
    list(TRANSFORM PKG_BINS PREPEND "${PKG_USER}::")
  endif()

  # Create <user>::<name> library if not existent for data-only pkg
  set(PKG_LIB_TARGET ${PKG_USER}::${PKG_NAME})
  if(NOT TARGET ${PKG_LIB_TARGET} AND NOT PKG_LIBS AND NOT PKG_BINS)
    add_library(${PKG_LIB_TARGET} INTERFACE IMPORTED ${ARCPKG_EXPORT_GLOBAL})
    list(APPEND PKG_LIBS ${PKG_LIB_TARGET})
    arcpkg_set_all_pkg_properties(${PKG_LIB_TARGET} PKG ${EXTRA_ATTRIBUTES})
  endif()

  # Create ALIAS targets and set ARCPKG properties for them
  foreach(X ${PKG_LIBS0})
    get_target_property(IMPORTED ${X} IMPORTED)
    get_target_property(IMPORTED_GLOBAL ${X} IMPORTED_GLOBAL)
    if(NOT IMPORTED OR NOT CMAKE_VERSION VERSION_LESS 3.18 OR IMPORTED_GLOBAL)
      add_library(${PKG_USER}::${X} ALIAS ${X})
    endif()
    arcpkg_set_all_pkg_properties(${X} PKG ${EXTRA_ATTRIBUTES})
  endforeach()
  foreach(X ${PKG_BINS0})
    add_executable(${PKG_USER}::${X} ALIAS ${X})
    arcpkg_set_all_pkg_properties(${X} PKG ${EXTRA_ATTRIBUTES})
  endforeach()

  # create <user>-<name> target
  set(PKG_TARGET ${PKG_USER}-${PKG_NAME})
  if(NOT TARGET arcpkg)
    add_custom_target(arcpkg)
    set_target_properties(arcpkg PROPERTIES FOLDER "arcpkg")
  endif()
  add_custom_target(${PKG_TARGET} DEPENDS ${PKG_INSTALL_TARGETS})
  add_dependencies(arcpkg ${PKG_TARGET})
  set_target_properties(${PKG_TARGET} PROPERTIES FOLDER "arcpkg")

  # Set ARCPKG properties for pkg targets
  arcpkg_set_all_pkg_properties(${PKG_TARGET} PKG ${EXTRA_ATTRIBUTES})

  # Export <user>_<name>_<prop> variables
  set(PKG ${PKG_USER}_${PKG_NAME})
  foreach(X ${ARCPKG_ATTRIBUTES})
    set(${PKG}_${X} ${PKG_${X}} PARENT_SCOPE)
  endforeach()

  # Debug information
  arcpkg_echo("arcpkg_define (${PKG_RECIPE}, ${PKG_CHANNEL}):")
  set(EXCLUDE_NAMES NAME VERSION USER HINTS CHANNEL RECIPE INC_DIR LIB_DIR BIN_DIR)
  foreach(X ${ARCPKG_ATTRIBUTES} INCS BINS_DEPENDS UNPARSED_ARGUMENTS)
    if(PKG_${X} AND NOT X IN_LIST EXCLUDE_NAMES)
      arcpkg_debug("  ${X}: ${PKG_${X}}")
    endif()
  endforeach()
  # arcpkg_debug("  DIR: ${PKG_INC_DIR}/${PKG_LIB_DIR}/${PKG_BIN_DIR}")
  # arcpkg_debug("  INSTALL_DIR: ${INSTALL_DIR}")
  # arcpkg_debug("  REMOTE_INSTALL_DIR: ${REMOTE_INSTALL_DIR}")

  # Early return if not export pkgs
  if(PKG_NO_EXPORT)
    return()
  endif()

  #########################################################
  # Install targets and files
  #########################################################

  if(POLICY CMP0087)
    cmake_policy(SET CMP0087 NEW) # install(CODE) and install(SCRIPT) support generator expressions, warns when CMake>=3.21.2
  endif()

  # Install general files
  arcpkg_define_install_general_files(DST_DIRS "${PKG_INSTALL}" ${INSTALL_DIR} ${PKG_TARGET})
  foreach(DST_DIR ${DST_DIRS})
    if(DST_DIR MATCHES "${INC_DIR}")
      set(HAVE_INC_DIR "HAVE_INC_DIR")
    endif()
  endforeach()

  # Install targets
  if(NOT PKG_NO_INSTALL_TARGETS)
    # Install include files
    file(GLOB PKG_INCS_GLOBED LIST_DIRECTORIES TRUE ${PKG_INCS})
    arcpkg_define_install_includes("${PKG_INCS_GLOBED}" ${INC_DIR} ${PKG_TARGET})
    if(PKG_INCS_GLOBED)
      set(HAVE_INC_DIR "HAVE_INC_DIR")
    endif()

    # Install targets (LIBS + BINS + IMPORTED)
    arcpkg_define_install_binaries("${PKG_INSTALL_TARGETS}" ${LIB_DIR} ${BIN_DIR} ${PKG_TARGET})

    # Note: keep install same rules for PKG_LIBS and PKG_BINS.
    arcpkg_define_install_bin_dependencies("${PKG_BINS_DEP_TARGETS}" "${PKG_BINS_DEP_LIBS}" ${LIB_DIR} ${BIN_DIR} ${PKG_TARGET})

    if(MSVC)
      # Install .pdb files
      arcpkg_define_install_pdbs("${PKG_BUILD_TARGETS}" ${LIB_DIR} ${BIN_DIR} ${PKG_TARGET})
    elseif(EMSCRIPTEN)
      # Install .js/.wasm files
      arcpkg_define_install_js_wasm("${PKG_BINS0}" ${BIN_DIR} ${PKG_TARGET})
    endif()

    # Install .map files
    arcpkg_define_install_map_files(PKG_MAP_FILES "${PKG_BUILD_TARGETS}" ${MAP_INSTALL_DIR} ${PKG_TARGET})

    # Generate <name>-config-version.cmake, <name>-config-targets.cmake and <name>-config-targets-<cfg>.cmake
    if(PKG_INSTALL_TARGETS AND NOT PKG_NO_CONFIG_FILE)
      arcpkg_define_install_config_files(
        "${PKG_INSTALL_TARGETS}"
        "${INSTALL_DIR}"
        "${HAVE_INC_DIR}"
        "${PKG_USER}"
        "${PKG_NAME}"
        "${PKG_VERSION}"
        "${PKG_TARGET}"
      )
    endif()
  endif()

  # Install arcpkg.txt
  if(PKG_NO_INSTALL_TARGETS)
    unset(PKG_LIBS)
    unset(PKG_BINS)
  endif()
  set(PKG_INFO_TXT "${CMAKE_CURRENT_BINARY_DIR}/arcpkg-${PKG_NAME}.txt")
  arcpkg_generate_info_file(${PKG_INFO_TXT} PKG ${PLATFORM_DEPENDENT})
  install(FILES ${PKG_INFO_TXT} DESTINATION "${INSTALL_DIR}" RENAME arcpkg.txt COMPONENT ${PKG_TARGET})
  # install(CODE "config${INSTALL_DIR}\")" COMPONENT ${PKG_TARGET})

  # cpack_add_component(${PKG_NAME} DESCRIPTION "${PKG_DESCRIPTION}")

  #########################################################
  # Create <user>-<name>-(clean,pack,upload,export) targets
  #########################################################

  # create <pkg>-clean target
  if(NOT TARGET arcpkg-clean)
    add_custom_target(arcpkg-clean)
    set_target_properties(arcpkg-clean PROPERTIES FOLDER "arcpkg")
  endif()
  set(CLEAN_PKG_TARGET "${PKG_TARGET}-clean")
  if(CMAKE_VERSION VERSION_LESS "3.17")
    set(REMOVE_FILE_CMD remove -f)
    set(REMOVE_TREE_CMD remove_directory)
  else()
    set(REMOVE_FILE_CMD rm -f)
    set(REMOVE_TREE_CMD rm -rf)
  endif()
  # message("REMOVE_TREE_CMD: ${REMOVE_TREE_CMD}")
  add_custom_target(
    ${CLEAN_PKG_TARGET}
    COMMAND ${CMAKE_COMMAND} -E ${REMOVE_FILE_CMD} "${ZIP_PATH_ABS}"
    COMMAND ${CMAKE_COMMAND} -E ${REMOVE_FILE_CMD} "${MAP_ZIP_PATH_ABS}"
    COMMAND ${CMAKE_COMMAND} -E ${REMOVE_TREE_CMD} "${INSTALL_DIR_ABS}" # clean
    COMMAND ${CMAKE_COMMAND} -E ${REMOVE_TREE_CMD} "${MAP_INSTALL_DIR_ABS}" # clean
    COMMENT "Cleaning zip files and install directories of ${PKG_RECIPE} in ${INSTALL_DIR_ABS}"
  )
  add_dependencies(arcpkg-clean ${CLEAN_PKG_TARGET})
  set_target_properties(${CLEAN_PKG_TARGET} PROPERTIES FOLDER "arcpkg")

  # create <pkg>-pack target
  if(NOT TARGET arcpkg-pack)
    add_custom_target(arcpkg-pack)
    set_target_properties(arcpkg-pack PROPERTIES FOLDER "arcpkg")
  endif()
  set(PACK_PKG_TARGET "${PKG_TARGET}-pack")
  set(PACK_COMMANDS)
  if(Python_EXECUTABLE)
    # prefer to use python to make checksum of zip file consistent
    set(ZIP_CMD ${ARCPKG_COMMAND} py_zip_dir)
    set(ZIP_CMD_OPTIONS)
  else()
    set(ZIP_CMD ${CMAKE_COMMAND} -E tar cvf)
    set(ZIP_CMD_OPTIONS --format=zip)
  endif()
  list(APPEND PACK_COMMANDS COMMAND ${CMAKE_COMMAND} -E chdir "${INSTALL_DIR_ABS}" ${ZIP_CMD} "${ZIP_PATH_ABS}" ${ZIP_CMD_OPTIONS} .)
  if(PKG_MAP_FILES)
    list(APPEND PACK_COMMANDS COMMAND ${CMAKE_COMMAND} -E chdir "${MAP_INSTALL_DIR_ABS}" ${ZIP_CMD} "${MAP_ZIP_PATH_ABS}" ${ZIP_CMD_OPTIONS} .)
  endif()
  add_custom_target(
    ${PACK_PKG_TARGET}
    ${PACK_COMMANDS}
    # DEPENDS ${PKG_INSTALL_TARGETS}
    COMMENT "Packing ${PKG_RECIPE} to ${ZIP_PATH_ABS}"
    ${ZIP_OUTPUT_QUIET}
    VERBATIM
  )
  add_dependencies(arcpkg-pack ${PACK_PKG_TARGET})
  set_target_properties(${PACK_PKG_TARGET} PROPERTIES FOLDER "arcpkg")

  # create <pkg>-upload target
  if(NOT TARGET arcpkg-upload)
    add_custom_target(arcpkg-upload)
    set_target_properties(arcpkg-upload PROPERTIES FOLDER "arcpkg")
  endif()
  set(UPLOAD_PKG_TARGET "${PKG_TARGET}-upload")
  set(UPLOAD_COMMANDS COMMAND ${ARCPKG_COMMAND} upload_file "${ZIP_PATH_ABS}" "${REMOTE_INSTALL_DIR}.zip")
  if(PKG_MAP_FILES)
    list(APPEND UPLOAD_COMMANDS COMMAND ${ARCPKG_COMMAND} upload_file "${MAP_ZIP_PATH_ABS}" "${REMOTE_MAP_INSTALL_DIR}.zip")
  endif()
  add_custom_target(
    ${UPLOAD_PKG_TARGET}
    ${UPLOAD_COMMANDS}
    # DEPENDS ${PACK_PKG_TARGET}
    COMMENT "Uploading ${PKG_RECIPE} to remote ${REMOTE_INSTALL_DIR}"
    VERBATIM
  )
  add_dependencies(arcpkg-upload ${UPLOAD_PKG_TARGET})
  set_target_properties(${UPLOAD_PKG_TARGET} PROPERTIES FOLDER "arcpkg")

  # create <pkg>-export target
  if(NOT TARGET arcpkg-export)
    add_custom_target(arcpkg-export)
    set_target_properties(arcpkg-export PROPERTIES FOLDER "arcpkg")
  endif()
  set(EXPORT_PKG_TARGET "${PKG_TARGET}-export")
  add_custom_target(
    ${EXPORT_PKG_TARGET}
    COMMAND ${ARCPKG_COMMAND} install_file "${ZIP_PATH_ABS}" "${PKG_INSTALL_DIR}"
    # DEPENDS ${PACK_PKG_TARGET}
    COMMENT "Exporting ${PKG_RECIPE} to local cache ${PKG_INSTALL_DIR}"
  )
  add_dependencies(${EXPORT_PKG_TARGET} ${PACK_PKG_TARGET})
  add_dependencies(arcpkg-export ${EXPORT_PKG_TARGET})
  set_target_properties(${EXPORT_PKG_TARGET} PROPERTIES FOLDER "arcpkg")
endfunction(arcpkg_define)


# Generates installation rules for a package.
# Usage: arcpkg_install(
#   <user>-<name>
#   <FILES|DIRECTORY|PROGRAMS|CODE> <files|dirs|code>...
#   DESTINATION <dir>
#   ... # more install() arguments
# )
# NOTE: support more arguments from cmake install() command.
function(arcpkg_install PKG_TARGET TYPE)
  cmake_parse_arguments(A "" "DESTINATION;CODE" "FILES;DIRECTORY;PROGRAMS" "${TYPE};${ARGN}")
  arcpkg_get_property_target(PROP_TARGET ${PKG_TARGET})
  get_target_property(PKG_INSTALL_DIR ${PROP_TARGET} ARCPKG_INSTALL_DIR)
  if(A_DESTINATION STREQUAL "." OR A_DESTINATION STREQUAL ".")
    set(DESTINATION "${PKG_INSTALL_DIR}")
  else()
    set(DESTINATION "${PKG_INSTALL_DIR}/${A_DESTINATION}")
  endif()
  set(TYPE_SOURCES "${A_${TYPE}}")
  install(${TYPE} ${TYPE_SOURCES} DESTINATION "${DESTINATION}" COMPONENT ${PKG_TARGET} ${A_UNPARSED_ARGUMENTS})
endfunction(arcpkg_install)


###########################################################
# Query version and deploy.
###########################################################

function(arcpkg_query_remote_deploys DEPLOYS_VAR PKG_USER PKG_NAME PKG_VERSION)
  set(ROOT_DIR "${PKG_USER}/${PKG_NAME}")
  if(Python_EXECUTABLE)
    arcpkg_jfrog_artifactory_aql(DEPLOYS_JSON [=[
items.find({
  "repo": "${ARTIFACTORY_REPO}",
  "$or": [
    {"path": {"$match": "${ROOT_DIR}/${PKG_VERSION}"}},
    {"$and": [
      {"path": "${ROOT_DIR}"},
      {"name": {"$match": "${PKG_NAME}-${PKG_VERSION}*.zip"}}
    ]}
  ]
}).include("repo", "name", "path")
]=])
    string(REPLACE "\",\n  \"name\" : \"" "/" DEPLOYS_JSON "${DEPLOYS_JSON}") # join <name>/<path>
  else()
    arcpkg_remote_pattern_search(DEPLOYS_JSON "${ROOT_DIR}/${PKG_VERSION}/*.zip")
    if(NOT DEPLOYS_JSON MATCHES "([^*\"]+)\\.zip")
      arcpkg_remote_pattern_search(DEPLOYS_JSON "${ROOT_DIR}/${PKG_NAME}-${PKG_VERSION}*.zip")
    endif()
  endif()
  string(REGEX MATCHALL "([^*\"]+)\\.zip" DEPLOYS "${DEPLOYS_JSON}")
  list(FILTER DEPLOYS EXCLUDE REGEX "-map.zip$")
  list(TRANSFORM DEPLOYS REPLACE "\\.zip$" "")
  set(${DEPLOYS_VAR} ${DEPLOYS} PARENT_SCOPE)
endfunction(arcpkg_query_remote_deploys)


# Query pkg from ARCPKG_PATH.
function(arcpkg_query_custom_local_deploys DEPLOYS_VAR PKG_USER PKG_NAME PKG_VERSION)
  set(DEPLOYS)
  foreach(ROOT ${ARCPKG_PATH})
    set(PKG_ROOT "${ROOT}/${PKG_USER}/${PKG_NAME}")
    if(NOT EXISTS "${PKG_ROOT}")
      continue()
    endif()
    file(GLOB FILES LIST_DIRECTORIES true RELATIVE "${ROOT}" "${PKG_ROOT}/${PKG_VERSION}/*" "${PKG_ROOT}/${PKG_NAME}-${PKG_VERSION}*")
    foreach(X ${FILES})
      if(IS_DIRECTORY "${ROOT}/${X}" AND NOT X MATCHES "-map$")
        list(APPEND DEPLOYS ${X})
      endif()
    endforeach()
    if(NOT "${DEPLOYS}" STREQUAL "")
      break()
    endif()
  endforeach()
  set(${DEPLOYS_VAR} ${DEPLOYS} PARENT_SCOPE)
endfunction(arcpkg_query_custom_local_deploys)


function(arcpkg_query_local_deploys DEPLOYS_VAR PKG_USER PKG_NAME PKG_VERSION)
  set(PKG_ROOT "${ARCPKG_ROOT}/${PKG_USER}/${PKG_NAME}")
  file(GLOB DEPLOYS RELATIVE "${ARCPKG_ROOT}" "${PKG_ROOT}/${PKG_VERSION}/*.ok")
  if("${DEPLOYS}" STREQUAL "")
    file(GLOB DEPLOYS RELATIVE "${ARCPKG_ROOT}" "${PKG_ROOT}/${PKG_NAME}-${PKG_VERSION}*.ok")
  endif()
  list(FILTER DEPLOYS EXCLUDE REGEX "-map.ok$")
  list(TRANSFORM DEPLOYS REPLACE "\\.ok$" "")
  set(${DEPLOYS_VAR} ${DEPLOYS} PARENT_SCOPE)
endfunction(arcpkg_query_local_deploys)


function(arcpkg_get_local_pkg_abs_path PATH_ABS_VAR PATH)
  foreach(ROOT ${ARCPKG_PATH} "${ARCPKG_ROOT}")
    set(PATH_ABS "${ROOT}/${PATH}")
    if(EXISTS "${PATH_ABS}")
      set(${PATH_ABS_VAR} "${PATH_ABS}" PARENT_SCOPE)
      return()
    endif()
  endforeach()
  set(${PATH_ABS_VAR} PARENT_SCOPE)
endfunction(arcpkg_get_local_pkg_abs_path)


# Parse multiple architectures from pkg deploy name for Apple universal binary.
function(arcpkg_parse_multi_arch ARCH_VAR DEPLOY SEP PKG_NAME PKG_VERSION PLATFORM)
  # Parse ARCH
  set(ARCH)
  foreach(X common i386 i686 x86_64 armv7 armv7s arm64 arm64e)
    if(DEPLOY MATCHES "${SEP}${X}${SEP}" OR DEPLOY MATCHES "${SEP}${X}$")
      list(APPEND ARCH ${X})
    endif()
  endforeach()
  set(${ARCH_VAR} ${ARCH} PARENT_SCOPE)
endfunction(arcpkg_parse_multi_arch)


# Find best deploy.
function(arcpkg_get_best_deploy BEST_DEPLOY_VAR DEPLOYS PLATFORM ARCH PKG)
  # Early return.
  if("${DEPLOYS}" STREQUAL "")
    set(${BEST_DEPLOY_VAR} PARENT_SCOPE)
    return()
  endif()

  set(PKG_NAME ${${PKG}_NAME})
  set(PKG_VERSION ${${PKG}_VERSION})
  arcpkg_escape_for_regex("${PKG_VERSION}" PKG_VERSION_REGEX)

  # LITE_DEPLOYS
  if(DEPLOYS MATCHES "/${PKG_NAME}/${PKG_VERSION_REGEX}/${PKG_NAME}-${PKG_VERSION_REGEX}-")
    # /<name>/<version>/<name>-<version>-<platform>...
    set(SEP "-")
    set(PREFIX_PATTERN "/${PKG_NAME}/${PKG_VERSION_REGEX}/${PKG_NAME}-${PKG_VERSION_REGEX}-")
  elseif(DEPLOYS MATCHES "/${PKG_NAME}/${PKG_NAME}-${PKG_VERSION_REGEX}-")
    # /<name>/<name>-<version>-<platform>... (old style)
    set(SEP "-")
    set(PREFIX_PATTERN "/${PKG_NAME}/${PKG_NAME}-${PKG_VERSION_REGEX}-")
  elseif(DEPLOYS MATCHES "/${PKG_NAME}/${PKG_VERSION_REGEX}/${PKG_NAME}([-_])${PKG_VERSION_REGEX}$")
    # /arcpkg/Eigen/3.3.6/Eigen_3.3.6
    set(SEP ${CMAKE_MATCH_1})
    set(PREFIX_PATTERN "/${PKG_NAME}/${PKG_VERSION_REGEX}/")
  else()
    # <name>/<version>/<platform>...
    set(SEP "[-_]")
    set(PREFIX_PATTERN "/${PKG_NAME}/${PKG_VERSION_REGEX}/")
  endif()
  arcpkg_list_replace(".*${PREFIX_PATTERN}" "" LITE_DEPLOYS ${DEPLOYS})

  # message("DEPLOYS: ${DEPLOYS}")
  # message("PREFIX_PATTERN: ${PREFIX_PATTERN}")
  # message("SEP: ${SEP}")
  # message("LITE_DEPLOYS: ${LITE_DEPLOYS}")

  # Early return.
  if("${LITE_DEPLOYS}" STREQUAL "")
    set(${BEST_DEPLOY_VAR} PARENT_SCOPE)
    return()
  endif()

  # Is it Windows, Apple, or TDA4?
  if(PLATFORM MATCHES "^vs20[0-2][0-9]$" OR PLATFORM MATCHES "^(vc6|uwp|windows)$")
    set(IS_WINDOWS 1)
  elseif(PLATFORM MATCHES "^(ios|mac)$")
    set(IS_APPLE 1)
  elseif(PLATFORM MATCHES "^(tda4|tda4vm|tda4al-ve|tda4vh|am62a|lq560v200)$")
    set(IS_TDA4 1)
  endif()

  # Filter by platform
  if(IS_WINDOWS)
    set(PLATFORM_DEPLOYS)
    foreach(D ${LITE_DEPLOYS})
      if(D MATCHES "^vs20[012][0-9]${SEP}" OR D MATCHES "^(windows|win32|win64|vc6)${SEP}")
        list(APPEND PLATFORM_DEPLOYS ${D})
      endif()
    endforeach(D)
  elseif(IS_TDA4)
    set(PLATFORM_DEPLOYS)
    foreach(D ${LITE_DEPLOYS})
      if(D MATCHES "^(tda4|tda4vm|tda4al-ve|tda4vh|am62a|lq560v200)-aarch64(${SEP}|$)")
        list(APPEND PLATFORM_DEPLOYS ${D})
      endif()
    endforeach()
  else()
    arcpkg_list_filter("^${PLATFORM}${SEP}" PLATFORM_DEPLOYS ${LITE_DEPLOYS})
  endif()
  # message("PLATFORM_DEPLOYS: ${PLATFORM_DEPLOYS}")

  # Filter by arch
  set(DEPLOY_CANDIDATES)
  if(IS_WINDOWS AND ARCH)
    # https://stackoverflow.com/questions/44288837/binary-compatibility-between-vs2017-and-vs2015
    # https://docs.microsoft.com/en-us/cpp/porting/binary-compat-2015-2017?view=msvc-170
    # The compiler toolsets in Visual Studio 2013 and earlier don't guarantee binary compatibility across versions.
    # The runtime libraries and apps compiled by Visual Studio 2015, 2017, 2019 and 2022 are binary-compatible.
    # Split into static and non-static deploys.
    set(STATIC_DEPLOY_CANDIDATES)
    set(STATIC_DEPLOY_CANDIDATES_MT)
    set(NON_STATIC_DEPLOY_CANDIDATES)
    foreach(D ${PLATFORM_DEPLOYS})
      if(D MATCHES "^[^-_]+${SEP}${ARCH}(${SEP}|$)")
        if(D MATCHES "${SEP}static(${SEP}|$)")
          if(D MATCHES "${SEP}mt(${SEP}|$)")
            list(APPEND STATIC_DEPLOY_CANDIDATES_MT ${D})
          else()
            list(APPEND STATIC_DEPLOY_CANDIDATES ${D})
          endif()
        else()
          list(APPEND NON_STATIC_DEPLOY_CANDIDATES ${D})
        endif()
      endif()
    endforeach()
    # message("STATIC_DEPLOY_CANDIDATES: ${STATIC_DEPLOY_CANDIDATES}")
    # message("STATIC_DEPLOY_CANDIDATES_MT: ${STATIC_DEPLOY_CANDIDATES_MT}")
    # message("NON_STATIC_DEPLOY_CANDIDATES: ${NON_STATIC_DEPLOY_CANDIDATES}")
    # Filter static libraries
    if(PLATFORM STREQUAL "vs2017")
      set(VS_VERSION_PATTERN "vs201[57]")
    elseif(PLATFORM STREQUAL "vs2019")
      set(VS_VERSION_PATTERN "vs201[579]")
    elseif(PLATFORM STREQUAL "vs2022")
      set(VS_VERSION_PATTERN "vs20(15|17|19|22)")
    else()
      set(VS_VERSION_PATTERN "${PLATFORM}")
    endif()
    arcpkg_list_filter("^${VS_VERSION_PATTERN}${SEP}" STATIC_DEPLOY_CANDIDATES ${STATIC_DEPLOY_CANDIDATES})
    arcpkg_list_filter("^${VS_VERSION_PATTERN}${SEP}" STATIC_DEPLOY_CANDIDATES_MT ${STATIC_DEPLOY_CANDIDATES_MT})
    arcpkg_is_mt(IS_MT)
    if(IS_MT)
      set(DEPLOY_CANDIDATES ${STATIC_DEPLOY_CANDIDATES_MT})
    else()
      set(DEPLOY_CANDIDATES ${STATIC_DEPLOY_CANDIDATES})
    endif()
    list(APPEND DEPLOY_CANDIDATES ${NON_STATIC_DEPLOY_CANDIDATES})
  elseif(IS_APPLE AND ARCH)
    # TODO: Could bitcode prebuilt libraries be linked in non-bitcode targets?
    arcpkg_is_bitcode(IS_BITCODE)
    if(IS_BITCODE)
      arcpkg_list_filter("${SEP}bitcode(${SEP}|$)" PLATFORM_DEPLOYS ${PLATFORM_DEPLOYS})
    endif()
    # All building architectures should be supplied in prebuilt package.
    foreach(D ${PLATFORM_DEPLOYS})
      arcpkg_parse_multi_arch(ABI_ARCH ${D} ${SEP} ${PKG_NAME} ${PKG_VERSION} ${PLATFORM})
      # message("${D}: ${ABI_ARCH}")
      set(ARCH_MATCHED ON)
      if(NOT ABI_ARCH STREQUAL "common")
        foreach(A ${ARCH})
          if(NOT A IN_LIST ABI_ARCH)
            set(ARCH_MATCHED OFF)
            break()
          endif()
        endforeach()
      endif()
      if(ARCH_MATCHED)
        list(APPEND DEPLOY_CANDIDATES ${D})
      endif()
    endforeach()
  elseif(IS_TDA4)
    if(PLATFORM MATCHES "^(tda4vm|tda4al-ve|tda4vh|am62a|lq560v200)$")
      arcpkg_list_filter("^(tda4|${PLATFORM})(${SEP}|$)" DEPLOY_CANDIDATES ${PLATFORM_DEPLOYS})
    elseif(PLATFORM STREQUAL "tda4")
      arcpkg_list_filter("^tda4(${SEP}|$)" DEPLOY_CANDIDATES ${PLATFORM_DEPLOYS})
    endif()
  elseif(ARCH)
    arcpkg_list_filter("^${PLATFORM}${SEP}${ARCH}(${SEP}|$)" DEPLOY_CANDIDATES ${PLATFORM_DEPLOYS})
  else()
    set(DEPLOY_CANDIDATES ${PLATFORM_DEPLOYS}) # for emcc or :host without arch
  endif()

  # Prefer the oldest deploys.
  list(SORT DEPLOY_CANDIDATES)

  # Prefer the closest deploy for VS and TDA4.
  if(IS_WINDOWS OR IS_TDA4)
    set(NEW_DEPLOY_CANDIDATES)
    foreach(D ${DEPLOY_CANDIDATES})
      if(D MATCHES "^${PLATFORM}${SEP}")
        list(INSERT NEW_DEPLOY_CANDIDATES 0 ${D})
      else()
        list(APPEND NEW_DEPLOY_CANDIDATES ${D})
      endif()
    endforeach()
    set(DEPLOY_CANDIDATES ${NEW_DEPLOY_CANDIDATES})
  endif()
  # message("DEPLOY_CANDIDATES: ${DEPLOY_CANDIDATES}")

  # Try to filter with HINTS.
  if(DEFINED ${PKG}_HINTS_FORCE)
    set(PKG_HINTS ${${PKG}_HINTS_FORCE})
    set(EXACT_MODE ON)
  else()
    set(PKG_HINTS ${${PKG}_HINTS})
    set(EXACT_MODE ${ARCPKG_EXACT_MODE})
  endif()
  # message("PKG_HINTS: ${PKG_HINTS} (EXACT=${EXACT_MODE})")
  set(BEST_DEPLOY)
  if(EXACT_MODE)
    foreach(D ${DEPLOY_CANDIDATES})
      set(MATCHED ON)
      foreach(H ${PKG_HINTS})
        if(NOT (D MATCHES "${SEP}${H}(${SEP}|$)"))
          set(MATCHED OFF)
          break()
        endif()
      endforeach(H)
      if(MATCHED)
        set(BEST_DEPLOY ${D})
        break()
      endif()
    endforeach(D)
  else()
    if(NOT "shared" IN_LIST PKG_HINTS)
      list(APPEND PKG_HINTS "static")
    endif()
    set(NUM_MATCHED_HINTS_MAX -1)
    foreach(D ${DEPLOY_CANDIDATES})
      set(NUM_MATCHED_HINTS 0)
      foreach(H ${PKG_HINTS})
        if(D MATCHES "${SEP}${H}(${SEP}|$)")
          math(EXPR NUM_MATCHED_HINTS "${NUM_MATCHED_HINTS}+1")
        endif()
      endforeach(H)
      if(NUM_MATCHED_HINTS GREATER NUM_MATCHED_HINTS_MAX)
        set(BEST_DEPLOY ${D})
        set(NUM_MATCHED_HINTS_MAX ${NUM_MATCHED_HINTS})
      endif()
    endforeach(D)
  endif()

  if(NOT BEST_DEPLOY)
    # Fallback to platform-independent package (`<name>-<version>`).
    arcpkg_list_get_first_by_regex("/${PKG_NAME}[-_]${PKG_VERSION_REGEX}$" BEST_DEPLOY ${DEPLOYS})
  else()
    # Convert from lite deploy to original deploy.
    arcpkg_list_get_first_by_regex("${PREFIX_PATTERN}${BEST_DEPLOY}$" BEST_DEPLOY ${DEPLOYS})
  endif()

  if(NOT BEST_DEPLOY)
    arcpkg_warn("  Fail to find deploy (EXACT=${EXACT_MODE}) from: ${LITE_DEPLOYS}")
  endif()

  set(${BEST_DEPLOY_VAR} ${BEST_DEPLOY} PARENT_SCOPE)
endfunction(arcpkg_get_best_deploy)


# Clean target paths in <name>-targets.cmake.
function(arcpkg_clean_paths_in_targets_dot_cmake TARGET_FILE INSTALL_DIR PKG_USER PKG_NAME)
  file(READ ${TARGET_FILE} CONTENT)
  string(PREPEND CONTENT "# Modified by arcpkg.cmake (${ARCPKG_VERSION})\n\n")
  if(TARGET_FILE MATCHES "targets.cmake$")
    string(REGEX REPLACE "get_filename_component\\(_IMPORT_PREFIX[^)]+\\)[\r\n]+" "##" CONTENT "${CONTENT}")
    string(REGEX REPLACE "##+" "get_filename_component(_IMPORT_PREFIX \"\${CMAKE_CURRENT_LIST_DIR}\" PATH)\n" CONTENT "${CONTENT}")
    if(ARCPKG_EXPORT_GLOBAL)
      string(REGEX REPLACE "(add_library|add_executable)\\(([^)]+) IMPORTED\\)" "\\1(\\2 IMPORTED GLOBAL)" CONTENT "${CONTENT}")
    endif()
  endif()
  if(NOT INSTALL_DIR)
    if(CONTENT MATCHES "\{_IMPORT_PREFIX\}/(${PKG_USER}/${PKG_NAME}/[^\"]+)/(inc|include)\"")
      set(INSTALL_DIR ${CMAKE_MATCH_1})
    elseif(CONTENT MATCHES "\{_IMPORT_PREFIX\}/(${PKG_USER}/${PKG_NAME}/[^\"]+)/(lib|bin)/")
      set(INSTALL_DIR ${CMAKE_MATCH_1})
    endif()
  endif()
  if(INSTALL_DIR)
    string(REPLACE "${INSTALL_DIR}/" "" CONTENT "${CONTENT}")
  endif()
  file(WRITE ${TARGET_FILE} "${CONTENT}")
endfunction(arcpkg_clean_paths_in_targets_dot_cmake)


# Clean export files
function(arcpkg_clean_export_files ROOT_DIR INSTALL_DIR PKG_USER PKG_NAME)
  if(INSTALL_DIR)
    set(INSTALL_DIR_ABS "${ROOT_DIR}/${INSTALL_DIR}")
  else()
    set(INSTALL_DIR_ABS "${ROOT_DIR}")
  endif()
  file(GLOB TARGET_FILES "${INSTALL_DIR_ABS}/cmake/${PKG_NAME}-targets*.cmake")
  if(NOT TARGET_FILES)
    return()
  endif()
  arcpkg_warn("Clean export files in ${INSTALL_DIR_ABS}/cmake")
  foreach(TARGET_FILE ${TARGET_FILES})
    arcpkg_warn("- ${TARGET_FILE}")
    arcpkg_clean_paths_in_targets_dot_cmake("${TARGET_FILE}" "${INSTALL_DIR}" ${PKG_USER} ${PKG_NAME})
  endforeach()
endfunction(arcpkg_clean_export_files)


# Upgrade installed package
function(arcpkg_upgrade_pkg PKG_DIR PKG_USER PKG_NAME)
  arcpkg_warn("Upgrading pkg in ${PKG_DIR}")
  file(GLOB NAMES RELATIVE "${PKG_DIR}" "${PKG_DIR}/${PKG_NAME}-*.cmake")
  if(NAMES)
    file(MAKE_DIRECTORY "${PKG_DIR}/cmake")
    foreach(X ${NAMES})
      file(RENAME "${PKG_DIR}/${X}" "${PKG_DIR}/cmake/${X}")
    endforeach()
  endif()
  arcpkg_clean_export_files("${PKG_DIR}" "" ${PKG_USER} ${PKG_NAME})
  set(PKG_INFO_FILE "${PKG_DIR}/arcpkg.txt")
  if(EXISTS ${PKG_INFO_FILE})
    arcpkg_warn("- ${PKG_INFO_FILE}")
    file(READ ${PKG_INFO_FILE} CONTENT)
    string(REGEX REPLACE "Pkg ?Version: ([^\r\n]+)" "Pkg Version: ${ARCPKG_VERSION} (upgraded from \\1)" CONTENT "${CONTENT}")
    file(WRITE ${PKG_INFO_FILE} "${CONTENT}")
  endif()
endfunction(arcpkg_upgrade_pkg)


# Install and export a package from remote to local cache.
# Note: requires PKG_RECIPE variable from arcpkg_import() or install command.
# Targets:
#   - arcpkg-update
#   - <user>-<name>-update
function(arcpkg_install_pkg PKG_DIR_VAR PKG_INSTALL_DIR_VAR PKG PLATFORM ARCH)
  if(${PKG}_HOST)
    arcpkg_get_host_platform(PLATFORM)
    unset(ARCH)
  elseif(ARCPKG_LINUX_DISTRO AND NOT CMAKE_CROSSCOMPILING AND ${PKG}_DISTRO)
    set(PLATFORM ${ARCPKG_LINUX_DISTRO})
  endif()
  arcpkg_echo("Installing ${${PKG}_RECIPE} for ${PLATFORM}:${ARCH}")
  set(PKG_USER ${${PKG}_USER})
  set(PKG_NAME ${${PKG}_NAME})
  set(PKG_VERSION ${${PKG}_VERSION})
  set(ROOT_DIR ${PKG_USER}/${PKG_NAME})
  set(BEST_DEPLOY)
  set(USE_REMOTE_DEPLOY 0)
  unset(REMOTE_DEPLOYS)
  # Step 0: search pkg in ARCPKG_PATH.
  if(ARCPKG_PATH AND NOT ARCPKG_ONLY_REMOTE_PKG)
    arcpkg_echo("  Searching in ARCPKG_PATH: ${ARCPKG_PATH}")
    arcpkg_query_custom_local_deploys(CUSTOM_LOCAL_DEPLOYS ${PKG_USER} ${PKG_NAME} ${PKG_VERSION})
    arcpkg_get_best_deploy(BEST_DEPLOY "${CUSTOM_LOCAL_DEPLOYS}" "${PLATFORM}" "${ARCH}" ${PKG})
  endif()
  # Step 1: search pkg in remote repo.
  if(NOT BEST_DEPLOY AND ARCPKG_AUTO_UPDATE AND ARTIFACTORY_USER AND ARTIFACTORY_URL)
    arcpkg_echo("  Searching remote repo in ${ROOT_DIR}")
    arcpkg_query_remote_deploys(REMOTE_DEPLOYS ${PKG_USER} ${PKG_NAME} ${PKG_VERSION})
    arcpkg_get_best_deploy(BEST_DEPLOY "${REMOTE_DEPLOYS}" "${PLATFORM}" "${ARCH}" ${PKG})
    if(BEST_DEPLOY)
      set(USE_REMOTE_DEPLOY 1)
    endif()
  endif()
  # Step 2: search pkg in local cache.
  if(NOT BEST_DEPLOY)
    arcpkg_echo("  Searching local cache in ${ARCPKG_ROOT}/${ROOT_DIR}")
    arcpkg_query_local_deploys(LOCAL_DEPLOYS ${PKG_USER} ${PKG_NAME} ${PKG_VERSION})
    arcpkg_get_best_deploy(BEST_DEPLOY "${LOCAL_DEPLOYS}" "${PLATFORM}" "${ARCH}" ${PKG})
  endif()
  # Step 3: try to search remote repo when no local pkg is found.
  if(NOT BEST_DEPLOY AND NOT DEFINED REMOTE_DEPLOYS AND ARTIFACTORY_URL)
    arcpkg_echo("  Searching remote repo in ${ROOT_DIR}")
    arcpkg_query_remote_deploys(REMOTE_DEPLOYS ${PKG_USER} ${PKG_NAME} ${PKG_VERSION})
    arcpkg_get_best_deploy(BEST_DEPLOY "${REMOTE_DEPLOYS}" "${PLATFORM}" "${ARCH}" ${PKG})
    if(BEST_DEPLOY)
      set(USE_REMOTE_DEPLOY 1)
    endif()
  endif()
  if(NOT BEST_DEPLOY)
    arcpkg_get_pkg_versions(ERROR_CODE PKG_VERSIONS ${PKG_USER} ${PKG_NAME})
    # message("ERROR_CODE: ${ERROR_CODE}")
    # message("PKG_VERSIONS: ${PKG_VERSIONS}")
    if(ERROR_CODE EQUAL 403)
      arcpkg_error("User ${ARTIFACTORY_USER} has no permission to access ${PKG_NAME}@${PKG_USER} pkg!")
    elseif(ERROR_CODE EQUAL 404)
      arcpkg_error("There is no ${PKG_NAME}@${PKG_USER} pkg in remote repo!")
    elseif(NOT PKG_VERSION IN_LIST PKG_VERSIONS)
      arcpkg_error("There is no required version (${PKG_VERSION}), available versions are \"${PKG_VERSIONS}\"!")
    else()
      arcpkg_error("Could not find appropriate deploy for ${PLATFORM}:${ARCH} of ${PKG_RECIPE} pkg in remote repo or local repo!")
    endif()
  endif()
  arcpkg_debug("  Use best match deploy: ${BEST_DEPLOY}")
  # Install remote pkg if needed
  if(USE_REMOTE_DEPLOY)
    arcpkg_parse_deploy(PKG ${BEST_DEPLOY})
    arcpkg_get_local_install_dir(INSTALL_DIR PKG)
    arcpkg_install_remote_zip("${BEST_DEPLOY}.zip" "${INSTALL_DIR}")
  else()
    set(INSTALL_DIR ${BEST_DEPLOY})
  endif()
  arcpkg_get_local_pkg_abs_path(INSTALL_DIR_ABS "${INSTALL_DIR}")
  set(${PKG_DIR_VAR} "${INSTALL_DIR_ABS}" PARENT_SCOPE)
  set(${PKG_INSTALL_DIR_VAR} "${INSTALL_DIR}" PARENT_SCOPE)
  arcpkg_debug("  Use local cache: ${INSTALL_DIR_ABS}")
  # Create arcpkg-update targets
  if(NOT DEFINED CMAKE_SCRIPT_MODE_FILE)
    if(NOT TARGET arcpkg-update)
      add_custom_target(arcpkg-update)
      set_target_properties(arcpkg-update PROPERTIES FOLDER "arcpkg")
    endif()
    set(UPDATE_PKG_TARGET "${${PKG}_TARGET}-update")
    add_custom_target(
      ${UPDATE_PKG_TARGET}
      COMMAND ${ARCPKG_COMMAND} install "${PKG_RECIPE}" NO_REQUIRES -p "${PLATFORM}" -a \"${ARCH}\"
      COMMENT "Installing ${PKG_RECIPE} from remote"
    )
    add_dependencies(arcpkg-update ${UPDATE_PKG_TARGET})
    set_target_properties(${UPDATE_PKG_TARGET} PROPERTIES FOLDER "arcpkg")
  endif()
endfunction(arcpkg_install_pkg)


# Scan and make prebuilt targets in INSTALL_DIR.
# Format 1:
#   - (include|inc)/    Include directories
#   - lib/              Archive directory
#   - bin/              Runtime directory
# Format 2 (plain layout):
#   - foo.h             Include headers
#   - foo.lib           Archive
#   - foo.dll           Runtime
# NOTE: debug libraries are named as "<name>_d".
function(arcpkg_find_targets PKG_LIBS_VAR PKG INSTALL_DIR)
  set(PKG_NAME ${${PKG}_NAME})
  set(PKG_USER ${${PKG}_USER})
  set(PKG_RECIPE ${${PKG}_RECIPE})
  set(PKG_TARGET ${PKG_USER}::${PKG_NAME})

  # Glob INCS
  arcpkg_echo("Scanning \"${PKG_NAME}\" pkg in ${INSTALL_DIR}")
  foreach(X "include" "inc" ".")
    set(SHORT_INC_DIR ${X})
    set(INC_DIR "${INSTALL_DIR}/${X}")
    file(GLOB_RECURSE INCS "${INC_DIR}/*.h" "${INC_DIR}/*.hpp" "${INC_DIR}/*.hxx")
    if(INCS)
      break()
    endif()
  endforeach()
  if(NOT INCS)
    set(SHORT_INC_DIR)
    set(INC_DIR)
  endif()

  # Glob LIBS
  set(LIB_DIR "${INSTALL_DIR}/lib")
  file(GLOB_RECURSE LIBS "${LIB_DIR}/*.a" "${LIB_DIR}/*.so" "${LIB_DIR}/*.lib" "${LIB_DIR}/*.LIB" "${LIB_DIR}/*.dylib" "${LIB_DIR}/*.tbd" "${LIB_DIR}/*.bc")
  if(NOT LIBS)
    set(LIB_DIR "${INSTALL_DIR}")
    file(GLOB LIBS "${LIB_DIR}/*.a" "${LIB_DIR}/*.so" "${LIB_DIR}/*.lib" "${LIB_DIR}/*.LIB" "${LIB_DIR}/*.dylib" "${LIB_DIR}/*.tbd" "${LIB_DIR}/*.bc")
  endif()

  # Glob DLLS
  set(BIN_DIR "${INSTALL_DIR}/bin")
  file(GLOB_RECURSE DLLS "${LIB_DIR}/*.dll" "${LIB_DIR}/*.DLL" "${BIN_DIR}/*.dll" "${BIN_DIR}/*.DLL")
  if(NOT DLLS)
    set(BIN_DIR "${INSTALL_DIR}")
    file(GLOB DLLS "${LIB_DIR}/*.dll" "${LIB_DIR}/*.DLL" "${BIN_DIR}/*.dll" "${BIN_DIR}/*.DLL")
  endif()

  # Not a library
  if(NOT INCS AND NOT LIBS AND NOT DLLS)
    arcpkg_echo("  Create imported target ${PKG_TARGET} (INTERFACE): .")
    add_library(${PKG_TARGET} INTERFACE IMPORTED ${ARCPKG_EXPORT_GLOBAL})
    set_target_properties(${PKG_TARGET} PROPERTIES
      INTERFACE_INCLUDE_DIRECTORIES "${INSTALL_DIR}"
      INTERFACE_LINK_LIBRARIES "${${PKG}_REQUIRES_LIBS}"
    )
    set(${PKG_LIBS_VAR} ${PKG_TARGET} PARENT_SCOPE)
    return()
  endif()

  # Create header-only target
  if(NOT LIBS AND NOT DLLS)
    arcpkg_echo("  Create imported target ${PKG_TARGET} (INTERFACE): ${SHORT_INC_DIR}")
    add_library(${PKG_TARGET} INTERFACE IMPORTED ${ARCPKG_EXPORT_GLOBAL})
    set_target_properties(${PKG_TARGET} PROPERTIES
      INTERFACE_INCLUDE_DIRECTORIES "${INC_DIR}"
      INTERFACE_LINK_LIBRARIES "${${PKG}_REQUIRES_LIBS}"
    )
    set(${PKG_LIBS_VAR} ${PKG_TARGET} PARENT_SCOPE)
    return()
  endif()

  # Filter debug libraries
  arcpkg_list_filter("_d\.(lib|LIB|a|so|dylib|tbd)$" LIBS_DEBUG ${LIBS})
  arcpkg_list_filter("_d\.(dll|DLL)$" DLLS_DEBUG ${DLLS})
  list(REMOVE_ITEM LIBS ${LIBS_DEBUG} "")
  list(REMOVE_ITEM DLLS ${DLLS_DEBUG} "")
  # message("LIBS: ${LIBS}")
  # message("LIBS_DEBUG: ${LIBS_DEBUG}")
  # message("DLLS: ${DLLS}")
  # message("DLLS_DEBUG: ${DLLS_DEBUG}")

  # Get LIB_NAMES and <x>_(LIB|DLL)(|_DEBUG)
  set(LIB_NAMES)
  set(MODULE_NAMES)
  set(MAIN_LIB_NAME)
  foreach(X ${LIBS} ${DLLS})
    get_filename_component(X_NAME0 "${X}" NAME_WE)
    if(X MATCHES "\.(lib|LIB|dll|DLL)$")
      set(X_NAME ${X_NAME0})
    else()
      string(REGEX REPLACE "^lib" "" X_NAME ${X_NAME0})
    endif()
    if(X_NAME IN_LIST LIB_NAMES OR X_NAME IN_LIST MODULE_NAMES)
      continue()
    endif()
    string(REGEX REPLACE "\\+" "\\\\+" X_NAME0_ESCAPED ${X_NAME0})
    # Set _LIB, _LIB_DEBUG, _DLL, _DLL_DEBUG
    arcpkg_list_get_first_by_regex("/${X_NAME0_ESCAPED}\.(lib|LIB|a|so|dylib|tbd|bc)$" ${X_NAME}_LIB ${LIBS})
    arcpkg_list_get_first_by_regex("/${X_NAME0_ESCAPED}_d\.(lib|LIB|a|so|dylib|tbd|bc)$" ${X_NAME}_LIB_DEBUG ${LIBS_DEBUG})
    if(X MATCHES "\.(lib|LIB|dll|DLL)$")
      arcpkg_list_get_first_by_regex("/${X_NAME0_ESCAPED}\.(dll|DLL)$" ${X_NAME}_DLL ${DLLS})
      arcpkg_list_get_first_by_regex("/${X_NAME0_ESCAPED}_d\.(dll|DLL)$" ${X_NAME}_DLL_DEBUG ${DLLS_DEBUG})
      list(REMOVE_ITEM DLLS ${${X_NAME}_DLL} "")
      list(REMOVE_ITEM DLLS_DEBUG ${${X_NAME}_DLL_DEBUG} "")
    endif()
    # Check
    if(${X_NAME}_LIB_DEBUG AND NOT ${X_NAME}_LIB)
      arcpkg_error("There is debug LIB (${${X_NAME}_LIB_DEBUG} but not release LIB!")
    endif()
    if(${X_NAME}_DLL_DEBUG AND NOT ${X_NAME}_DLL)
      arcpkg_error("There is debug DLL (${${X_NAME}_DLL_DEBUG} but not release DLL!")
    endif()
    # LIBS or MODULES
    if(${X_NAME}_LIB)
      list(APPEND LIB_NAMES ${X_NAME})
      # MAIN_LIB_NAME
      if(X_NAME STREQUAL PKG_NAME)
        set(MAIN_LIB_NAME ${PKG_NAME})
      endif()
    else()
      list(APPEND MODULE_NAMES ${X_NAME})
    endif()
  endforeach()
  set(LIB_NAMES ${MODULE_NAMES} ${LIB_NAMES})

  # Adjust the LIBS link order by ${PKG}_REQUIRES_LIBS
  # REQUIRED_LIBS: REQUIRES not in LIB_NAMES
  set(REQUIRED_LIBS ${${PKG}_REQUIRES_LIBS})
  list(REMOVE_ITEM REQUIRED_LIBS ${LIB_NAMES} "")
  set(REQUIRED_LIBS_SCANNED ${${PKG}_REQUIRES_LIBS})
  list(REMOVE_ITEM REQUIRED_LIBS_SCANNED ${REQUIRED_LIBS} "")
  list(REMOVE_ITEM LIB_NAMES ${REQUIRED_LIBS_SCANNED} ${MAIN_LIB_NAME} "")
  set(LIB_NAMES ${REQUIRED_LIBS_SCANNED} ${LIB_NAMES} ${MAIN_LIB_NAME}) # Order the LIBS
  # message("REQUIRED_LIBS_SCANNED: ${REQUIRED_LIBS_SCANNED}")
  # message("LIB_NAMES: ${LIB_NAMES}")
  # message("REQUIRED_LIBS: ${REQUIRED_LIBS}")

  if(NOT MAIN_LIB_NAME)
    list(GET LIB_NAMES -1 MAIN_LIB_NAME)
    if(MAIN_LIB_NAME IN_LIST MODULE_NAMES)
      set(MAIN_LIB_NAME)
      arcpkg_error("Could not create pkg with DLL's only!")
    endif()
  endif()

  # Create targets
  set(PKG_LIBS)
  set(PKG_IMPORTED_LOCATION)
  set(PKG_IMPORTED_LOCATION_DEBUG)
  foreach(X ${LIB_NAMES})
    set(X_LIB ${${X}_LIB})
    set(X_DLL ${${X}_DLL})
    set(X_LIB_DEBUG ${${X}_LIB_DEBUG})
    set(X_DLL_DEBUG ${${X}_DLL_DEBUG})
    if(X_DLL OR (X STREQUAL MAIN_LIB_NAME AND PKG_IMPORTED_LOCATION))
      if(X_LIB)
        set(X_TYPE SHARED)
      else()
        set(X_TYPE MODULE) # dll-only
      endif()
      set(IMPORTED_IMPLIB ${X_LIB})
      set(IMPORTED_LOCATION ${X_DLL})
      set(IMPORTED_IMPLIB_DEBUG ${X_LIB_DEBUG})
      set(IMPORTED_LOCATION_DEBUG ${X_DLL_DEBUG})
    else()
      if(X_LIB MATCHES "\\.(so|dylib|tbd)$")
        set(X_TYPE SHARED)
      else()
        set(X_TYPE STATIC)
      endif()
      set(IMPORTED_IMPLIB)
      set(IMPORTED_LOCATION ${X_LIB})
      set(IMPORTED_IMPLIB_DEBUG)
      set(IMPORTED_LOCATION_DEBUG ${X_LIB_DEBUG})
    endif()
    if(X STREQUAL PKG_NAME)
      set(X_TARGET ${PKG_USER}::${X})
      set(INTERFACE_LINK_LIBRARIES ${REQUIRED_LIBS} ${PKG_LIBS})
    else()
      set(X_TARGET ${PKG_USER}::${PKG_NAME}::${X})
      set(INTERFACE_LINK_LIBRARIES ${REQUIRED_LIBS})
    endif()
    if(X STREQUAL MAIN_LIB_NAME)
      list(APPEND IMPORTED_LOCATION ${PKG_IMPORTED_LOCATION})
      if(IMPORTED_LOCATION_DEBUG OR IMPORTED_IMPLIB_DEBUG) # Add more only when there is debug LIB
        list(APPEND IMPORTED_LOCATION_DEBUG ${PKG_IMPORTED_LOCATION_DEBUG})
      endif()
    endif()
    # message("IMPORTED_IMPLIB: ${IMPORTED_IMPLIB}")
    # message("IMPORTED_LOCATION: ${IMPORTED_LOCATION}")
    # message("INTERFACE_LINK_LIBRARIES: ${INTERFACE_LINK_LIBRARIES}")
    if(X_TYPE STREQUAL "MODULE")
      list(APPEND PKG_IMPORTED_LOCATION ${IMPORTED_LOCATION})
      if(IMPORTED_LOCATION_DEBUG)
        list(APPEND PKG_IMPORTED_LOCATION_DEBUG ${IMPORTED_LOCATION_DEBUG})
      else()
        list(APPEND PKG_IMPORTED_LOCATION_DEBUG ${IMPORTED_LOCATION}) # add release DLL's for debug
      endif()
      continue() # Add DLL's to main lib
    else()
      list(APPEND PKG_LIBS ${X_TARGET})
    endif()
    # Create normal ${X_TARGET} target
    set(LIBS_VIZ ${IMPORTED_IMPLIB} ${IMPORTED_LOCATION})
    string(REPLACE "${LIB_DIR}/" "" LIBS_VIZ "${LIBS_VIZ}")
    string(REPLACE "${BIN_DIR}/" "" LIBS_VIZ "${LIBS_VIZ}")
    arcpkg_echo("  Create ${X_TARGET} (${X_TYPE}): ${SHORT_INC_DIR}, ${LIBS_VIZ}")
    add_library(${X_TARGET} ${X_TYPE} IMPORTED ${ARCPKG_EXPORT_GLOBAL})
    set_target_properties(${X_TARGET} PROPERTIES
      INTERFACE_INCLUDE_DIRECTORIES "${INC_DIR}"
      IMPORTED_IMPLIB "${IMPORTED_IMPLIB}"
      IMPORTED_LOCATION "${IMPORTED_LOCATION}"
      INTERFACE_LINK_LIBRARIES "${INTERFACE_LINK_LIBRARIES}"
    )
    # Create debug ${X_TARGET} target
    if(IMPORTED_LOCATION_DEBUG)
      set(SUFFIX _DEBUG)
      set(LIBS_VIZ ${IMPORTED_IMPLIB${SUFFIX}} ${IMPORTED_LOCATION${SUFFIX}})
      string(REPLACE "${LIB_DIR}/" "" LIBS_VIZ "${LIBS_VIZ}")
      string(REPLACE "${BIN_DIR}/" "" LIBS_VIZ "${LIBS_VIZ}")
      arcpkg_echo("  - Add DEBUG ${X_TARGET}: ${LIBS_VIZ}")
      set_property(TARGET ${X_TARGET} APPEND PROPERTY IMPORTED_CONFIGURATIONS Debug)
      set_target_properties(${X_TARGET} PROPERTIES
        IMPORTED_IMPLIB${SUFFIX} "${IMPORTED_IMPLIB${SUFFIX}}"
        IMPORTED_LOCATION${SUFFIX} "${IMPORTED_LOCATION${SUFFIX}}"
        INTERFACE_LINK_LIBRARIES${SUFFIX} "${INTERFACE_LINK_LIBRARIES}"
      )
      set_target_properties(${X_TARGET} PROPERTIES
        MAP_IMPORTED_CONFIG_MINSIZEREL ""
        MAP_IMPORTED_CONFIG_RELWITHDEBINFO ""
      )
    endif()
  endforeach()
  if(PKG_LIBS AND NOT TARGET ${PKG_TARGET})
    set(X_TYPE INTERFACE)
    string(REPLACE "${PKG_TARGET}::" "" LIBS_VIZ "${PKG_LIBS}")
    arcpkg_echo("  Create ${PKG_TARGET} (${X_TYPE}): ${LIBS_VIZ}")
    add_library(${PKG_TARGET} ${X_TYPE} IMPORTED ${ARCPKG_EXPORT_GLOBAL})
    set_target_properties(${PKG_TARGET} PROPERTIES
      INTERFACE_INCLUDE_DIRECTORIES "${INC_DIR}"
      INTERFACE_LINK_LIBRARIES "${PKG_LIBS}"
    )
    list(APPEND PKG_LIBS ${PKG_TARGET})
  endif()
  set(${PKG_LIBS_VAR} ${PKG_LIBS} PARENT_SCOPE)
endfunction()


function(arcpkg_find_host_executables PKG_BINS_VAR PKG CONFIG_DIR)
  set(PKG_NAME ${${PKG}_NAME})
  # Scan <name>-targets.cmake
  set(TARGET_FILES
    "${CONFIG_DIR}/${PKG_NAME}-targets.cmake"
    "${CONFIG_DIR}/${PKG_NAME}.cmake")
  unset(BIN_TARGETS)
  foreach(X ${TARGET_FILES})
    if(EXISTS ${X})
      file(STRINGS ${X} BIN_TARGETS_LINES REGEX "^add_executable\\(")
      string(REGEX REPLACE "add_executable\\(([^ ]+)[^\\)]+\\)" "\\1" BIN_TARGETS "${BIN_TARGETS_LINES}")
      break()
    endif()
  endforeach()
  if(NOT BIN_TARGETS)
    return()
  endif()
  # Scan <name>-targets-release.cmake
  set(CFG_FILES
    "${CONFIG_DIR}/${PKG_NAME}-targets-release.cmake"
    "${CONFIG_DIR}/${PKG_NAME}-targets-debug.cmake"
    "${CONFIG_DIR}/${PKG_NAME}-release.cmake"
    "${CONFIG_DIR}/${PKG_NAME}-debug.cmake")
  set(IMPORT_PREFIX ${${PKG}_DIR})
  foreach(CFG_FILE ${CFG_FILES})
    if(NOT EXISTS ${CFG_FILE})
      continue()
    endif()
    arcpkg_debug("Scanning host executables in ${CFG_FILE}")
    file(STRINGS ${CFG_FILE} BIN_LOCATION_LINES REGEX "^list\\(APPEND (_cmake_import_check_files_for|_IMPORT_CHECK_FILES_FOR)_")
    set(PKG_BINS)
    foreach(LINE ${BIN_LOCATION_LINES})
      if(LINE MATCHES "(_cmake_import_check_files_for|_IMPORT_CHECK_FILES_FOR)_([^ ]+) \"\\\$\\\{_IMPORT_PREFIX\\\}/([^\"]+)")
        if(NOT CMAKE_MATCH_2 IN_LIST BIN_TARGETS)
          continue()
        endif()
        set(NAME "host::${CMAKE_MATCH_2}")
        set(LOCATION "${IMPORT_PREFIX}/${CMAKE_MATCH_3}")
        arcpkg_echo("- Add ${NAME}: ${LOCATION}")
        add_executable(${NAME} IMPORTED)
        set_target_properties(${NAME} PROPERTIES IMPORTED_LOCATION "${LOCATION}")
        list(APPEND PKG_BINS ${NAME})
      endif()
    endforeach()
    set(${PKG_BINS_VAR} ${PKG_BINS} PARENT_SCOPE)
    break()
  endforeach()
endfunction(arcpkg_find_host_executables)


# See arcpkg_find_package_end()
macro(arcpkg_find_package_begin)
  set(${PKG_NAME}_DIR_OLD ${${PKG_NAME}_DIR})
  set(${PKG_NAME}_LIBS_OLD ${${PKG_NAME}_LIBS})
  # https://stackoverflow.com/questions/45479456/cmake-is-there-a-way-to-get-a-list-of-imported-targets-that-belong-to-a-package
  if(CMAKE_VERSION VERSION_GREATER_EQUAL "3.21")
    get_property(IMPORTED_TARGETS_BEFORE DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}" PROPERTY IMPORTED_TARGETS)
    get_property(BUILDSYSTEM_TARGETS_BEFORE DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}" PROPERTY BUILDSYSTEM_TARGETS)
  endif()
endmacro()


# See arcpkg_find_package_begin()
macro(arcpkg_find_package_end)
  if(${PKG_NAME}_LIBS) # e.g. OpenCV_LIBS
    set(NEW_IMPORTED_TARGETS ${${PKG_NAME}_LIBS} ${${PKG_NAME}_BINS})
  elseif(CMAKE_VERSION VERSION_GREATER_EQUAL "3.21")
    get_property(IMPORTED_TARGETS_AFTER DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}" PROPERTY IMPORTED_TARGETS)
    get_property(BUILDSYSTEM_TARGETS_AFTER DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}" PROPERTY BUILDSYSTEM_TARGETS)
    list(REMOVE_ITEM IMPORTED_TARGETS_AFTER ${IMPORTED_TARGETS_BEFORE} "")
    list(REMOVE_ITEM BUILDSYSTEM_TARGETS_AFTER ${BUILDSYSTEM_TARGETS_BEFORE} "")
    set(NEW_IMPORTED_TARGETS ${IMPORTED_TARGETS_AFTER} ${BUILDSYSTEM_TARGETS_AFTER})
  elseif(${PKG}_LIBS OR ${PKG}_BINS) # Use LIBS/BINS in arcpkg.txt
    set(NEW_IMPORTED_TARGETS ${${PKG}_LIBS} ${${PKG}_BINS})
  elseif(TARGET ${PKG_USER}::${PKG_NAME}) # default target
    set(NEW_IMPORTED_TARGETS ${PKG_USER}::${PKG_NAME})
  elseif(TARGET ${PKG_NAME}::${PKG_NAME}) # default target
    set(NEW_IMPORTED_TARGETS ${PKG_NAME}::${PKG_NAME})
  else()
    set(NEW_IMPORTED_TARGETS)
  endif()
  # Set _BINS and _LIBS
  unset(${PKG}_IMPORTED_LIBS)
  unset(${PKG}_IMPORTED_BINS)
  # message("NEW_IMPORTED_TARGETS: ${NEW_IMPORTED_TARGETS}")
  foreach(IMPORTED_TARGET ${NEW_IMPORTED_TARGETS})
    get_target_property(IMPORTED_TARTET_TYPE ${IMPORTED_TARGET} TYPE)
    if(IMPORTED_TARTET_TYPE STREQUAL "EXECUTABLE")
      list(APPEND ${PKG}_IMPORTED_BINS "${IMPORTED_TARGET}")
    else()
      list(APPEND ${PKG}_IMPORTED_LIBS "${IMPORTED_TARGET}")
    endif()
  endforeach()
  # Restore old states
  if(${PKG_NAME}_LIBS_OLD)
    set(${PKG_NAME}_LIBS ${${PKG_NAME}_LIBS_OLD})
  endif()
  unset(${PKG_NAME}_LIBS_OLD)
  unset(IMPORTED_TARGETS_BEFORE)
  unset(IMPORTED_TARGETS_AFTER)
  unset(BUILDSYSTEM_TARGETS_BEFORE)
  unset(BUILDSYSTEM_TARGETS_AFTER)
endmacro()


# find_package() and collect following variables:
# - ${PKG}_IMPORTED_LIBS
# - ${PKG}_IMPORTED_BINS
# NOTE: old ${PKG}_DIR, ${PKG}_LIBS and ${PKG}_BINS are saved and restored during importing.
macro(arcpkg_find_package CONFIG_DIR PKG_NAME)
  arcpkg_echo("Searching ${PKG_NAME} pkg in ${CONFIG_DIR}")
  # Save old states
  set(${PKG_NAME}_DIR_OLD ${${PKG_NAME}_DIR})
  set(${PKG_NAME}_DIR "${CONFIG_DIR}")
  # Find package
  arcpkg_find_package_begin()
  # find_package(${PKG_NAME} ${${PKG}_VERSION} REQUIRED PATHS ${${PKG}_DIR} NO_DEFAULT_PATH)
  # find_package(${PKG_NAME} ${${PKG}_VERSION} REQUIRED NO_DEFAULT_PATH)
  find_package(${PKG_NAME} REQUIRED NO_DEFAULT_PATH)
  arcpkg_find_package_end()
  # Restore old states
  if(${PKG_NAME}_DIR_OLD)
    set(${PKG_NAME}_DIR ${${PKG_NAME}_DIR_OLD})
  endif()
  unset(${PKG_NAME}_DIR_OLD)
endmacro(arcpkg_find_package)


function(arcpkg_check_imported_consistency PKG_USER PKG_NAME TXT_LIBS IMPORTED_LIBS)
  list(SORT TXT_LIBS)
  list(SORT IMPORTED_LIBS)
  list(LENGTH TXT_LIBS NUM_TXT_LIBS)
  list(LENGTH IMPORTED_LIBS NUM_IMPORTED_LIBS)
  if(NUM_TXT_LIBS GREATER NUM_IMPORTED_LIBS)
    arcpkg_error("The number of LIBS/BINS from arcpkg.txt should be less or equal than those from imported:\n"
                 "- arcpkg.txt(${NUM_TXT_LIBS}): ${TXT_LIBS}\n"
                 "- imported(${NUM_IMPORTED_LIBS}): ${IMPORTED_LIBS}")
  else()
    foreach(X ${TXT_LIBS})
      if(X IN_LIST IMPORTED_LIBS)
        continue()
      elseif(X MATCHES "::")
        if(NOT ${X} IN_LIST IMPORTED_LIBS AND NOT TARGET ${X})
          arcpkg_error("Can not find ${X} from arcpkg.txt in imported targets: ${IMPORTED_LIBS}!")
        endif()
      elseif(X STREQUAL PKG_NAME)
        if(NOT ${PKG_USER}::${X} IN_LIST IMPORTED_LIBS)
          arcpkg_error("Can not find ${PKG_USER}::${X} from arcpkg.txt in imported targets: ${IMPORTED_LIBS}!")
        endif()
      elseif(NOT ${PKG_USER}::${PKG_NAME}::${X} IN_LIST IMPORTED_LIBS)
        arcpkg_error("Can not find ${PKG_USER}::${PKG_NAME}::${X} from arcpkg.txt in imported targets: ${IMPORTED_LIBS}!")
      endif()
    endforeach()
  endif()
endfunction(arcpkg_check_imported_consistency)


# DFS requires collection w.r.t. <PKG>_REQUIRES.
function(arcpkg_sort_import_order_recursive SORTED_PKGS_VAR PKG)
  set(SORTED_PKGS ${${SORTED_PKGS_VAR}})
  set(PKG_REQUIRES ${${PKG}_REQUIRES})
  if(PKG_REQUIRES)
    foreach(D ${PKG_REQUIRES}) # foo/0.1@arcpkg
      if(D MATCHES "([^:/]+)/[^:/@]+@([^:/@]+)")
        set(D_PKG ${CMAKE_MATCH_2}_${CMAKE_MATCH_1})
      elseif(D MATCHES "([^:/]+)/[^:/@]+") # foo/0.1
        set(D_PKG ${ARCPKG_DEFAULT_USER}_${CMAKE_MATCH_1})
      else()
        continue() # ignore
      endif()
      if(NOT D_PKG IN_LIST SORTED_PKGS)
        arcpkg_sort_import_order_recursive(SORTED_PKGS ${D_PKG})
      endif()
    endforeach()
  endif()
  list(APPEND SORTED_PKGS ${PKG})
  set(${SORTED_PKGS_VAR} ${SORTED_PKGS} PARENT_SCOPE)
endfunction(arcpkg_sort_import_order_recursive)


# DFS requires collection w.r.t. <PKG>_REQUIRES.
function(arcpkg_sort_import_order PKGS_VAR)
  set(PKGS ${${PKGS_VAR}})
  set(SORTED_PKGS)
  foreach(PKG ${PKGS})
    if(NOT PKG IN_LIST SORTED_PKGS)
      arcpkg_sort_import_order_recursive(SORTED_PKGS ${PKG})
    endif()
  endforeach()
  set(NEW_SORTED_PKGS)
  foreach(PKG ${SORTED_PKGS})
    if(PKG IN_LIST PKGS)
      list(APPEND NEW_SORTED_PKGS ${PKG})
    endif()
  endforeach()
  set(${PKGS_VAR} ${NEW_SORTED_PKGS} PARENT_SCOPE)
endfunction(arcpkg_sort_import_order)


# Fix <config> specified properties.
function(arcpkg_fix_imported_target TARGET)
  get_target_property(TYPE ${TARGET} TYPE)
  if(TYPE STREQUAL "INTERFACE_LIBRARY")
    return()
  endif()
  if(TYPE STREQUAL "STATIC_LIBRARY")
    set(REQUIRE_LINK_LANGUAGES ON)
  else()
    set(REQUIRE_LINK_LANGUAGES OFF)
  endif()
  get_target_property(IMPORTED_IMPLIB ${TARGET} IMPORTED_IMPLIB)
  get_target_property(IMPORTED_LOCATION ${TARGET} IMPORTED_LOCATION)
  get_target_property(IMPORTED_LINK_INTERFACE_LANGUAGES ${TARGET} IMPORTED_LINK_INTERFACE_LANGUAGES)
  # message(STATUS "  IMPORTED_IMPLIB: ${IMPORTED_IMPLIB}")
  # message(STATUS "  IMPORTED_LOCATION: ${IMPORTED_LOCATION}")
  # message(STATUS "  IMPORTED_LINK_INTERFACE_LANGUAGES: ${IMPORTED_LINK_INTERFACE_LANGUAGES}")
  if((NOT REQUIRE_LINK_LANGUAGES OR IMPORTED_LINK_INTERFACE_LANGUAGES) AND(IMPORTED_IMPLIB OR IMPORTED_LOCATION))
    return()
  endif()
  set(PREFER_CFG)
  foreach(CFG RELEASE MINSIZEREL RELWITHDEBINFO DEBUG)
    get_target_property(IMPORTED_IMPLIB_${CFG} ${TARGET} IMPORTED_IMPLIB_${CFG})
    get_target_property(IMPORTED_LOCATION_${CFG} ${TARGET} IMPORTED_LOCATION_${CFG})
    get_target_property(IMPORTED_LINK_INTERFACE_LANGUAGES_${CFG} ${TARGET} IMPORTED_LINK_INTERFACE_LANGUAGES_${CFG})
    if(IMPORTED_IMPLIB_${CFG} OR IMPORTED_LOCATION_${CFG})
      if(NOT PREFER_CFG)
        set(PREFER_CFG ${CFG})
      endif()
    endif()
  endforeach()
  # message("IMPORTED_IMPLIB_${PREFER_CFG}: ${IMPORTED_IMPLIB_${PREFER_CFG}}")
  # message("IMPORTED_LOCATION_${PREFER_CFG}: ${IMPORTED_LOCATION_${PREFER_CFG}}")
  # message("IMPORTED_LINK_INTERFACE_LANGUAGES_${PREFER_CFG}: ${IMPORTED_LINK_INTERFACE_LANGUAGES_${PREFER_CFG}}")
  if(NOT (IMPORTED_IMPLIB OR IMPORTED_LOCATION))
    if(NOT PREFER_CFG)
      message(FATAL_ERROR "Can not find IMPORTED_IMPLIB or IMPORTED_LOCATION of imported target \"${TARGET}\"!")
    endif()
    if(NOT IMPORTED_IMPLIB_${PREFER_CFG})
      set(IMPORTED_IMPLIB_${PREFER_CFG})
    endif()
    if(NOT IMPORTED_LOCATION_${PREFER_CFG})
      set(IMPORTED_LOCATION_${PREFER_CFG})
    endif()
    set_target_properties(${TARGET} PROPERTIES
      IMPORTED_IMPLIB "${IMPORTED_IMPLIB_${PREFER_CFG}}"
      IMPORTED_LOCATION "${IMPORTED_LOCATION_${PREFER_CFG}}"
    )
  endif()
  if(REQUIRE_LINK_LANGUAGES AND NOT IMPORTED_LINK_INTERFACE_LANGUAGES AND IMPORTED_LINK_INTERFACE_LANGUAGES_${PREFER_CFG})
    set_target_properties(${TARGET} PROPERTIES
      IMPORTED_LINK_INTERFACE_LANGUAGES "${IMPORTED_LINK_INTERFACE_LANGUAGES_${PREFER_CFG}}"
    )
  endif()
endfunction()


# Install packages recursively but not really import them into CMake projects.
# - Set ARCPKG_IMPORT_PKGS to collect packages to be imported.
# - Set ${PKG}_<ATTR> in ${ARCPKG_ATTRIBUTES} for arcpkg.txt.
# - Set ${PKG}_DIR.
# - Set ${PKG}_INSTALL_DIR.
# - Set ${PKG}_LITE_RECIPE.
# - Set ${PKG}_RECIPE.
# - Set ${PKG}_TARGET.
# - Set ${PKG}_PROP_TARGET.
macro(arcpkg_import_ PKG_RECIPE PLATFORM ARCH NO_REQUIRES)
  arcpkg_parse_recipe(PKG ${PKG_RECIPE})
  set(PKG ${PKG_USER}_${PKG_NAME})
  set(PKG_TARGET ${PKG_USER}-${PKG_NAME})
  set(PKG_TARGET_NS ${PKG_USER}::${PKG_NAME})
  # NOTE: overwrite the package version
  if(DEFINED ${PKG}_VERSION_FORCE)
    set(PKG_VERSION ${${PKG}_VERSION_FORCE})
  endif()
  # Lazy Linux distro checking.
  if(NOT ARCPKG_LINUX_DISTRO AND PKG_DISTRO)
    arcpkg_get_linux_distro(ARCPKG_LINUX_DISTRO)
  endif()
  # Add HOST prefix.
  if(PKG_HOST)
    set(PKG HOST_${PKG})
    set(PKG_TARGET host-${PKG_TARGET})
    set(PKG_TARGET_NS host::${PKG_TARGET_NS})
  endif()
  # Get PKG_RECIPE and PKG_PROP_TARGET.
  arcpkg_make_recipe(PKG_RECIPE PKG)
  arcpkg_make_lite_recipe(PKG_LITE_RECIPE PKG)
  set(PKG_PROP_TARGET)
  set(PKG_RECIPE_IMPORTED)
  if(TARGET "${PKG_TARGET_NS}") # default imported package
    set(PKG_PROP_TARGET "${PKG_TARGET_NS}")
  elseif(DEFINED ${PKG}_LITE_RECIPE) # to-imported target
    set(PKG_RECIPE_IMPORTED ${${PKG}_LITE_RECIPE})
  elseif(TARGET "${PKG_TARGET}-update") # imported package (can NOT access its variables)
    set(PKG_PROP_TARGET "${PKG_TARGET}-update")
  elseif(TARGET "${PKG_TARGET}") # package in source project
    set(PKG_PROP_TARGET "${PKG_TARGET}")
  else() # not-installed package
    list(APPEND ARCPKG_IMPORT_PKGS ${PKG})
  endif()
  if(PKG_PROP_TARGET)
    arcpkg_get_pkg_property(PKG_RECIPE_IMPORTED ${PKG_PROP_TARGET} ARCPKG_RECIPE)
    if(NOT PKG_RECIPE_IMPORTED)
      arcpkg_error_i("Internal error: ${PKG_PROP_TARGET} has no ARCPKG_RECIPE.")
    endif()
  endif()
  # message("PKG: ${PKG}")
  # message("PKG_PROP_TARGET: ${PKG_PROP_TARGET}")
  # message("PKG_LITE_RECIPE: ${PKG_LITE_RECIPE}")
  # message("PKG_RECIPE_IMPORTED: ${PKG_RECIPE_IMPORTED}")
  set(PKG_NEED_TO_BE_IMPORTED FALSE)
  if(PKG_RECIPE_IMPORTED)
    # If the PKG has been imported, check the receip consistency.
    if(NOT PKG_RECIPE_IMPORTED MATCHES "^${PKG_LITE_RECIPE}")
      if(TARGET "${PKG_TARGET}")
        arcpkg_warn("Use ${PKG_RECIPE_IMPORTED} in project instead of importing ${PKG_RECIPE}.")
      else()
        arcpkg_error("Inconsistent package recipes between imported (${PKG_RECIPE_IMPORTED}) and to-be-imported (${PKG_LITE_RECIPE}).\n"
                     "Please call `set(${PKG}_VERSION_FORCE <target-version>)` before `arcpkg_import()`.")
      endif()
    endif()
    if(NOT TARGET "${PKG_TARGET}" AND NOT DEFINED ${PKG}_DIR)
      # The PKG has been imported but not accessible in ${CMAKE_CURRENT_SOURCE_DIR}.
      list(APPEND ARCPKG_IMPORT_PKGS ${PKG})
      set(PKG_NEED_TO_BE_IMPORTED TRUE)
      arcpkg_echo("Re-importing ${PKG_RECIPE}")
      # Get _DIR pkg attributes.
      arcpkg_get_pkg_property(${PKG}_DIR ${PKG_PROP_TARGET} ARCPKG_DIR)
      set(${PKG}_REIMPORTING TRUE)
    endif()
  else()
    set(PKG_NEED_TO_BE_IMPORTED TRUE)
    # Install the new PKG and get _DIR/_INSTALL_DIR pkg attributes.
    arcpkg_install_pkg(${PKG}_DIR ${PKG}_INSTALL_DIR PKG "${PLATFORM}" "${ARCH}")
  endif()
  # Set pkg attributes (requires ${PKG}_DIR).
  if(PKG_NEED_TO_BE_IMPORTED)
    # Set pkg attributes from arcpkg.txt.
    set(${PKG}_INFO_PATH "${${PKG}_DIR}/arcpkg.txt")
    if(EXISTS ${${PKG}_INFO_PATH})
      arcpkg_parse_info_file(${${PKG}_INFO_PATH} ${PKG})
      # Check consistency between arcpkg_import() and arcpkg.txt.
      foreach(X NAME VERSION) # NOTE: ignore USER for easy pkg moving between <user>'s.
        # NOTE: only check existent attributes.
        if(DEFINED ${PKG}_${X} AND NOT PKG_${X} STREQUAL ${PKG}_${X})
          arcpkg_error("Inconsistent of ${X} of ${PKG_RECIPE} in arcpkg_import(${PKG_${X}}) and ${${PKG}_INFO_PATH}(${${PKG}_${X}})")
        endif()
      endforeach()
      # Upgrade pkg in older format
      if(${PKG}_PKG_VERSION VERSION_LESS "0.9")
        arcpkg_upgrade_pkg("${${PKG}_DIR}" ${PKG_USER} ${PKG_NAME})
      endif()
    else()
      unset(${PKG}_INFO_PATH)
    endif()
    # Set non-existent pkg attributes.
    set(${PKG}_USER ${PKG_USER}) # NOTE: use USER from receipe for easy pkg moving between <user>'s.
    foreach(X NAME VERSION USER CHANNEL HINTS DISTRO HOST RECIPE LITE_RECIPE TARGET PROP_TARGET)
      if(NOT ${PKG}_${X})
        set(${PKG}_${X} ${PKG_${X}})
      endif()
    endforeach()
    # Install dependencies.
    # NOTE: ignore requires of HOST pkg as only executables would be imported, and all shared dependencies are included already.
    if(NOT ${PKG}_HOST AND ${PKG}_REQUIRES)
      arcpkg_debug("  REQUIRES: ${${PKG}_REQUIRES}")
      if(NOT "${NO_REQUIRES}")
        list(APPEND ARCPKG_IMPORT_STACK ${PKG_TARGET})
        foreach(D ${${PKG}_REQUIRES})
          if(D MATCHES ".+/.+")
            arcpkg_import_("${D}" "${PLATFORM}" "${ARCH}" "${NO_REQUIRES}")
          endif()
        endforeach()
        unset(D)
        list(REMOVE_AT ARCPKG_IMPORT_STACK -1)
      endif()
    endif()
  endif()
  # Add <pkg>-update dependencies.
  if(ARCPKG_IMPORT_STACK AND NOT DEFINED CMAKE_SCRIPT_MODE_FILE AND NOT TARGET "${PKG_TARGET}")
    list(GET ARCPKG_IMPORT_STACK -1 PARENT_PKG)
    # message("${PARENT_PKG} -> ${PKG_TARGET}")
    add_dependencies(${PARENT_PKG}-update ${PKG_TARGET}-update)
  endif()
  # Unset temporary variables.
  foreach(_X X PKG PKG_NAME PKG_VERSION PKG_USER PKG_CHANNEL PKG_HINTS PKG_HOST PKG_DISTRO PKG_RECIPE PKG_LITE_RECIPE PKG_RECIPE_IMPORTED PKG_PROP_TARGET PKG_NEED_TO_BE_IMPORTED)
    unset(${_X})
  endforeach()
  unset(_X)
endmacro(arcpkg_import_)


# Generate targets with <user>:: namespace for imported targets.
function(arcpkg_generate_ns_targets NS_TARGETS_VAR TARGETS PKG)
  # 1. Collect <user>::<name> targets.
  set(PKG_USER ${${PKG}_USER})
  set(PKG_NAME ${${PKG}_NAME})
  set(NS_TARGETS)
  foreach(M ${TARGETS})
    if(TARGET ${M} AND M MATCHES "^host::${PKG_USER}::") # M is <user>::<name>
      list(APPEND NS_TARGETS ${M})
    elseif(TARGET ${M} AND M MATCHES "^${PKG_USER}::") # M is <user>::<name>
      list(APPEND NS_TARGETS ${M})
    elseif(TARGET ${M} AND M STREQUAL "${PKG_NAME}::${PKG_NAME}") # M is <name>::<name>
      list(APPEND NS_TARGETS ${M})
      get_target_property(M_TYPE ${M} TYPE)
      if(M_TYPE STREQUAL "EXECUTABLE")
        add_executable(${PKG_USER}::${PKG_NAME} ALIAS ${M})
      else()
        add_library(${PKG_USER}::${PKG_NAME} ALIAS ${M})
      endif()
    elseif(TARGET ${PKG_USER}::${M}) # M is <name> and <user>::<name> exists
      list(APPEND NS_TARGETS ${PKG_USER}::${M})
    elseif(TARGET ${M}) # M is <name> and <user>::<name> does not exist
      list(APPEND NS_TARGETS ${M}) # NOTE: use original name for compatibility with old arcpkg
      get_target_property(M_IMPORTED ${M} IMPORTED)
      # Create <user>::<name> aliased target
      if(NOT M_IMPORTED OR ARCPKG_EXPORT_GLOBAL)
        if(M_IMPORTED AND CMAKE_VERSION VERSION_GREATER_EQUAL "3.11")
          set_target_properties(${M} PROPERTIES IMPORTED_GLOBAL TRUE) # CMake 3.11
        endif()
        get_target_property(M_TYPE ${M} TYPE)
        if(M_TYPE STREQUAL "EXECUTABLE")
          add_executable(${PKG_USER}::${M} ALIAS ${M})
        else()
          add_library(${PKG_USER}::${M} ALIAS ${M})
        endif()
      endif()
    else()
      list(APPEND NS_TARGETS ${M}) # non-target
    endif()
  endforeach()
  # 2. Set properties for <user>::<name> targets.
  foreach(M ${NS_TARGETS})
    arcpkg_set_all_pkg_properties(${M} ${PKG})
    get_target_property(M_IMPORTED ${M} IMPORTED)
    if(NOT M_IMPORTED)
      continue()
    endif()
    get_target_property(ALIASED_TARGET ${M} ALIASED_TARGET)
    if(ALIASED_TARGET)
      set(M ${ALIASED_TARGET})
    endif()
    if(ARCPKG_EXPORT_GLOBAL)
      set_target_properties(${M} PROPERTIES IMPORTED_GLOBAL TRUE) # CMake 3.11
    endif()
    arcpkg_fix_imported_target(${M})
  endforeach()
  set(${NS_TARGETS_VAR} ${NS_TARGETS} PARENT_SCOPE)
endfunction(arcpkg_generate_ns_targets)


###########################################################
# Import packages.
# Usage: arcpkg_import(<pkg> [pkg...] NO_REQUIRES)
# package recipe name convention
# Targets:
#   - arcpkg-update
#   - <user>-<name>-update
macro(arcpkg_import)
  cmake_parse_arguments(A "NO_REQUIRES" "" "" ${ARGN})
  # 1. Install packages.
  set(ARCPKG_IMPORT_PKGS)
  foreach(PKG_RECIPE ${A_UNPARSED_ARGUMENTS})
    arcpkg_import_(${PKG_RECIPE} "${ARCPKG_PLATFORM}" "${ARCPKG_ARCH}" "${A_NO_REQUIRES}")
    unset(PKG)
  endforeach()
  if(CMAKE_SCRIPT_MODE_FILE)
    return()
  endif()
  # 2. Sort importing order of packages.
  arcpkg_sort_import_order(ARCPKG_IMPORT_PKGS)
  # message("ARCPKG_IMPORT_PKGS: ${ARCPKG_IMPORT_PKGS}")
  # 3. Import packages.
  foreach(PKG ${ARCPKG_IMPORT_PKGS})
    # a0: Only export pkg attributes and find_package() when global attribute variables are not accessible.
    if(${PKG}_REIMPORTING)
      arcpkg_export_all_pkg_properties(${${PKG}_PROP_TARGET} ${PKG})
      if(${PKG}_CONFIG_DIR)
        arcpkg_find_package(${${PKG}_CONFIG_DIR} ${${PKG}_NAME})
      endif()
      continue()
    endif()
    set(PKG_USER ${${PKG}_USER})
    set(PKG_NAME ${${PKG}_NAME})
    set(PKG_DIR ${${PKG}_DIR})
    # a. Collect lib requires, used by arcpkg_find_targets().
    set(${PKG}_REQUIRES_LIBS)
    # message("${PKG}_REQUIRES: ${${PKG}_REQUIRES}")
    # message("${PKG}_DIR: ${${PKG}_DIR}")
    # message("${PKG}_INSTALL_DIR: ${${PKG}_INSTALL_DIR}")
    foreach(D ${${PKG}_REQUIRES})
      if(D MATCHES "^[^/@:]+/[^/@:]+") # arcpkg targets
        arcpkg_get_pkg_prefix_from_recipe(D_PKG ${D})
        list(APPEND ${PKG}_REQUIRES_LIBS ${${D_PKG}_LIBS})
      else() # system library name or targets, e.g. dl, OpenGL::OpenGL
        list(APPEND ${PKG}_REQUIRES_LIBS ${D})
      endif()
    endforeach()
    unset(D)
    unset(D_PKG)
    # b. find_package()
    set(PKG_CONFIG_DIR)
    string(TOLOWER "${PKG_NAME}" LOWERCASE_PKG_NAME)
    if(EXISTS "${PKG_DIR}/${PKG_NAME}Config.cmake" OR EXISTS "${PKG_DIR}/${LOWERCASE_PKG_NAME}-config.cmake")
      set(PKG_CONFIG_DIR "${PKG_DIR}")
    elseif(EXISTS "${PKG_DIR}/cmake/${PKG_NAME}Config.cmake" OR
           EXISTS "${PKG_DIR}/cmake/${LOWERCASE_PKG_NAME}-config.cmake")
      set(PKG_CONFIG_DIR "${PKG_DIR}/cmake")
    elseif(EXISTS "${PKG_DIR}/lib/cmake/${PKG_NAME}/${PKG_NAME}Config.cmake" OR
           EXISTS "${PKG_DIR}/lib/cmake/${PKG_NAME}/${LOWERCASE_PKG_NAME}-config.cmake")
      set(PKG_CONFIG_DIR "${PKG_DIR}/lib/cmake/${PKG_NAME}")
    elseif(EXISTS "${PKG_DIR}/lib/${PKG_NAME}/cmake/${PKG_NAME}Config.cmake" OR
           EXISTS "${PKG_DIR}/lib/${PKG_NAME}/cmake/${LOWERCASE_PKG_NAME}-config.cmake")
      set(PKG_CONFIG_DIR "${PKG_DIR}/lib/${PKG_NAME}/cmake")
    elseif(EXISTS "${PKG_DIR}/share/${PKG_NAME}/${PKG_NAME}Config.cmake" OR
           EXISTS "${PKG_DIR}/share/${PKG_NAME}/${LOWERCASE_PKG_NAME}-config.cmake")
      set(PKG_CONFIG_DIR "${PKG_DIR}/share/${PKG_NAME}")
    elseif(EXISTS "${PKG_DIR}/share/${PKG_NAME}/cmake/${PKG_NAME}Config.cmake" OR
           EXISTS "${PKG_DIR}/share/${PKG_NAME}/cmake/${LOWERCASE_PKG_NAME}-config.cmake")
      set(PKG_CONFIG_DIR "${PKG_DIR}/share/${PKG_NAME}/cmake")
    elseif(EXISTS "${PKG_DIR}/CMakeLists.txt")
      arcpkg_find_package_begin()
      include("${PKG_DIR}/CMakeLists.txt")
      arcpkg_find_package_end()
    else()
      arcpkg_find_targets(${PKG}_IMPORTED_LIBS ${PKG} "${PKG_DIR}")
    endif()
    if(PKG_CONFIG_DIR)
      if(${PKG}_HOST)
        arcpkg_find_host_executables(${PKG}_IMPORTED_BINS ${PKG} ${PKG_CONFIG_DIR})
      else()
        arcpkg_find_package(${PKG_CONFIG_DIR} ${PKG_NAME})
        set(${PKG}_CONFIG_DIR ${PKG_CONFIG_DIR})
      endif()
    endif()
    unset(${PKG}_REQUIRES_LIBS)
    # c. Get LIBS and BINS.
    set(${PKG}_LIBS_TXT ${${PKG}_LIBS})
    set(${PKG}_BINS_TXT ${${PKG}_BINS})
    if(${PKG}_HOST)
      set(${PKG}_LIBS ${${PKG}_IMPORTED_LIBS})
      set(${PKG}_BINS ${${PKG}_IMPORTED_BINS})
    else()
      if(${PKG}_IMPORTED_LIBS)
        set(${PKG}_LIBS ${${PKG}_IMPORTED_LIBS})
      endif()
      if(${PKG}_IMPORTED_BINS)
        set(${PKG}_BINS ${${PKG}_IMPORTED_BINS})
      endif()
    endif()
    # d. Create <user>::<name> targets.
    arcpkg_generate_ns_targets(${PKG}_LIBS "${${PKG}_LIBS}" ${PKG})
    arcpkg_generate_ns_targets(${PKG}_BINS "${${PKG}_BINS}" ${PKG})
    arcpkg_set_all_pkg_properties("${${PKG}_TARGET}-update" ${PKG})
    if(${PKG}_LIBS)
      arcpkg_echo("  Imported LIBS: ${${PKG}_LIBS}")
    endif()
    if(${PKG}_BINS)
      arcpkg_echo("  Imported BINS: ${${PKG}_BINS}")
    endif()
    # e. Check consistency of imported targets.
    if(${PKG}_IMPORTED_LIBS AND ${PKG}_LIBS_TXT)
      arcpkg_check_imported_consistency(${PKG_USER} ${PKG_NAME} "${${PKG}_LIBS_TXT}" "${${PKG}_IMPORTED_LIBS}")
    endif()
    if(${PKG}_IMPORTED_BINS AND ${PKG}_BINS_TXT)
      arcpkg_check_imported_consistency(${PKG_USER} ${PKG_NAME} ${${PKG}_BINS_TXT} ${${PKG}_IMPORTED_BINS})
    endif()
  endforeach()
  # 4. Unset temporary variables.
  foreach(_X M X XX PKG PKG_NAME PKG_USER PKG_HINTS PKG_DISTRO PKG_HOST PKG_DIR PKG_RECIPE ARCPKG_IMPORT_PKGS A_NO_REQUIRES A_UNPARSED_ARGUMENTS)
    unset(${_X})
  endforeach()
  unset(_X)
endmacro(arcpkg_import)


# Collect DLL directories of dependencies.
function(arcpkg_collect_dll_dirs DLL_DIRS_VAR TARGET)
  arcpkg_get_dependencies(DEPENDS ${TARGET})
  set(DLL_DIRS ${${DLL_DIRS_VAR}})
  foreach(D ${DEPENDS})
    if(NOT TARGET ${D})
      if(EXISTS ${D} AND D MATCHES "\\.(dll|DLL)$")
        get_filename_component(DLL_DIR "${D}" DIRECTORY)
        file(TO_NATIVE_PATH "${DLL_DIR}" DLL_DIR)
        list(INSERT DLL_DIRS 0 ${DLL_DIR})
      endif()
    else(NOT TARGET ${D})
      get_target_property(TYPE ${D} TYPE)
      if(NOT TYPE MATCHES "^(SHARED_LIBRARY|MODULE_LIBRARY)$")
        continue()
      endif()
      get_target_property(IMPORTED ${D} IMPORTED)
      if(IMPORTED)
        foreach(PROP IMPORTED_LOCATION IMPORTED_LOCATION_DEBUG IMPORTED_LOCATION_RELEASE)
          get_target_property(IMPORTED_LOCATION ${D} ${PROP})
          if(IMPORTED_LOCATION)
            foreach(DLL_PATH ${IMPORTED_LOCATION})
              get_filename_component(DLL_DIR "${DLL_PATH}" DIRECTORY)
              file(TO_NATIVE_PATH "${DLL_DIR}" DLL_DIR)
              list(INSERT DLL_DIRS 0 ${DLL_DIR})
            endforeach()
          endif()
        endforeach()
      else()
        # TODO: ignore targets in source project.
        # message("if TARGET and not IMPORTED, DEPENDS: ${D}")
        set(DLL_DIR $<TARGET_FILE_DIR:${D}>)
        list(INSERT DLL_DIRS 0 ${DLL_DIR})
      endif()
    endif(NOT TARGET ${D})
  endforeach()
  list(REMOVE_DUPLICATES DLL_DIRS)
  set(${DLL_DIRS_VAR} ${DLL_DIRS} PARENT_SCOPE)
endfunction(arcpkg_collect_dll_dirs)


# Check if ASAN is enabled for target, and set ASAN variable to 1 if enabled.
function(arcpkg_asan_enabled TARGET)
  if(NOT ASAN)
    if(CMAKE_C_FLAGS MATCHES "/fsanitize=address")
      set(ASAN 1 PARENT_SCOPE)
    else()
      get_target_property(COMPILE_OPTIONS ${TARGET} COMPILE_OPTIONS)
      if(COMPILE_OPTIONS)
        foreach(COMPILE_OPTION ${COMPILE_OPTIONS})
          if(COMPILE_OPTION MATCHES "/fsanitize=address")
            set(ASAN 1 PARENT_SCOPE)
            return()
          endif()
        endforeach()
      endif()
    endif()
  endif()
endfunction()


# Add DLL directories of dependencies for executable target, ONLY handle imported targets.
function(arcpkg_vs_debugger_environment TARGET)
  if(NOT CMAKE_GENERATOR MATCHES "Visual Studio")
    return()
  endif()
  get_target_property(TARGET_TYPE ${TARGET} TYPE)
  if(NOT TARGET_TYPE STREQUAL "EXECUTABLE")
    return()
  endif()

  # Collect existed environment variables and values.
  get_target_property(ENVIRONMENT_LINES ${TARGET} VS_DEBUGGER_ENVIRONMENT)
  string(REPLACE ";" "|" ENVIRONMENT_LINES "${ENVIRONMENT_LINES}")
  string(REPLACE "\r\n" "\n" ENVIRONMENT_LINES "${ENVIRONMENT_LINES}")
  string(REPLACE "\n" ";" ENVIRONMENT_LINES "${ENVIRONMENT_LINES}")
  set(_ENV_NAMES)
  foreach(ENVIRONMENT_LINE ${ENVIRONMENT_LINES})
    if(ENVIRONMENT_LINE MATCHES "([a-zA-Z0-9_]+)=(.*)")
      set(NAME ${CMAKE_MATCH_1})
      set(VALUES ${CMAKE_MATCH_2})
      string(REPLACE "|" ";" VALUES "${VALUES}")
      set(ENV_${NAME} ${VALUES})
      list(APPEND _ENV_NAMES ${NAME})
    endif()
  endforeach()

  # Append ASAN environment variables.
  arcpkg_asan_enabled(${TARGET})
  if(ASAN)
    arcpkg_debug("Setting ASAN environment for ${TARGET}")
    if(CMAKE_SIZEOF_VOID_P EQUAL 8)
      set(ENV_PATH "\$(VC_ExecutablePath_x64);${ENV_PATH}")
      set(ENV_ASAN_SYMBOLIZER_PATH "\$(VC_ExecutablePath_x64);${ENV_ASAN_SYMBOLIZER_PATH}")
    elseif(CMAKE_SIZEOF_VOID_P EQUAL 4)
      set(ENV_PATH "\$(VC_ExecutablePath_x86);${ENV_PATH}")
      set(ENV_ASAN_SYMBOLIZER_PATH "\$(VC_ExecutablePath_x86);${ENV_ASAN_SYMBOLIZER_PATH}")
    else()
      arcpkg_error("Unsupported CMAKE_SIZEOF_VOID_P: ${CMAKE_SIZEOF_VOID_P}")
    endif()
    list(APPEND _ENV_NAMES PATH)
    list(APPEND _ENV_NAMES ASAN_SYMBOLIZER_PATH)
  endif()

  # Append DLL directories of dependencies.
  arcpkg_collect_dll_dirs(DLL_DIRS ${TARGET})
  set(NEW_APPENED_DLL_DIRS)
  foreach(DLL_DIR ${DLL_DIRS})
    if(DLL_DIR IN_LIST ENV_PATH)
      continue()
    endif()
    list(APPEND NEW_APPENED_DLL_DIRS ${DLL_DIR})
  endforeach()
  # message("NEW_APPENED_DLL_DIRS: ${NEW_APPENED_DLL_DIRS}")
  if(NEW_APPENED_DLL_DIRS)
    set(ENV_PATH ${NEW_APPENED_DLL_DIRS} ${ENV_PATH})
    list(APPEND _ENV_NAMES PATH)
    arcpkg_echo("Prepending dll directories to debugging PATH of ${TARGET}")
    foreach(DLL_DIR ${NEW_APPENED_DLL_DIRS})
      arcpkg_echo("- ${DLL_DIR}")
    endforeach()
  endif()

  # Generate new environment lines.
  set(NEW_ENVIRONMENT_LINES)
  list(REMOVE_DUPLICATES _ENV_NAMES)
  foreach(_ENV_NAME ${_ENV_NAMES})
    set(VALUES ${ENV_${_ENV_NAME}})
    if(_ENV_NAME STREQUAL "PATH")
      list(APPEND VALUES "%PATH%")
    endif()
    list(REMOVE_DUPLICATES VALUES)
    string(REGEX REPLACE ";$" "" VALUES "${VALUES}")
    # message("ENV_${_ENV_NAME}: ${VALUES}")
    if(NEW_ENVIRONMENT_LINES)
      set(NEW_ENVIRONMENT_LINES "${NEW_ENVIRONMENT_LINES}\n${_ENV_NAME}=${VALUES}")
    else()
      set(NEW_ENVIRONMENT_LINES "${_ENV_NAME}=${VALUES}")
    endif()
  endforeach()
  # message("NEW_ENVIRONMENT_LINES: ${NEW_ENVIRONMENT_LINES}")

  # Set new environment variables.
  if(NEW_ENVIRONMENT_LINES)
    set_target_properties(${TARGET} PROPERTIES VS_DEBUGGER_ENVIRONMENT "${NEW_ENVIRONMENT_LINES}")
  endif()
endfunction(arcpkg_vs_debugger_environment)


macro(arcpkg_vs_debugger_enviroment TARGET)
  arcpkg_error("arcpkg_vs_debugger_enviroment() is deprecated, please use arcpkg_vs_debugger_environment() instead")
endmacro(arcpkg_vs_debugger_enviroment)


# Remove local or remote packages.
# arcpkg_remove(<mode> [pattern...])
function(arcpkg_remove MODE)
  foreach(PATTERN ${ARGN})
    string(REGEX REPLACE "[\r\n]" "" PATTERN "${PATTERN}")
    arcpkg_echo("arcpkg_cmd_remove: ${MODE}: ${PATTERN}")
    if(${PATTERN} MATCHES "\\.\\.")
      arcpkg_error("Please don't include \"..\" in pattern (${PATTERN}) to avoid accidentally deleting!")
    elseif(NOT ${PATTERN} MATCHES ".+/.+")
      arcpkg_error("Please include \"/\" in pattern (${PATTERN}) to avoid accidentally deleting!")
    endif()
    if("LOCAL" IN_LIST MODE)
      file(GLOB PKGS_TO_REMOVE LIST_DIRECTORIES TRUE "${ARCPKG_ROOT}/${PATTERN}")
      # message(("${ARCPKG_ROOT}/${PATTERN}: ${PKGS_TO_REMOVE}"))
      if(PKGS_TO_REMOVE)
        foreach(PKG ${PKGS_TO_REMOVE})
          arcpkg_echo("- ${PKG}")
        endforeach()
        file(REMOVE_RECURSE ${PKGS_TO_REMOVE})
      endif()
    endif()
    if("REMOTE" IN_LIST MODE)
      set(URL "${ARTIFACTORY_URL}/${ARTIFACTORY_REPO}/${PATTERN}")
      arcpkg_echo("- ${URL}")
      # use RESULT_VARIABLE and OUTPUT_VARIABLE to ignore error and output
      arcpkg_py_http_request("${URL}" METHOD "DELETE" RESULT_VARIABLE RET OUTPUT_VARIABLE OUTPUT IGNORE_ERRORS "404")
    endif()
  endforeach()
endfunction(arcpkg_remove)


if(NOT DEFINED CMAKE_SCRIPT_MODE_FILE)
  return()
endif()

###########################################################
# Generate link version script.
###########################################################

function(arcpkg_parse_global_symbols SYMBOLS_VAR INC_CONTENT)
  string(REGEX REPLACE "[ \t]*//[^\r\n]*([\r\n])" "\\1" INC_CONTENT "${INC_CONTENT}") # // comment
  string(REGEX REPLACE "(^|[\r\n])[ \t]*[\r\n]+" "\\1" INC_CONTENT "${INC_CONTENT}") # empty line
  string(REGEX REPLACE "/\\*[^\r\n]*\\*/" "" INC_CONTENT "${INC_CONTENT}") # /**/ one line comment
  string(REGEX REPLACE "\\[ \t]*[\r\n]+" " " INC_CONTENT "${INC_CONTENT}") # join lines in same macros
  string(REGEX REPLACE "(^|[\r\n])[ \t]*#[^\r\n]+" "" INC_CONTENT "${INC_CONTENT}") # macros
  # while(1)
  #   set(NEW_INC_CONTENT "${INC_CONTENT}")
  #   string(REGEX REPLACE "{[^{}]*};?" "" INC_CONTENT "${INC_CONTENT}") # {} or {};
  #   if(NEW_INC_CONTENT STREQUAL INC_CONTENT)
  #     break()
  #   endif()
  # endwhile()
  string(REGEX MATCHALL "[a-zA-Z_][a-zA-Z_0-9]+[ \t\r\n]*\\([^\\(\\)]*\\)[ \t\r\n]*;" FUNCS "${INC_CONTENT}") # functions
  string(REGEX REPLACE "[ \t\r\n]*\\([^\\(\\)]*\\)[ \t\r\n]*;" "" FUNCS "${FUNCS}") # remove all except function names
  set(${SYMBOLS_VAR} ${FUNCS} PARENT_SCOPE)
endfunction(arcpkg_parse_global_symbols)


function(arcpkg_parse_proguard_defines DEFINES_VAR INC_CONTENT)
  string(REGEX MATCHALL "#define[ \ta-zA-Z_0-9]+" INC_CONTENT "${INC_CONTENT}")
  string(REGEX REPLACE "#define[ \t]+[a-zA-Z_0-9]+[ \t]+([a-zA-Z_0-9]+)" "\\1" DEFINES "${INC_CONTENT}")
  set(${DEFINES_VAR} ${DEFINES} PARENT_SCOPE)
endfunction(arcpkg_parse_proguard_defines)


function(arcpkg_collect_global_symbols GLOBAL_SYMBOLS_VAR)
  set(GLOBAL_SYMBOLS)
  arcpkg_debug("Parsing header files:")
  foreach(INC ${ARGN})
    arcpkg_debug("- ${INC}")
    file(READ "${INC}" INC_CONTENT)
    if(INC MATCHES "_proguard\\.(h|hpp|hxx)$")
      arcpkg_debug("Parsing proguard defines...")
      arcpkg_parse_proguard_defines(SYMBOLS "${INC_CONTENT}")
    else()
      arcpkg_parse_global_symbols(SYMBOLS "${INC_CONTENT}")
    endif()
    if(SYMBOLS)
      list(APPEND GLOBAL_SYMBOLS ${SYMBOLS})
    endif()
  endforeach()
  if(GLOBAL_SYMBOLS)
    list(REMOVE_DUPLICATES GLOBAL_SYMBOLS)
    list(SORT GLOBAL_SYMBOLS)
  endif()
  arcpkg_debug("Exported functions:")
  foreach(name ${GLOBAL_SYMBOLS})
    arcpkg_debug("- ${name}")
  endforeach()
  set(${GLOBAL_SYMBOLS_VAR} ${GLOBAL_SYMBOLS} PARENT_SCOPE)
endfunction(arcpkg_collect_global_symbols)


function(arcpkg_write_ld_version_script FILE_PATH)
  set(CONTENT "{\n")
  set(CONTENT "${CONTENT}global:")
  foreach(sym ${ARGN})
    set(CONTENT "${CONTENT}\n    ${sym};")
  endforeach()
  if(ARGN)
    set(CONTENT "${CONTENT}\n")
  else()
    set(CONTENT "${CONTENT} ;\n")
  endif()
  set(CONTENT "${CONTENT}    local: *;\n")
  set(CONTENT "${CONTENT}};")
  file(WRITE "${FILE_PATH}" "${CONTENT}")
endfunction(arcpkg_write_ld_version_script)


###########################################################
# Helper functions for command-line.
###########################################################

function(arcpkg_cmd_echo)
  message(${ARGN})
endfunction(arcpkg_cmd_echo)


function(arcpkg_execute_program)
  if(ARCPKG_VERBOSE EQUAL 0)
    set(OUTPUT_FLAGS OUTPUT_QUIET)
  else()
    set(OUTPUT_FLAGS)
  endif()
  if(ARCPKG_VERBOSE GREATER 2)
    string(REPLACE ";" " " ARGS_STR "${ARGN}")
    arcpkg_debug("execute_process(${ARGS_STR})")
  endif()
  execute_process(${ARGN} RESULT_VARIABLE RES ${OUTPUT_FLAGS})
  if(NOT RES EQUAL 0)
    arcpkg_error("Failed with return code ${RES}!")
  endif()
endfunction(arcpkg_execute_program)


macro(arcpkg_cmd_prepare_)
  get_filename_component(BUILD_DIR "${BUILD_DIR}" REALPATH)
  if(NOT PKG)
    set(PKG arcpkg)
  endif()
  set(BUILD_ARGS)
  if(NOT CMAKE_VERSION LESS "3.12" AND ARCPKG_NUM_JOBS)
    list(APPEND BUILD_ARGS -j ${ARCPKG_NUM_JOBS})
  endif()
  if(ARCPKG_VERBOSE GREATER 3)
    list(APPEND BUILD_ARGS --verbose)
  endif()
  # -nologo for msbuild
  file(GLOB SLN_FILES "${BUILD_DIR}/*.sln")
  if(SLN_FILES)
    list(APPEND BUILD_ARGS -- -nologo)
  endif()
  set(BUILD_CMD COMMAND ${CMAKE_COMMAND} --build ${BUILD_DIR})
  # message("BUILD_ARGS: ${BUILD_ARGS}")
endmacro(arcpkg_cmd_prepare_)


function(arcpkg_cmd_pack BUILD_DIR PKG CONFIGS)
  arcpkg_cmd_prepare_()
  if(NOT CONFIGS)
    set(CONFIGS Release)
  endif()
  arcpkg_echo("arcpkg_cmd_pack: ${BUILD_DIR} ${PKG} ${CONFIGS} ${BUILD_ARGS}")
  # NOTE: run multi-pass build commands to avoid build failure for project with file generation (e.g. ncnn projects)
  foreach(CFG ${CONFIGS})
    arcpkg_execute_program(${BUILD_CMD} --target ${PKG} --config ${CFG} ${BUILD_ARGS}) # build
  endforeach()
  arcpkg_execute_program(${BUILD_CMD} --target ${PKG}-clean ${BUILD_ARGS}) # clean
  foreach(CFG ${CONFIGS})
    if(CMAKE_VERSION VERSION_LESS "3.15")
      if(SLN_FILES)
        set(INSTALL_TARGET INSTALL)
      else()
        set(INSTALL_TARGET install)
      endif()
      arcpkg_execute_program(${BUILD_CMD} --target ${INSTALL_TARGET} --config ${CFG} ${BUILD_ARGS}) # install
    else()
      set(CMAKECACHE_FILE "${BUILD_DIR}/CMakeCache.txt")
      file(STRINGS "${CMAKECACHE_FILE}" PLATFORM_STR REGEX "ARCBUILD_PLATFORM:UNINITIALIZED=([^ ]+)")
      if(PLATFORM_STR MATCHES "=emcc$")
        arcpkg_echo("Disable stripping for emcc platform")
        set(INSTALL_COMMAND COMMAND ${CMAKE_COMMAND} --install ${BUILD_DIR} --config ${CFG})
      else()
        set(INSTALL_COMMAND COMMAND ${CMAKE_COMMAND} --install ${BUILD_DIR} --strip --config ${CFG})
      endif()
      if(NOT PKG STREQUAL "arcpkg")
        list(APPEND INSTALL_COMMAND --component ${PKG})
      endif()
      arcpkg_execute_program(${INSTALL_COMMAND}) # install
    endif()
  endforeach()
  arcpkg_execute_program(${BUILD_CMD} --target ${PKG}-pack ${BUILD_ARGS}) # pack
endfunction(arcpkg_cmd_pack)


function(arcpkg_cmd_export BUILD_DIR PKG)
  arcpkg_cmd_prepare_()
  arcpkg_echo("arcpkg_cmd_export: ${BUILD_DIR} ${PKG} ${BUILD_ARGS}")
  arcpkg_execute_program(${BUILD_CMD} --target ${PKG}-export ${BUILD_ARGS})
endfunction(arcpkg_cmd_export)


function(arcpkg_cmd_upload BUILD_DIR PKG)
  arcpkg_cmd_prepare_()
  arcpkg_echo("arcpkg_cmd_upload: ${BUILD_DIR} ${PKG} ${BUILD_ARGS}")
  arcpkg_execute_program(${BUILD_CMD} --target ${PKG}-upload ${BUILD_ARGS})
endfunction(arcpkg_cmd_upload)


function(arcpkg_cmd_update BUILD_DIR PKG)
  arcpkg_cmd_prepare_()
  arcpkg_echo("arcpkg_cmd_update: ${BUILD_DIR} ${PKG} ${BUILD_ARGS}")
  arcpkg_execute_program(${BUILD_CMD} --target ${PKG}-update ${BUILD_ARGS})
endfunction(arcpkg_cmd_update)


function(arcpkg_cmd_search PATTERN)
  if(PATTERN MATCHES "^([^/@]+)/([^/@]+)@([^/@]+)$")
    set(PKG_NAME ${CMAKE_MATCH_1})
    set(PKG_VERSION ${CMAKE_MATCH_2})
    set(PKG_USER ${CMAKE_MATCH_3})
  elseif(PATTERN MATCHES "^([^/@]+)/([^/@]+)$")
    set(PKG_NAME ${CMAKE_MATCH_1})
    set(PKG_VERSION ${CMAKE_MATCH_2})
    set(PKG_USER "*")
  elseif(PATTERN MATCHES "^([^/@]+)@([^/@]+)$")
    set(PKG_NAME ${CMAKE_MATCH_1})
    set(PKG_VERSION "*")
    set(PKG_USER ${CMAKE_MATCH_2})
  elseif(PATTERN MATCHES "^([^/@]+)$")
    set(PKG_NAME ${CMAKE_MATCH_1})
    set(PKG_VERSION "*")
    set(PKG_USER "*")
  else()
    arcpkg_error("Unsupported pattern: ${PATTERN}, please check help!")
  endif()
  arcpkg_cmd_echo("Searching pkgs with pattern: name='${PKG_NAME}', version='${PKG_VERSION}', user='${PKG_USER}'")
  set(ARTIFACTORIES)
  if(Python_EXECUTABLE)
    arcpkg_cmd_echo("Powered by Artifactory AQL when python is available.")
    arcpkg_jfrog_artifactory_aql(DEPLOYS_JSON [=[
items.find({
  "repo": "${ARTIFACTORY_REPO}",
  "path": {"$match": "${PKG_USER}/${PKG_NAME}"},
  "$or": [
    {"$and": [{"type": "folder"}, {"name": {"$match": "${PKG_VERSION}"}}] },
    {"name": {"$match": "${PKG_NAME}-${PKG_VERSION}.zip"}}
  ]
}).include("repo", "name", "path")
]=])
    string(REGEX MATCHALL "[^\"]+\",\n  \"name\" : \"[^\"]+" DEPLOYS_JSON "${DEPLOYS_JSON}")
    string(REPLACE "\",\n  \"name\" : \"" "/" ARTIFACTORIES "${DEPLOYS_JSON}") # join <name>/<path>
  else()
    arcpkg_cmd_echo("Powered by Artifactory pattern search when python is not available.")
    arcpkg_remote_pattern_search(DEPLOYS_JSON "${PKG_USER}/${PKG_NAME}/${PKG_NAME}-${PKG_VERSION}.zip")
    if(NOT DEPLOYS_JSON MATCHES "([^*\"]+)\\.zip")
      arcpkg_remote_pattern_search(DEPLOYS_JSON "${PKG_USER}/${PKG_NAME}/${PKG_VERSION}/*.zip")
    endif()
    string(REGEX MATCHALL "([^*\"]+)\\.zip" ARTIFACTORIES "${DEPLOYS_JSON}")
  endif()
  set(PKGS)
  foreach(X ${ARTIFACTORIES})
    if(X MATCHES "^([^/@]+)/([^/@]+)/([^/@]+)$")
      set(USER ${CMAKE_MATCH_1})
      set(NAME ${CMAKE_MATCH_2})
      set(VERSION ${CMAKE_MATCH_3})
    elseif(X MATCHES "^([^/@]+)/([^/@]+)/([^/@]+)/([^/@]+)$")
      set(USER ${CMAKE_MATCH_1})
      set(NAME ${CMAKE_MATCH_2})
      set(VERSION ${CMAKE_MATCH_3})
    endif()
    if(VERSION MATCHES "^${NAME}-(.*).zip$")
      set(VERSION ${CMAKE_MATCH_1})
    endif()
    list(APPEND PKGS "${NAME}/${VERSION}@${USER}")
  endforeach()
  list(REMOVE_DUPLICATES PKGS)
  list(SORT PKGS)
  foreach(PKG ${PKGS})
    arcpkg_cmd_echo("- ${PKG}")
  endforeach(PKG)
endfunction(arcpkg_cmd_search)


# arcpkg_cmd_remove(<mode> [pattern...])
function(arcpkg_cmd_remove MODE)
  arcpkg_remove("${MODE}" ${ARGN})
endfunction(arcpkg_cmd_remove)


function(arcpkg_cmd_install PLATFORM ARCH)
  arcpkg_check_remote_variables()
  set(ARCPKG_PLATFORM "${PLATFORM}")
  set(ARCPKG_ARCH "${ARCH}")
  set(ARCPKG_AUTO_UPDATE ON) # NOTE: force update when install
  set(ARCPKG_ONLY_REMOTE_PKG ON)
  arcpkg_import(${ARGN})
endfunction(arcpkg_cmd_install)


# Check updates of arcpkg.cmake.
function(arcpkg_cmd_check_updates)
  cmake_parse_arguments(A "FORCE;VERBOSE" "" "" ${ARGN})
  set(URL "https://git.arcsoft.com.cn/lny1856/arcpkg/raw/master/CHANGELOG.md")
  set(LOCAL_PATH "${ARCPKG_ROOT}/CHANGELOG.md")
  file(LOCK "${LOCAL_PATH}.lock")
  file(DOWNLOAD "${URL}" "${LOCAL_PATH}" STATUS DOWNLOAD_STATUS LOG DOWNLOAD_LOG)
  if(EXISTS ${LOCAL_PATH})
    file(READ "${LOCAL_PATH}" CONTENT LIMIT 1024)
    string(REPLACE "\r\n" "\n" CONTENT "${CONTENT}")
    string(REPLACE "\r" "\n" CONTENT "${CONTENT}")
    if(CONTENT MATCHES "## +([^\n]+)\n(.*)")
      set(LATEST_VERSION ${CMAKE_MATCH_1})
      string(FIND "${CONTENT}" "\n\n" END_IDX)
      string(SUBSTRING "${CONTENT}" 0 ${END_IDX} LATEST_CHANGELOG)
      if(${LATEST_VERSION} VERSION_GREATER ${ARCPKG_VERSION})
        arcpkg_cmd_echo("Found newer version of arcpkg.cmake: ${LATEST_VERSION}, current version is ${ARCPKG_VERSION}.
   It's recommended to upgrading to the latest version.
   You could upgrade it from https://git.arcsoft.com.cn/lny1856/arcpkg/raw/master/arcpkg.cmake manually or
   Run `cmake -P arcpkg.cmake upgrade-arcpkg` to download the latest version.`")
      elseif(A_VERBOSE)
        arcpkg_cmd_echo("There are no updates is available, latest version is ${ARCPKG_VERSION}.")
      endif()
      string(REPLACE "\n" "\n   " LATEST_CHANGELOG "${LATEST_CHANGELOG}")
      arcpkg_cmd_echo("${LATEST_CHANGELOG}")
    else()
      arcpkg_error_i("Could not find ARCPKG_VERSION in ${LOCAL_PATH}, SHOULD NOT HAPPEN!")
    endif()
  elseif(A_VERBOSE)
    arcpkg_warn("Fail to check latest version!")
  endif()
  file(LOCK "${LOCAL_PATH}.lock" RELEASE)
endfunction(arcpkg_cmd_check_updates)


# Upgrade arcpkg.cmake
function(arcpkg_cmd_upgrade_self)
  set(REMOTE_PATH "https://git.arcsoft.com.cn/lny1856/arcpkg/-/raw/master/arcpkg.cmake")
  set(LOCAL_PATH "${ARCPKG_SCRIPT}.upgraded")
  arcpkg_cmd_echo("Download ${REMOTE_PATH}")
  file(DOWNLOAD ${REMOTE_PATH} ${LOCAL_PATH} STATUS DOWNLOAD_STATUS)
  if(NOT DOWNLOAD_STATUS EQUAL 0)
    file(REMOVE ${LOCAL_PATH})
    arcpkg_cmd_echo("Fail to download ${REMOTE_PATH} with status: ${DOWNLOAD_STATUS}!")
  else()
    file(RENAME "${LOCAL_PATH}" "${ARCPKG_SCRIPT}")
    arcpkg_cmd_echo("${ARCPKG_SCRIPT} was upgraded to latest version!")
  endif()
endfunction(arcpkg_cmd_upgrade_self)


###########################################################
# Main entry, called by "cmake -P arcpkg.cmake"
###########################################################

function(arcpkg_main)
  # parse command line arguments
  set(IDX 1)
  set(SCRIPT_IDX 0)
  set(ARCPKG_ARGC 0)
  set(ARCPKG_PARSED_ARGV)
  while(${IDX} LESS ${CMAKE_ARGC})
    set(ARG ${CMAKE_ARGV${IDX}})
    # message("${IDX}: ${ARG}")
    if(ARG STREQUAL "-P")
      math(EXPR SCRIPT_IDX "${IDX}+1")
    elseif(ARG STREQUAL "--")
      # ignore
    elseif(IDX EQUAL SCRIPT_IDX)
      # ignore script
    elseif(ARG MATCHES "-j([0-9]+)")
      set(ARCPKG_ARGV_JOBS ${CMAKE_MATCH_1})
      unset(LAST_ARGV_NAME)
      list(APPEND ARCPKG_PARSED_ARGV ${ARCPKG_ARGV_JOBS})
    elseif(ARG MATCHES "^--?([^-].*)$")
      set(LAST_ARGV_NAME ${CMAKE_MATCH_1})
      if(LAST_ARGV_NAME STREQUAL "v")
        set(LAST_ARGV_NAME "VERBOSE")
      elseif(LAST_ARGV_NAME STREQUAL "p")
        set(LAST_ARGV_NAME "PLATFORM")
      elseif(LAST_ARGV_NAME STREQUAL "a")
        set(LAST_ARGV_NAME "ARCH")
      elseif(LAST_ARGV_NAME STREQUAL "c")
        set(LAST_ARGV_NAME "CONFIG")
      elseif(LAST_ARGV_NAME STREQUAL "j")
        set(LAST_ARGV_NAME "JOBS")
      else()
        string(TOUPPER "${LAST_ARGV_NAME}" LAST_ARGV_NAME)
      endif()
      list(APPEND ARCPKG_PARSED_ARGV ARCPKG_ARGV_${LAST_ARGV_NAME})
      # message("LAST_ARGV_NAME: ${LAST_ARGV_NAME}")
    elseif(LAST_ARGV_NAME)
      list(APPEND ARCPKG_ARGV_${LAST_ARGV_NAME} ${ARG})
    else()
      set(ARCPKG_ARGV${ARCPKG_ARGC} ${ARG})
      # message("${IDX}: ${ARCPKG_ARGC}: |${ARG}|")
      math(EXPR ARCPKG_ARGC "${ARCPKG_ARGC}+1")
    endif()
    math(EXPR IDX "${IDX}+1")
    if(NOT ARG MATCHES "^-")
      unset(LAST_ARGV_NAME)
    endif()
  endwhile()
  # non-positional variables
  set(POSITIONAL_ARGS)
  set(IDX 1)
  while(IDX LESS ARCPKG_ARGC)
    list(APPEND POSITIONAL_ARGS ${ARCPKG_ARGV${IDX}})
    math(EXPR IDX "${IDX}+1")
  endwhile()
  # message("POSITIONAL_ARGS: ${POSITIONAL_ARGS}")
  # message("ARCPKG_PARSED_ARGV: ${ARCPKG_PARSED_ARGV}")
  # define non-value options
  foreach(X ${ARCPKG_PARSED_ARGV})
    if(NOT DEFINED ${X})
      set(${X} "")
    endif()
  endforeach()
  get_filename_component(SCRIPT_NAME "${ARCPKG_SCRIPT}" NAME)
  set(CMD "cmake -P ${SCRIPT_NAME} [-v <verbose>]")
  if(ARCPKG_ARGV0 MATCHES "(pack|export|upload|update|install)")
    set(CMD "${CMD}  [-j<jobs>]")
  endif()
  set(CMD_ARGV0 "${CMD} ${ARCPKG_ARGV0}")
  if(ARCPKG_ARGV_VERBOSE)
    set(ARCPKG_VERBOSE ${ARCPKG_ARGV_VERBOSE})
  endif()
  if(ARCPKG_ARGV_JOBS)
    set(ARCPKG_NUM_JOBS ${ARCPKG_ARGV_JOBS})
  endif()
  # distribute sub-command
  if(ARCPKG_ARGV0 STREQUAL "version")
    arcpkg_cmd_echo("arcpkg.cmake ${ARCPKG_VERSION}

Homepage: https://git.arcsoft.com.cn/lny1856/arcpkg
Maintainer: Lin Naiyang <<EMAIL>>
")
  elseif(ARCPKG_ARGV0 STREQUAL "arcpkg-updates")
    arcpkg_cmd_check_updates(FORCE VERBOSE)
  elseif(ARCPKG_ARGV0 STREQUAL "upgrade-arcpkg")
    arcpkg_cmd_upgrade_self()
  elseif(ARCPKG_ARGV0 STREQUAL "pack")
    if(ARCPKG_ARGC LESS 2)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <build-dir> [pkg] [-c <config>]")
    else()
      arcpkg_cmd_pack("${ARCPKG_ARGV1}" "${ARCPKG_ARGV2}" "${ARCPKG_ARGV_CONFIG}")
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "export")
    if(ARCPKG_ARGC LESS 2)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <build-dir> [pkg]")
    else()
      arcpkg_cmd_export("${ARCPKG_ARGV1}" "${ARCPKG_ARGV2}")
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "upload")
    if(ARCPKG_ARGC LESS 2)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <build-dir> [pkg]")
    else()
      arcpkg_cmd_upload("${ARCPKG_ARGV1}" "${ARCPKG_ARGV2}")
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "update")
    if(ARCPKG_ARGC LESS 2)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <build-dir> [pkg]")
    else()
      arcpkg_cmd_update("${ARCPKG_ARGV1}" "${ARCPKG_ARGV2}")
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "install")
    if(ARCPKG_ARGC LESS 2)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <pkg>... [-p <platform>] [-a <arch>]...")
    else()
      string(REPLACE " " ";" ARCPKG_ARGV_ARCH "${ARCPKG_ARGV_ARCH}")
      arcpkg_cmd_install("${ARCPKG_ARGV_PLATFORM}" "${ARCPKG_ARGV_ARCH}" ${POSITIONAL_ARGS})
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "search")
    # https://docs.conan.io/en/latest/reference/commands/consumer/search.html
    # https://vcpkg.readthedocs.io/en/latest/commands/search/
    if(NOT ARCPKG_ARGC EQUAL 2)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <pattern>

Supported patterns:
1. <name>, e.g. 'Eigen', 'toy_*'.
2. <name>@<user>, e.g. 'Eigen@arcpkg', 'toy_*@arcpkg'.
3. <name>/<version>, e.g. 'Eigen/3.*', 'toy_*/1.0'.
4. <name>/<version>@<user>, e.g. 'Eigen/3.*@arcpkg'.
")
    else()
      arcpkg_cmd_search(${POSITIONAL_ARGS})
    endif()
  elseif(ARCPKG_ARGV0 MATCHES "^(rm|remove)$")
    if(ARCPKG_ARGC LESS 2)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <local-cache-dir-pattern> [--all|--local|--remote]")
    else()
      if(ARCPKG_ARGV_ALL)
        set(REMOVE_MODE "LOCAL;REMOTE")
      elseif(ARCPKG_ARGV_REMOTE)
        set(REMOVE_MODE "REMOTE")
      else()
        set(REMOVE_MODE "LOCAL")
      endif()
      arcpkg_cmd_remove("${REMOVE_MODE}" ${POSITIONAL_ARGS})
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "install_file")
    if(ARCPKG_ARGC LESS 2)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <remote-zip-path|local-zip-path> [<local-cache-dir>]")
    else()
      get_filename_component(ZIP_PATH_ABS "${ARCPKG_ARGV1}" REALPATH)
      if(EXISTS ${ARCPKG_ARGV1} AND NOT IS_DIRECTORY ${ZIP_PATH_ABS})
        arcpkg_install_local_zip(${ARCPKG_ARGV1} "${ARCPKG_ARGV2}")
      else()
        arcpkg_install_remote_zip(${ARCPKG_ARGV1} "${ARCPKG_ARGV2}")
      endif()
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "upload_file")
    if(ARCPKG_ARGC LESS 3)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <local-path> <remote-path>")
    else()
      arcpkg_upload_file(${ARCPKG_ARGV1} ${ARCPKG_ARGV2})
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "download_file")
    if(ARCPKG_ARGC LESS 3)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <remote-path> <local-path>")
    else()
      arcpkg_download_file(${ARCPKG_ARGV1} ${ARCPKG_ARGV2})
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "py_zip_dir")
    if(ARCPKG_ARGC LESS 3)
      arcpkg_cmd_echo("Usage: ${CMD_ARGV0} <zip-path> <data-dir>")
    else()
      arcpkg_py_zip_dir(${ARCPKG_ARGV1} ${ARCPKG_ARGV2})
    endif()
  elseif(ARCPKG_ARGV0 STREQUAL "generate_ld_version_script")
    if(ARCPKG_ARGC LESS 3)
      message(FATAL_ERROR "Usage: ${CMD_ARGV0} <version-script> <inc> [inc...]")
    else()
      set(VERSION_SCRIPT_FILE ${ARCPKG_ARGV1})
      list(REMOVE_AT POSITIONAL_ARGS 0)
      arcpkg_collect_global_symbols(GLOBAL_SYMBOLS ${POSITIONAL_ARGS})
      arcpkg_write_ld_version_script(${VERSION_SCRIPT_FILE} ${GLOBAL_SYMBOLS})
    endif()
  else()
    if(NOT ARCPKG_ARGV0 MATCHES "^(help|)$")
      arcpkg_cmd_echo("Unknown command: ${ARCPKG_ARGV0}")
    endif()
    arcpkg_cmd_echo("Usage: ${CMD} help|version|pack|export|upload|update|install|search|remove|arcpkg-updates|upgrade-arcpkg
  help            Print this help.
  version         Print arcpkg.cmake version.
  pack            Build and pack pkg in build directory.
  export          Export pkg in build directory to local cache (~/.arcpkg).
  upload          Upload pkg in build directory to remote repository.
  update          Update pkg in build directory from remote repository.
  install         Install pkg from remote repository to local cache (~/.arcpkg).
  search          Search pkg in remote repository.
  remove          Remove pkg in local cache (~/.arcpkg) and remote repository.
  arcpkg-updates  Check updates of arcpkg.cmake.
  upgrade-arcpkg  Upgrade arcpkg.cmake.
")
  endif()
endfunction(arcpkg_main)

arcpkg_main()
