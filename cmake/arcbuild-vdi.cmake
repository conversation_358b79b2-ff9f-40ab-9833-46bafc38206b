# Author: <PERSON><PERSON><PERSON> <<EMAIL>>
# Homepage: https://code.arcsoft.com.cn/lny1856/arcbuild2

cmake_minimum_required(VERSION 3.6)

###########################################################
# User Setup
###########################################################

# Use tags in https://code.arcsoft.com.cn/lny1856/arcbuild2/tags is recommended for stable!
set(ARCBUILD_VERSION master) # which version to be used, master or available tags (recommended).
set(ARCBUILD_CLASSICAL_MODE OFF) # generate and build in one command if ON.

# CHANGELOG: https://code.arcsoft.com.cn/lny1856/arcbuild2/blob/master/CHANGELOG.md

###########################################################
# Global variables
###########################################################

# select ARCBUILD_HOME, the root directory for arcbuild
if(NOT ARCBUILD_HOME)
  set(ARCBUILD_HOME "$ENV{ARCBUILD_HOME}")
endif()
if(NOT ARCBUILD_HOME)
  set(ARCBUILD_HOME "~/.arcbuild")
endif()

# bootstrap verbose
if(DEFINED VERBOSE)
  set(ARCBUILD_VERBOSE ${VERBOSE})
elseif(NOT DEFINED ARCBUILD_VERBOSE)
  set(ARCBUILD_VERBOSE 2)
endif()


###########################################################
# Message functions
###########################################################

function(arcbuild_debug_)
  if(ARCBUILD_VERBOSE GREATER 2)
    message(STATUS "ARCBUILD/D: " ${ARGN})
  endif()
endfunction()

function(arcbuild_echo_)
  if(ARCBUILD_VERBOSE GREATER 1)
    message(STATUS "ARCBUILD/I: " ${ARGN})
  endif()
endfunction()

function(arcbuild_warn_)
  if(ARCBUILD_VERBOSE GREATER 0)
    message(STATUS "ARCBUILD/W: " ${ARGN})
  endif()
endfunction()

function(arcbuild_error_)
  message(FATAL_ERROR "ARCBUILD/E: " ${ARGN})
endfunction()


###########################################################
# Wrapper helper functions
###########################################################

function(arcbuild_boot_get_version_id VERSION_ID_VAR LOCAL_JSON_PATH VERSION)
  set(REMOTE_PATH "https://code.arcsoft.com.cn/api/v4/projects/lny1856%2Farcbuild2/repository/commits/${VERSION}")
  arcbuild_debug_("Downloading ${REMOTE_PATH}...")
  file(DOWNLOAD ${REMOTE_PATH} ${LOCAL_JSON_PATH} STATUS DOWNLOAD_STATUS)
  if(DOWNLOAD_STATUS EQUAL 6)
    # 6: Couldn't resolve host name
    set(${VERSION_ID_VAR} "" PARENT_SCOPE)
    return()
  elseif(DOWNLOAD_STATUS EQUAL 0)
    file(READ "${LOCAL_JSON_PATH}" JSON_CONTENT)
    string(REGEX MATCH "\"id\":\"([^\"]+)\"" VERSION_ID "${JSON_CONTENT}")
    if(NOT CMAKE_MATCH_1)
      arcbuild_error_("Fail to parse commit id from ${LOCAL_JSON_PATH}!")
    endif()
    set(${VERSION_ID_VAR} ${CMAKE_MATCH_1} PARENT_SCOPE)
  else()
    arcbuild_error_("Fail to download ${REMOTE_PATH} with status: ${DOWNLOAD_STATUS}!")
  endif()
endfunction()

function(arcbuild_boot_self_install ARCBUILD_DIR_VAR VERSION)
  set(ARCBUILD_REMOTE_URL "https://code.arcsoft.com.cn/lny1856/arcbuild2/-/archive/${VERSION}/arcbuild2-${VERSION}.zip")
  set(REMOTE_PATH ${ARCBUILD_REMOTE_URL})
  get_filename_component(ZIP_NAME ${REMOTE_PATH} NAME)
  get_filename_component(LOCAL_ROOT "${ARCBUILD_HOME}/wrapper" REALPATH)
  string(REPLACE ".zip" "" ZIP_NAME_WO_ExT ${ZIP_NAME})
  set(LOCAL_ZIP_PATH "${LOCAL_ROOT}/${ZIP_NAME}")
  set(LOCAL_UNZIP_DIR "${LOCAL_ROOT}/${ZIP_NAME_WO_ExT}")
  set(VER_FILE_PATH "${LOCAL_UNZIP_DIR}.ver.txt")
  set(LOCK_FILE_PATH "${LOCAL_UNZIP_DIR}.lock")
  set(${ARCBUILD_DIR_VAR} "${LOCAL_UNZIP_DIR}/arcbuild" PARENT_SCOPE)

  # lock
  file(LOCK "${LOCK_FILE_PATH}")

  # get commit id for branches
  if(VERSION MATCHES "^[.0-9]+$" OR VERSION MATCHES "^v[.0-9]+$")
    set(VERSION_ID "${VERSION}")
  else()
    arcbuild_boot_get_version_id(VERSION_ID "${LOCAL_UNZIP_DIR}.ver.json" ${VERSION})
  endif()

  # check unzip directory
  arcbuild_echo_("Required arcbuild version id: ${VERSION}/${VERSION_ID}")
  if(EXISTS ${VER_FILE_PATH})
    file(READ "${VER_FILE_PATH}" LOCAL_VERSION_ID)
    arcbuild_echo_("Local arcbuild version id: ${LOCAL_VERSION_ID}")
    if(LOCAL_VERSION_ID STREQUAL VERSION_ID OR NOT VERSION_ID)
      file(LOCK "${LOCK_FILE_PATH}" RELEASE)
      return() # early return
    endif()
  endif()

  # remove old directory
  if(EXISTS ${LOCAL_UNZIP_DIR})
    arcbuild_warn_("Dirty unzip directory, delete it!")
    file(REMOVE_RECURSE ${LOCAL_UNZIP_DIR})
  endif()

  # download zip file
  arcbuild_echo_("Downloading ${REMOTE_PATH}...")
  file(DOWNLOAD ${REMOTE_PATH} ${LOCAL_ZIP_PATH} STATUS DOWNLOAD_STATUS)
  if(NOT DOWNLOAD_STATUS EQUAL 0)
    arcbuild_error_("Fail to download ${REMOTE_PATH} with status: ${DOWNLOAD_STATUS}!")
  endif()
  arcbuild_echo_("Downloading ${REMOTE_PATH} [DONE]")

  # unzip
  if(NOT EXISTS "${LOCAL_UNZIP_DIR}")
    arcbuild_echo_("Unzipping ${ZIP_NAME}...")
    execute_process(
      COMMAND ${CMAKE_COMMAND} -E tar zxf ${ZIP_NAME}
      WORKING_DIRECTORY ${LOCAL_ROOT}
      RESULT_VARIABLE UNZIP_RET
      )
    if(NOT UNZIP_RET EQUAL 0)
      arcbuild_warn_("Unzipping failed, clean unzip directory!")
      file(REMOVE_RECURSE ${LOCAL_UNZIP_DIR})
    endif()
    arcbuild_echo_("Unzipping ${ZIP_NAME} [DONE]")
  endif()

  # write VERSION
  arcbuild_echo_("Write ${VER_FILE_PATH}")
  file(WRITE "${VER_FILE_PATH}" "${VERSION_ID}")

  # unlock
  file(LOCK "${LOCK_FILE_PATH}" RELEASE)
endfunction()

### Wrapper
set(ARCBUILD "${CMAKE_CURRENT_LIST_DIR}/arcbuild")
if(EXISTS "${ARCBUILD}/boot.cmake")
  include(${ARCBUILD}/boot.cmake)
else()
  arcbuild_boot_self_install(ARCBUILD "${ARCBUILD_VERSION}")
  include(${ARCBUILD}/boot.cmake)
endif()
