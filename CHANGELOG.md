## 0.4.15
1. 规划新增scenario_state状态输出
2. 精简dlog信息
3. 接入底盘speed_limit、time_gap、autodrive_mode信号
4. 优化纵向速度dp跟踪效果
5. 修复变道选线跳变问题

## 0.4.14
1. 更新arcsoft_ads_noa_i接口至0.4版本

## 0.3.13
1. 改进日志: 允许下游使用者运行期修改 planning 模块日志等级
2. 增加速度拼接算法，提升纵向跟踪效果
3. 调整跟车距离计算方式，允许控制一定范围内跟踪误差，提升舒适性
4. 修复变道选线异常，状态无法跳回等问题
5. 增加取消拨杆变道，返回本车道功能
6. 修复路径优化中jerk无法约束问题
7. 增加分流选线功能
8. 增加规划debug信息dlog输出

## 0.3.12
1. 优化速度规划参数
2. 增加障碍物多帧碰撞过滤
3. 接入dlog，输出规划ST图信息

## 0.3.11
1. 接入高频定位接口
2. 增加限速软约束接口

## 0.3.10
1. 调整横向规划参数
2. 添加拨杆变道功能

## 0.3.9
1. 修复速度规划jerk无约束问题
2. 更新跟车距离计算方式
3. 调整障碍物权重
4. 更新速度优化问题构造形式及参数

## 0.3.8
1. 更新osqp至0.5.0
2. 解决内存泄漏

## 0.3.7
1. 解决部分内存泄漏问题
2. 调整速度规划参数
3. 接入转向灯拨杆信号

## 0.3.6
1. 调整jerk_bound至6，修复加减速过慢问题
2. 调整限速至80km/h，测试高架场景

## 0.3.5
1. 调整超速权重至1e3，修复无法减速问题

## 0.3.4
1. 降低限速至60km/h
2. 轨迹拼接逻辑取消接入智驾模式
3. 规划失败时，智驾模式发送OFF，退出智驾
4. 增大路径优化jerk bound至1.0
5. 增加速度规划下障碍物决策，提升速度优化效果

## 0.3.3
1. 接入感知给入定位加速度信息
2. 使用第二模态预测
3. 修复速度优化失败问题
4. 适配arcsoft_ads_noa_i/0.3接口

## 0.2.2
1. 修复缩减优化迭代次数导致求解失败问题

## 0.2.1
1. 优化耗时问题
2. 修复部分场景下优化失败问题
3. 增加空指针引起的crash保护
4. 增加分流进匝道选线功能

## 0.2.0
- 初始版本
包含功能：
1. LCC+ 车辆横向控制车道保持
2. ACC+ 多动态障碍物下自适应速度规划

包含模块：
1. 参考线平滑模块
2. 路径平滑模块
3. 障碍物碰撞检测模块
4. 速度规划、速度平滑模块