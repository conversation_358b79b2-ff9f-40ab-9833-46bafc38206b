/* Default header fonts are ugly */
@import url(https://fonts.googleapis.com/css?family=Open+Sans:400,400i,600,600i,700,700i);
@import url(https://fonts.googleapis.com/css?family=Inconsolata);

h1, h2, h3, h4, h5, h6 {
    font-family:  "Open Sans", "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-weight: 600;
}
body {
  font-family: 'Open Sans', sans-serif;
}

.wy-nav-top a {
    font-size: 175%;
    font-weight: 600;
}

/* Remove rounded navigation bar */
.wy-side-nav-search input[type=text] {
    border-radius: 2px;
}

div[class^='highlight'] pre {
    font-family: 'Inconsolata', monospace;
    font-size: 16px;
    background: #f3f6f6;
}

/* Make glossary entries bolder to stand out */
dl.glossary>dt{
    font-weight: 700;
}

/* Make bold things less bold */
b, strong {
    font-weight: 600;
}

/* Less bold table head */
.wy-table thead th, .rst-content table.docutils thead th, .rst-content table.field-list thead th {
    font-weight: 600;
}

/* Less bold current active element */
.wy-menu-vertical li.on a, .wy-menu-vertical li.current>a {
    font-weight: 600;
}


/* Types link refs less bold */
.rst-content tt.xref, a .rst-content tt, .rst-content tt.xref, .rst-content code.xref, a .rst-content tt, a .rst-content code {
    font-weight: normal;
}

/* Make types in C/C++ docs less bold */
.rst-content dl dt {
    font-weight: 600;
}


/* Link Styling */
a,a:visited, a:focus {
	color: rgb(46, 118, 176);  /* Light blue*/
	text-decoration: none;
}
a:hover, a:active {
	color: rgb(0, 32, 72);  /*  blue*/
	text-decoration: none;
}



/* Make bar oxford blue*/
.wy-side-nav-search {
    background-color: rgb(0, 32, 72); /* Oxford blue */
}

.wy-nav-side {
    background: rgb(0, 32, 72); /* Oxford blue */
}


.wy-nav-content-wrap, .wy-menu li.current > a  {
    background-color: #fff;
    /* font-weight: 600; */
}

@media screen and (min-width: 1400px) {
    .wy-nav-content-wrap {
        /*background-color: rgba(0, 0, 0, 0.0470588);*/
        background-color: rgb(245, 245, 245); /* Light grey */
    }

    .wy-nav-content {
        background-color: #fff;
    }
}

.wy-nav-top {
    background-color: rgb(0, 32, 72); /* Oxford blue */
}


/* Change color of the line at the top of Read the Docs box at the bottom left
   corner */
.rst-versions{
    border-top:solid 10px rgb(0, 32, 72); /* Oxford blue */
}


/* Change color of top left name */
.wy-side-nav-search>a{
    color: rgb(255, 255, 255); /* White text*/
}
.wy-side-nav-search>div.version{
    color: rgb(245, 245, 245); /* Light grey text*/
}
.wy-nav-top a{
    color: rgb(255, 255, 255); /* White text*/
}



/* Navigation bar text */
.wy-menu-vertical header, .wy-menu-vertical p.caption {
    color: rgb(255, 255, 255); /* White text*/
    font-weight: 600;
}

.wy-menu-vertical a{
    color: rgb(178, 188, 200); /* Grey */
}

.wy-menu-vertical a:hover{
    background: rgb(46, 118, 176);
    color: rgb(255, 255, 255); /* White text*/
}






/* Change text color for tt literal */
.rst-content tt.literal, .rst-content tt.literal, .rst-content code.literal{
    color: rgb(46, 118, 176);  /* Light blue*/
    font-size: 14px;
}

tr td code span{
   font-size: 13px;
}

.s1, .s2{
	color: rgb(46, 118, 176);  /* Light blue*/
}


/* Rubric bigger (initial page) */
.rst-content p.rubric {
    font-family:  "Open Sans", "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-weight: 600;
    font-size: 150%;
    margin-bottom: 20px;
}

/* Smaller rubric in C/C++ description */
p.breathe-sectiondef-title.rubric {
	font-size: 100%;
}




/* Remove box shadow from buttons */
.btn {
    font-weight: normal;
    font-family:  "Open Sans", "Helvetica Neue",Helvetica,Arial,sans-serif;
    box-shadow: none;
}
/* Force breathe display inline parameters */
/*.wy-plain-list-disc li p:last-child, .rst-content .section ul li p:last-child, .rst-content .toctree-wrapper ul li p:last-child, article ul li p:last-child{
    display: inline;
}*/


/* Remove line block indentation */
.rst-content .line-block {
    margin-bottom: 0px !important;
    margin-left: 0px !important;
}
