Interfaces
============


+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| Language                           | Maintainers                                              | Repository                                                                               |
+====================================+==========================================================+==========================================================================================+
| :ref:`C/C++ <c_cpp_interface>`     | | `Bartolomeo Stellato <<EMAIL>>`_ | `github.com/oxfordcontrol/osqp <https://github.com/oxfordcontrol/osqp>`_                 |
|                                    | | `Goran Banjac <<EMAIL>>`_           |                                                                                          |
|                                    | | `Paul Goulart <<EMAIL>>`_            |                                                                                          |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| :ref:`Python <python_interface>`   | | `Bartolomeo Stellato <<EMAIL>>`_ | `github.com/oxfordcontrol/osqp-python <https://github.com/oxfordcontrol/osqp-python>`_   |
|                                    | | `Goran Banjac <<EMAIL>>`_           |                                                                                          |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| :ref:`Matlab <matlab_interface>`   | | `Bartolomeo Stellato <<EMAIL>>`_ | `github.com/oxfordcontrol/osqp-matlab <https://github.com/oxfordcontrol/osqp-matlab>`_   |
|                                    | | `Goran Banjac <<EMAIL>>`_           |                                                                                          |
|                                    | | `Paul Goulart <<EMAIL>>`_            |                                                                                          |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| :ref:`Julia <julia_interface>`     | | `Twan Koolen <<EMAIL>>`_                       | `github.com/oxfordcontrol/OSQP.jl <https://github.com/oxfordcontrol/OSQP.jl>`_           |
|                                    | | `Benoît Legat <<EMAIL>>`_            |                                                                                          |
|                                    | | `Bartolomeo Stellato <<EMAIL>>`_ |                                                                                          |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| `R`_                               | | `Bartolomeo Stellato <<EMAIL>>`_ | `github.com/oxfordcontrol/osqp-r <https://github.com/oxfordcontrol/osqp-r>`_             |
|                                    | | `Paul Goulart <<EMAIL>>`_            |                                                                                          |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| `Rust <https://docs.rs/osqp/>`_    | | `Ed Barnard <<EMAIL>>`_                    | `github.com/oxfordcontrol/osqp.rs <https://github.com/oxfordcontrol/osqp.rs>`_           |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| :ref:`Fortran <fortran_interface>` | | `Nick Gould <<EMAIL>>`_                  | `github.com/oxfordcontrol/osqp-fortran <https://github.com/oxfordcontrol/osqp-fortran>`_ |
|                                    | | `Bartolomeo Stellato <<EMAIL>>`_ |                                                                                          |
|                                    | | `Paul Goulart <<EMAIL>>`_            |                                                                                          |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| :ref:`Cutest <cutest_interface>`   | | `Nick Gould <<EMAIL>>`_                  | `github.com/ralna/CUTEst <https://github.com/ralna/CUTEst/tree/master/src/osqp>`_        |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+
| `Eigen`_                           | | `Giulio Romualdi <<EMAIL>>`_         | `github.com/robotology/osqp-eigen <https://github.com/robotology/osqp-eigen>`_           |
+------------------------------------+----------------------------------------------------------+------------------------------------------------------------------------------------------+

.. toctree::
   :maxdepth: 1
   :glob:
   :hidden:

   CC++.rst
   python.rst
   julia.rst
   matlab.rst
   r.rst
   fortran.rst
   rust.rst
   cutest.rst
   solver_settings.rst
   linear_systems_solvers.rst
   status_values.rst



.. _Eigen: https://robotology.github.io/osqp-eigen/doxygen/doc/html/index.html

.. _R: https://cran.r-project.org/web/packages/osqp/
