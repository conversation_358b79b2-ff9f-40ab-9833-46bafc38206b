CUTEst
=======

To be able to use OSQP and `CUTEst <https://ccpforge.cse.rl.ac.uk/gf/project/cutest/wiki/>`_ you need to

* Install `CUTEst <https://ccpforge.cse.rl.ac.uk/gf/project/cutest/wiki/>`_
* Compile :ref:`OSQP from sources <build_from_sources>`
* Set the environment variable :code:`OSQP` to the main OSQP directory containing source code for which the compiled binary libraries lie in :code:`$OSQP/build/out`.

For more details, see the `README.osqp <https://ccpforge.cse.rl.ac.uk/svn/cutest/cutest/trunk/src/osqp/README.osqp>`_ file in the CUTEst repository.


