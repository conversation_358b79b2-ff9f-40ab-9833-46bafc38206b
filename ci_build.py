from __future__ import print_function
import os
import re
import shutil
import subprocess


def log(msg):
    print('ci_build.py: ' + msg)


def run(cmd):
    log(cmd)
    subprocess.check_call(cmd, shell=True)


def get_version(path):
    it = re.finditer(r'^## (\S+)', open(path).read())
    return next(it).group(1)


def main():
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('-p', '--platform', help='platform', required=True)
    parser.add_argument('-a', '--arch', nargs='*', help='architectures')
    parser.add_argument('-c', '--config', nargs='+', default=['Release'], help='configurations')
    parser.add_argument('-b', '--build_dir', help='build directory, default is build/<platform>_<arch>')
    parser.add_argument('-G', '--generator', help='cmake generator')
    parser.add_argument('-t', '--tag', help='tag', default=os.environ.get('CI_COMMIT_TAG', '').strip())
    parser.add_argument('-r', '--run_test', help='run unit tests', action='store_true')
    parser.add_argument('-j', '--jobs', help='tells make to execute many recipes simultaneously', type=int, default=2)
    args = parser.parse_args()

    # version = get_version('CHANGELOG.md')
    # assert not args.tag or args.tag == version, 'tag (%s) != version (%s)' % (args.tag, version)

    platform_arch = '-p {}'.format(args.platform)
    if args.arch:
        platform_arch += ' -a "{}"'.format(';'.join(args.arch))

    if args.build_dir:
        build_dir = args.build_dir
    else:
        build_dir = 'build/' + '_'.join([args.platform] + (args.arch or []))
        if args.mt:
            build_dir += '-mt'

    shutil.rmtree(build_dir + '/install', ignore_errors=True)
    generator_args = ''
    if args.generator:
        generator_args = '-G "{}"'.format(args.generator)
    toolset_args = ''
    if args.platform.startswith('vs') and args.generator not in ['Ninja', 'Ninja Multi-Config']:
        toolset_args = '-T host=x64'
    run('cmake -P cmake/arcbuild.cmake {} -S . -B {} {} {}'.format(platform_arch, build_dir, toolset_args, generator_args))
    if args.run_test:
        run('cmake --build {} --config Release -j {}'.format(build_dir, args.jobs))
        run('cd {} && ctest --output-on-failure -C Release -j2'.format(build_dir))

    # for cfg in args.configs:
        # run('cmake --build {} --config {} -j {}'.format(build_dir, cfg, args.jobs))
    run('cmake -P cmake/arcpkg.cmake pack {} ads-arcsoft_ads_planning -c "{}" -j {}'.format(build_dir, ';'.join(args.config), args.jobs))
    run('cmake -P cmake/arcpkg.cmake upload {} ads-arcsoft_ads_planning'.format(build_dir))


if __name__ == "__main__":
    main()
