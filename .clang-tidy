Checks: >
  -*,
  clang-analyzer-core.CallAndMessage,
  clang-analyzer-core.Uninitialized,
  clang-analyzer-core.NullDereference
  bugprone-undelegated-constructor,
  bugprone-argument-comment,
  bugprone-bad-signal-to-kill-thread,
  bugprone-bool-pointer-implicit-conversion,
  bugprone-copy-constructor-init,
  bugprone-dangling-handle,
  bugprone-forward-declaration-namespace,
  bugprone-fold-init-type,
  bugprone-inaccurate-erase,
  bugprone-incorrect-roundings,
  bugprone-infinite-loop,
  bugprone-integer-division,
  bugprone-macro-repeated-side-effects,
  bugprone-misplaced-operator-in-strlen-in-alloc,
  bugprone-misplaced-pointer-arithmetic-in-alloc,
  bugprone-misplaced-widening-cast,
  bugprone-move-forwarding-reference,
  bugprone-multiple-statement-macro,
  bugprone-parent-virtual-call,
  bugprone-posix-return,
  bugprone-reserved-identifier,
  bugprone-signed-char-misuse,
  bugprone-sizeof-container,
  bugprone-sizeof-expression,
  bugprone-string-constructor,
  bugprone-string-integer-assignment,
  bugprone-string-literal-with-embedded-nul,
  bugprone-suspicious-enum-usage,
  bugprone-suspicious-include,
  bugprone-suspicious-memset-usage,
  bugprone-suspicious-missing-comma,
  bugprone-suspicious-string-compare,
  bugprone-swapped-arguments,
  bugprone-terminating-continue,
  bugprone-throw-keyword-missing,
  bugprone-too-small-loop-variable,
  bugprone-undefined-memory-manipulation,
  bugprone-unhandled-self-assignment,
  bugprone-unused-raii,
  bugprone-unused-return-value,
  bugprone-use-after-move,
  bugprone-virtual-near-miss,
  # bugprone-macro-parentheses,
  # bugprone-narrowing-conversions,
  # bugprone-exception-escape,

  performance-faster-string-find,
  performance-for-range-copy,
  performance-implicit-conversion-in-loop,
  performance-inefficient-algorithm,
  performance-inefficient-vector-operation,
  performance-move-constructor-init,
  performance-no-automatic-move,
  performance-trivially-destructible,
  performance-unnecessary-copy-initialization,
  performance-move-const-arg,

  modernize-avoid-bind,
  modernize-loop-convert,
  modernize-make-shared,
  modernize-make-unique,
  modernize-raw-string-literal,
  modernize-redundant-void-arg,
  modernize-replace-auto-ptr,
  modernize-replace-random-shuffle,
  modernize-use-auto,
  modernize-use-bool-literals,
  modernize-use-nullptr,
  modernize-use-using,
  modernize-use-override,
  # modernize-use-equals-default,
  modernize-use-equals-delete,

  misc-throw-by-value-catch-by-reference,
  misc-misplaced-const,
  misc-unconventional-assign-operator,
  misc-redundant-expression,
  misc-static-assert,
  misc-unconventional-assign-operator,
  misc-uniqueptr-reset-release,
  misc-unused-alias-decls,
  misc-unused-parameters,
  misc-unused-using-decls,

  readability-avoid-const-params-in-decls
  readability-const-return-type,
  readability-container-size-empty,
  readability-delete-null-pointer,
  readability-deleted-default,
  readability-misplaced-array-index,
  readability-non-const-parameter,
  readability-redundant-control-flow,
  readability-redundant-function-ptr-dereference,
  readability-redundant-smartptr-get,
  readability-redundant-string-cstr,
  readability-redundant-string-init,
  readability-static-definition-in-anonymous-namespace,
  readability-string-compare,
  readability-uniqueptr-delete-release,
  readability-simplify-subscript-expr,
  readability-simplify-boolean-expr,
  readability-inconsistent-declaration-parameter-name,
  # readability-qualified-auto,

  cert-flp30-c,
  cert-mem57-cpp,
  cert-oop58-cpp,
  google-build-explicit-make-pair,
  google-runtime-operator,
  hicpp-exception-baseclass,
  cppcoreguidelines-virtual-class-destructor,
WarningsAsErrors: "*"
HeaderFilterRegex: '.*'
CheckOptions:
  - key: performance-move-const-arg.CheckTriviallyCopyableMove
    value: false