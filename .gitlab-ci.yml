.build:
  when: on_success
  tags: [docker]
  stage: build
  only: [main, merge_requests, tags]

.windows:
  extends: .build
  tags: [windows]
  before_script:
  - CHCP 65001

vs2019-x64:
  extends: .windows
  script:
  - python ci_build.py -p vs2019 -a x64 -b build -c Debug Release

linux-x64:
  extends: .build
  image: artifactory.arcsoft.com.cn:6555/lny1856/ubuntu-mesa-gtk2-ffmpeg:focal
  script:
  - python ci_build.py -p linux -a x64 -b build

qnx710-aarch64:
  extends: .build
  image: artifactory.arcsoft.com.cn:6555/lny1856/qnx-sdp:710
  script:
  - python ci_build.py -p qnx710 -a aarch64 -b build
  #- python scripts/jenkins_client.py delivery_demo_exe --project_id 23186-1 -p qnx710 -a aarch64 -b build --target_type exe --pkg_name ads_planning_h264_demo --pkg_user ads
  #- python scripts/jenkins_client.py push_demo_exe --project_id 23186-1 --push_target_project_id 23574-1 -p qnx710 -a aarch64 -b build --target_type exe --pkg_name ads_planning_h264_demo --pkg_user ads

# android-r18b:
#   extends: .build
#   image: artifactory.arcsoft.com.cn:6555/lny1856/android-ndk:r18b
#   script:
#   - python ci_build.py -p android -a arm64 -b build
