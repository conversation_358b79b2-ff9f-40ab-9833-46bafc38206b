#include <arcsoft_ads_planning.h>
#include <arcsoft_ads_sense_frame.h>       // 内部算法结果
#include <arcsoft_ads_sense_frame_base.h>  // OEM算法结果

int main() {
    ArcAdsSenseFrame* sense_frame = nullptr;
    ArcAdsSenseFrame_create(nullptr, &sense_frame);

    // 从 sense_frame 获取数据
    const ArcAdsTime time = ArcAdsSenseFrame_getTime(sense_frame);
    const uint32_t frame_id = ArcAdsSenseFrame_getFrameId(sense_frame);

    // 定位信息
    const ArcAdsEgoMotionMeasurement* ego_motion =
        ArcAdsSenseFrame_getEgoMotionMeasurement(sense_frame);

    // 障碍物信息
    const ArcAdsObjectMeasurements* object_measurements =
        ArcAdsSenseFrame_getObjectMeasurements(sense_frame);

    // 局部地图
    const ArcAdsLocalMapMeasurementV2* local_map =
        ArcAdsSenseFrame_getLocalMapMeasurement(sense_frame);

    // 交通灯信息
    const ArcAdsTrafficLightLogicalAssembly* traffic_light_logical_assembly =
        ArcAdsSenseFrame_getTrafficLightLogicalAssembly(sense_frame);

    // 限速信息
    const ArcAdsSpeedLimits* speed_limits =
        ArcAdsSenseFrame_getSpeedLimits(sense_frame);

    // Nav_model信息
    const ArcAdsNavReferenceLines* reference_lines =
        ArcAdsSenseFrame_getNavReferenceLines(sense_frame);

    // 预测信息
    const ArcAdsMultiModalPrediction* multi_modal_prediction =
        ArcAdsSenseFrame_getMultiModalPrediction(sense_frame);

    ArcAdsPlanning* planning_engine = nullptr;
    ArcAdsPlanning_create(&planning_engine);

    ArcAdsPlanningTrajectory* out_trajectory = new ArcAdsPlanningTrajectory();

    // TODO: use the extracted data
    ArcAdsPlanning_process(planning_engine, out_trajectory, ego_motion,
                           object_measurements, local_map,
                           traffic_light_logical_assembly, speed_limits,
                           reference_lines, multi_modal_prediction);

    ArcAdsSenseFrame_destroy(sense_frame);
    ArcAdsPlanning_destroy(planning_engine);
    delete out_trajectory;

    return 0;
}