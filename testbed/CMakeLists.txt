add_executable(ads_planning_navi_model_demo ads_planning_navi_model_demo.cpp)
# add_executable(ads_planning_navi_model_demo ads_planning_h264_demo.cpp)

if(MSVC)
  set_property(TARGET ads_planning_navi_model_demo PROPERTY VS_DEBUGGER_ENVIRONMENT "PATH=$ENV{PATH}")
endif()

#set(STACK_SIZE "104857600")
if(WIN32)
    set(STACK_FLAG "/STACK:100000000")
else()
    set(STACK_FLAG "-Wl,-z,stack-size=104857600")
endif()

set(arcpkg_protobuf_VERSION_FORCE 3.21.9) 

if(NOT CMAKE_CROSSCOMPILING)
  arcpkg_import(
    imgui/docking-20250214-p1@arcpkg 
  )
endif()

arcpkg_import(
  CLI11/2.3.2@arcpkg
  spdlog/1.12.0@arcpkg
  fmt/10.2.1@arcpkg

  ads_devkit/0.3.8@ads # new
  arcsoft_ads_base/0.5.15@ads # new

  arcsoft_ads_rectify/0.5.17@ads
  arcsoft_ads_sense_frame/0.20.65@ads
  arcsoft_ads_nav_tts_action_rec/0.1.5@ads
  arcsoft_ads_nav_model/0.4.0@ads 
  arcsoft_ads_prediction/0.4.4@ads
  # demo_utils/0.7.13@ads
)

target_include_directories(ads_planning_navi_model_demo 
  PRIVATE 
  ${CMAKE_CURRENT_SOURCE_DIR}/../sdk/common
  ${CMAKE_CURRENT_SOURCE_DIR}/../sdk/framework
)

target_link_libraries(ads_planning_navi_model_demo
  PRIVATE
    ads::arcsoft_ads_nav_model
    ads::arcsoft_ads_prediction
    ads::arcsoft_ads_nav_tts_action_rec
    arcsoft_ads_planning 
    
    arcpkg::CLI11
    arcpkg::spdlog
    arcpkg::fmt
    arcpkg::ghc-filesystem

    ads::ads_sim 
    ads::ads_h264
    
    ads::arcsoft_ads_base
    ads::arcsoft_ads_rectify
    ads::arcsoft_ads_sense_frame
    arcpkg::Eigen
   
    # ads::demo_utils
    arcpkg::mlog
)

if(NOT CMAKE_CROSSCOMPILING)
  target_compile_definitions(ads_planning_navi_model_demo PRIVATE ENABLE_VIZ=1)
  target_link_libraries(ads_planning_navi_model_demo
    PRIVATE
      arcpkg::imgui
      arcpkg::xgl
      ads::ads_vis 
  )
endif()

arcpkg_vs_debugger_environment(ads_planning_navi_model_demo)