#include <arcsoft_ads_nav_model.h>
#include <arcsoft_ads_nav_tts_action_rec.h>
#include <arcsoft_ads_planning.h>
#include <arcsoft_ads_prediction.h>
#include <arcsoft_ads_sense_frame.h>       // 内部算法结果
#include <arcsoft_ads_sense_frame_base.h>  // OEM算法结果
//#include <sanitizer/lsan_interface.h>      // ASan的泄漏检查API
#include <string.h>

#include <Eigen/Eigen>
#include <clocale>
#include <codecvt>
#include <csignal>  // signal处理
#include <cstdio>   // fprintf (可选，用于自定义消息)
#include <cstdlib>
#include <iostream>

// spdlog
#ifndef SPDLOG_FMT_EXTERNAL
#define SPDLOG_FMT_EXTERNAL
#endif
#include <spdlog/spdlog.h>

#if ENABLE_VIZ
#include <imgui.h>

#include <xgl/ModelMatrix.hpp>
#include <xgl_viz/all.hpp>
namespace viz = xgl::viz;
#endif

#include <arcsoft_ads_geometry.h>
#include <arcsoft_ads_rectify.h>

#include <ads_h264/ads_data_type_str.hpp>
#include <ads_h264/ads_h264.hpp>
// #include <parse_nav_info.hpp>
#define _ARC_USE_MATH_DEFINES
#include <math.h>

#define MLOG_MODULE arcads_planning
#include "mlog.h"

#ifdef _WIN32
#include <windows.h>
#endif

using namespace std;

struct Buffer {
    const uint8_t* data = nullptr;
    size_t size = 0;
    size_t pos = 0;
};

// // 新增：信号处理函数（捕获Ctrl+C，优雅触发ASan报告）
// void asanSignalHandler(int signum) {
//     fprintf(stderr, "Received SIGINT. Forcing ASan leak...\n");
//     __lsan_do_leak_check();  // 手动触发ASan泄漏报告（包括Stats和SUMMARY）
//     exit(1);  // 优雅退出，返回1表示异常（可自定义）
// }

//--------------------------------------------------------------------------------
// convert std::vector<arc_gtcar::NamedBufferHeader> to
// std::vector<demo_utils::NamedBufferHeader>
// static void convert_named_buffers(
//     const ads_h264::H264RawFrame& frame,
//     std::vector<demo_utils::NamedBufferHeader>& named_buffers) {
//     named_buffers.reserve(frame.named_buffers.size());
//     for (const auto& buf : frame.named_buffers) {
//         demo_utils::NamedBufferHeader named_buf;
//         named_buf.name = buf.name;
//         named_buf.size = buf.size;
//         named_buf.data = buf.data;
//         named_buffers.emplace_back(named_buf);
//     }
// }

//--------------------------------------------------------------------------------
size_t VectorBuffer_sgetn(void* userdata, void* s, size_t n) {
    Buffer* buf = (Buffer*)userdata;
    if (buf->pos + n > buf->size) n = buf->size - buf->pos;
    if (n == 0) return 0;
    memcpy(s, buf->data + buf->pos, n);
    buf->pos += n;
    return n;
}
//--------------------------------------------------------------------------------
static void quaternionToEuler(float qx, float qy, float qz, float qw,
                              float& roll, float& pitch, float& yaw) {
    // roll (X-axis rotation)
    float sinr_cosp = 2.f * (qw * qx + qy * qz);
    float cosr_cosp = 1.f - 2.f * (qx * qx + qy * qy);
    roll = std::atan2(sinr_cosp, cosr_cosp);

    // pitch (Y-axis rotation)
    float sinp = 2.f * (qw * qy - qz * qx);
    if (std::fabs(sinp) >= 1.f)
        pitch =
            static_cast<float>(std::copysign(M_PI / 2.f, sinp));  // 90° 或 -90°
    else
        pitch = std::asin(sinp);

    // yaw (Z-axis rotation)
    float siny_cosp = 2.f * (qw * qz + qx * qy);
    float cosy_cosp = 1.f - 2.f * (qy * qy + qz * qz);
    yaw = std::atan2(siny_cosp, cosy_cosp);
}
//--------------------------------------------------------------------------------
void poseTransform(const ArcAdsPose3f& pose, ArcAdsGlobalPose3d& global_pose) {
    global_pose.x = pose.translation.x;
    global_pose.y = pose.translation.y;
    global_pose.z = pose.translation.z;

    quaternionToEuler(pose.rotation.x, pose.rotation.y, pose.rotation.z,
                      pose.rotation.w, global_pose.roll, global_pose.pitch,
                      global_pose.yaw);
}
//--------------------------------------------------------------------------------
#if ENABLE_VIZ
//--------------------------------------------------------------------------------
static void get_colormap(const std::vector<float>& input_data,
                         viz::V_Color& out, float road_maximum_width = 15.f) {
    for (size_t i = 0; i < input_data.size(); i++) {
        // ref: https://www.cnblogs.com/burellow/p/3421106.html
        float color_value = input_data[i] / road_maximum_width;

        if (color_value < 0.f) color_value = 0.f;
        if (color_value > 1.f) color_value = 1.f;

        double dr, dg, db;
        if (color_value < 0.1242) {
            db = 0.504 + ((1. - 0.504) / 0.1242) * color_value;
            dg = dr = 0.;
        } else if (color_value < 0.3747) {
            db = 1.;
            dr = 0.;
            dg = (color_value - 0.1242) * (1. / (0.3747 - 0.1242));
        } else if (color_value < 0.6253) {
            db = (0.6253 - color_value) * (1. / (0.6253 - 0.3747));
            dg = 1.;
            dr = (color_value - 0.3747) * (1. / (0.6253 - 0.3747));
        } else if (color_value < 0.8758) {
            db = 0.;
            dr = 1.;
            dg = (0.8758 - color_value) * (1. / (0.8758 - 0.6253));
        } else {
            db = 0.;
            dg = 0.;
            dr = 1. - (color_value - 0.8758) * ((1. - 0.504) / (1. - 0.8758));
        }

        xgl::Color color;
        color.r = static_cast<uint8_t>(255 * dr);
        color.g = static_cast<uint8_t>(255 * dg);
        color.b = static_cast<uint8_t>(255 * db);
        color.a = 255U;

        out.emplace_back(color);
    }
}
//--------------------------------------------------------------------------------
template <typename ParentType>
void DrawRefLineColor(ParentType* pkNode, const ArcAdsNavReferenceLine& rkLine,
                      const viz::V_Vector3f& rkPts,
                      float ArcAdsNavReferenceLinePoint::*ptMember,
                      float fMaxDistance) {
    std::vector<float> kDistances(rkLine.num_points);
    for (unsigned int i = 0; i < rkLine.num_points; i++) {
        kDistances[i] = rkLine.points[i].*ptMember;
    }

    viz::V_Color kColors;
    kColors.reserve(rkLine.num_points);
    get_colormap(kDistances, kColors, fMaxDistance);

    pkNode->add(fmt::format("{:2d}", rkLine.id),
                viz::DBT<viz::Lines>(viz::Lines::LINE_STRIP, rkPts, kColors)
                    .lineWidth(3)
                    .build());
}
//--------------------------------------------------------------------------------

float width = 1.9f;
const viz::Vector3f up = {0.f, 0.f, 1.f};
float halfWidth = width * 0.5f;

//
void DrawMesh(const viz::V_Vector3f& points, int uiNumPoints,
              viz::V_Vector3f& vertices, viz::V_Vector3i& indices) {
    for (int i = 0; i < uiNumPoints; ++i) {
        viz::Vector3f tangent;
        if (i == 0)
            tangent = (points[i + 1] - points[i]).normalize();
        else if (i == uiNumPoints - 1)
            tangent = (points[i] - points[i - 1]).normalize();
        else {
            xgl::Vector3f forward = (points[i + 1] - points[i]).normalize();
            xgl::Vector3f back = (points[i] - points[i - 1]).normalize();
            tangent = (forward + back).normalize();
            // tangent = forward;
        }
        xgl::Vector3f normal = xgl::cross(tangent, up);
        if (normal.magnitude() < 1e-6f)
            normal = xgl::cross(tangent, {1.f, 0.f, 0.f});
        xgl::Vector3f offset = normal * halfWidth;
        xgl::Vector3f left = points[i] + offset;
        xgl::Vector3f right = points[i] - offset;
        vertices.push_back(left);
        vertices.push_back(right);
    }
    for (int i = 0; i < uiNumPoints - 1; ++i) {
        int i0 = i * 2;
        int i1 = i0 + 1;
        int i2 = i0 + 2;
        int i3 = i0 + 3;

        // First triangle
        indices.push_back({i0, i2, i1});

        // Second triangle
        indices.push_back({i2, i3, i1});
    }
}

//-----------------------------------------------------------------------
template <typename ParentType>
viz::CollectionNamed* DrawPlanningPath(ParentType* pkParent, const char* acName,
                                       const ArcAdsPlanningPathPoint* pps,
                                       int uiNumPoints) {
    viz::CollectionNamed* pkNode = new viz::CollectionNamed();
    pkParent->add(acName, pkNode);

    viz::V_Vector3f points;
    points.reserve(uiNumPoints);
    for (int j = 0; j < uiNumPoints; j++) {
        points.emplace_back(pps[j].x, pps[j].y, 0.f);
    }

    viz::V_Vector3f vertices;
    viz::V_Vector3i indices;
    DrawMesh(points, uiNumPoints, vertices, indices);

    viz::Color path_color = viz::Color(255, 255, 255, 200);
    pkNode->add("path_mesh",
                viz::DBT<viz::Mesh>(std::move(vertices), std::move(indices))
                    .color(path_color)
                    .blendFunc(XGL_STATE_BLEND_ALPHA)
                    .build());

    return pkNode;
}

template <typename ParentType>
viz::CollectionNamed* DrawPlanningTrajectory(
    ParentType* pkParent, const char* acName,
    const ArcAdsPlanningTrajectoryPoint* pps, int uiNumPoints,
    const viz::Color& rkColor) {
    viz::CollectionNamed* pkNode = new viz::CollectionNamed();
    pkParent->add(acName, pkNode);

    viz::V_Vector3f points;
    points.reserve(uiNumPoints);
    for (int j = 0; j < uiNumPoints; j++) {
        points.emplace_back(pps[j].path_point.x, pps[j].path_point.y, 0.f);
    }

    viz::V_Vector3f vertices;
    viz::V_Vector3i indices;
    DrawMesh(points, uiNumPoints, vertices, indices);

    viz::Color color = viz::Color(200, 160, 255, 125);
    pkNode->add("trajectory_mesh",
                viz::DBT<viz::Mesh>(std::move(vertices), std::move(indices))
                    .color(color)
                    .blendFunc(XGL_STATE_BLEND_ALPHA)
                    .build());

    return pkNode;
}

//-----------------------------------------------------------------------
template <typename ParentType, typename MeasurementType>
viz::CollectionNamed* DrawLines(ParentType* pkParent, const char* acName,
                                const MeasurementType* akLines,
                                unsigned int uiNumLines,
                                const viz::Color& rkColor) {
    viz::CollectionNamed* pkNode = new viz::CollectionNamed();
    pkParent->add(acName, pkNode);
    for (uint32_t i = 0; i < uiNumLines; ++i) {
        const MeasurementType& rkLine = akLines[i];

        viz::V_Vector3f points;
        points.reserve(rkLine.num_pts);
        for (uint32_t j = 0; j < rkLine.num_pts; j++) {
            points.emplace_back(rkLine.pts[j].x, rkLine.pts[j].y, 0.f);
        }
        const viz::V_Color colors(points.size(), rkColor);
        pkNode->add(fmt::format("{:2d}", rkLine.id),
                    viz::DBT<viz::Lines>(viz::Lines::LINE_STRIP, points, colors)
                        .build());
    }
    return pkNode;
}
//--------------------------------------------------------------------------------

//--------------------------------------------------------------------------------
template <typename NodeType>
void DrawReferenceLines(NodeType* pkParent, const char* acName,
                        const ArcAdsNavReferenceLines& rkRefLines) {
    viz::CollectionNamed* viz_reference_line = new viz::CollectionNamed();
    viz::CollectionNamed* pkRefLineDistanceVis = new viz::CollectionNamed();
    viz::CollectionNamed* viz_distance2leftdivider = new viz::CollectionNamed();
    viz::CollectionNamed* viz_distance2rightdivider =
        new viz::CollectionNamed();
    viz::CollectionNamed* viz_distance2leftBoundary =
        new viz::CollectionNamed();
    viz::CollectionNamed* viz_distance2rightBoundary =
        new viz::CollectionNamed();

    pkParent->add("Reference_line", viz_reference_line);
    // pkParent->add("Ref dist vis", pkRefLineDistanceVis);
    pkRefLineDistanceVis->add("Left divider", viz_distance2leftdivider);
    pkRefLineDistanceVis->add("Right divider", viz_distance2rightdivider);
    pkRefLineDistanceVis->add("Left boundary", viz_distance2leftBoundary);
    pkRefLineDistanceVis->add("Right boundary", viz_distance2rightBoundary);

    for (uint32_t i = 0; i < rkRefLines.num_lines; ++i) {
        const ArcAdsNavReferenceLine& line = rkRefLines.lines[i];

        viz::V_Vector3f points;
        points.reserve(line.num_points);

        // reference-line points (x,y)
        for (uint32_t j = 0; j < line.num_points; j++) {
            points.emplace_back(line.points[j].x, line.points[j].y, 0.f);
        }
        if (line.is_highlight)  // Set Hight to green
        {
            const viz::V_Color colors(points.size(), viz::Color::green());
            viz_reference_line->add(
                fmt::format("{:2d}", line.id),
                viz::DBT<viz::Lines>(viz::Lines::LINE_STRIP, points, colors)
                    .lineWidth(50)
                    .build());
        } else {
            const viz::V_Color colors(points.size(), viz::Color::white());
            viz_reference_line->add(
                fmt::format("{:2d}", line.id),
                viz::DBT<viz::Lines>(viz::Lines::LINE_STRIP, points, colors)
                    .lineWidth(50)
                    .build());
        }

        // reference-line distance_to_left_divider
        DrawRefLineColor(viz_distance2leftdivider, line, points,
                         &ArcAdsNavReferenceLinePoint::distance_to_left_divider,
                         15.f);
        DrawRefLineColor(
            viz_distance2rightdivider, line, points,
            &ArcAdsNavReferenceLinePoint::distance_to_right_divider, 15.f);
        DrawRefLineColor(
            viz_distance2leftBoundary, line, points,
            &ArcAdsNavReferenceLinePoint::distance_to_left_boundary, 30.f);
        DrawRefLineColor(
            viz_distance2rightBoundary, line, points,
            &ArcAdsNavReferenceLinePoint::distance_to_right_boundary, 30.f);
    }
}
//--------------------------------------------------------------------------------
#endif
//--------------------------------------------------------------------------------
template <typename Derived>
class StringConverter {
protected:
    std::string ansi_str;  // Input ANSI string (system locale)

public:
    explicit StringConverter(const std::string& ansi) : ansi_str(ansi) {}

    // Templated conversion function
    auto convert() const {
        return static_cast<const Derived*>(this)->convert_impl();
    }

    virtual ~StringConverter() = default;
};
class ToUtf8Converter : public StringConverter<ToUtf8Converter> {
public:
    using StringConverter::StringConverter;  // Inherit constructor

    std::string convert_impl() const {
        if (ansi_str.empty()) return std::string();

        // For UTF-8, we can return the input directly if it's already UTF-8
        // compatible Otherwise, we need to handle system locale, but
        // codecvt_utf8_utf16 is used for consistency
        std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> converter;

        try {
            // Convert ANSI to UTF-16, then back to UTF-8
            std::wstring utf16_str = converter.from_bytes(ansi_str);
            std::string utf8_str = converter.to_bytes(utf16_str);
            return utf8_str;
        } catch (const std::range_error& e) {
            throw std::runtime_error("Conversion to UTF-8 failed: " +
                                     std::string(e.what()));
        }
    }
};

//--------------------------------------------------------------------------------
int main() {
    // 新增：注册SIGINT处理（Ctrl+C时调用handler）
    // signal(SIGINT, asanSignalHandler);
    spdlog::set_pattern("[%^%L%$] [%s:%#] %v");

    // set run-time mlog level
    // mlog_set_level(MLOG_DEBUG);
    arcads_planning_mlog_set_level(MLOG_DEBUG);

    string h264_path =
        "../data/test_case/VisInsight_20250729142602_87107_87747.h264";
    string adas_camera_calib_path = "../data/test_case/CALIB-CAM_0319.json";
    string vehicle_calib_path = "../data/test_case/calibration_carinfo.json";

    ads_h264::H264Clip::Ptr clip = ads_h264::load_h264_clip(
        h264_path.c_str(), adas_camera_calib_path.c_str(),
        vehicle_calib_path.c_str(), ads_h264::PixelFormat::NV12);
    const auto& vehicle_calib = clip->get_vehicle_calib();
    const auto ads_cam_calibs = clip->get_camera_calibs();

    ArcAdsSenseFrame* sense_frame = nullptr;
    ArcAdsSenseFrame_create(nullptr, &sense_frame);

    ArcAdsNavModel* nav_model_engine = nullptr;
    ArcAdsNavModel_create(&nav_model_engine);

    ArcAdsNavTTSActionRec* nav_tts_action_engine = nullptr;
    ArcAdsNavTTSActionRec_create(&nav_tts_action_engine);

    // ArcAdsNavLaneGuideRec* pkLaneGuideRecEngine = nullptr;
    // ArcAdsNavLaneGuideRec_create(&pkLaneGuideRecEngine);

    const auto veh_calib = clip->get_vehicle_calib();

    ArcAdsPredictionEngine* pred_engine = nullptr;
    ArcAdsPredictionEngine_create(&pred_engine, &veh_calib);

    ArcAdsPlanning* planning_engine = nullptr;
    ArcAdsPlanning_create(&planning_engine, &veh_calib);

    ArcAdsNavReferenceLines kReferenceLines;

    ArcAdsMultiModalPrediction* multi_modal_prediction =
        new ArcAdsMultiModalPrediction();

    ArcAdsPlanningTrajectory* out_trajectory = new ArcAdsPlanningTrajectory();

#if ENABLE_VIZ
    // Find the placement order.
    constexpr int NVIEWS = 6;
    constexpr ArcAdsCameraPlacement placement_orders[NVIEWS] = {
        ARCADS_CAMERA_PLACEMENT_AROUND_VIEW_LEFT,
        ARCADS_CAMERA_PLACEMENT_FRONT_WIDE,
        ARCADS_CAMERA_PLACEMENT_AROUND_VIEW_RIGHT,
        ARCADS_CAMERA_PLACEMENT_AROUND_VIEW_FRONT,
        ARCADS_CAMERA_PLACEMENT_REAR_WIDE,
        ARCADS_CAMERA_PLACEMENT_AROUND_VIEW_REAR,
    };
    const int rectified_width = 640;
    const int rectified_height = 320;
    std::vector<ArcAdsCameraCalib> ads_rectified_cam_calibs(NVIEWS);
    for (size_t i = 0; i < ads_rectified_cam_calibs.size(); ++i) {
        auto& c = ads_rectified_cam_calibs[i];
        c.rate_hz = 10;
        c.placement = placement_orders[i];
        c.T_ego_camera.rotation = {0, 0, 0, 1};
        c.T_ego_camera.translation = {0, 0, 0};
        c.intrinsic.model = ARCADS_CAMERA_MODEL_PINHOLE;
        c.intrinsic.width = rectified_width;
        c.intrinsic.height = rectified_height;
        c.intrinsic.fx = c.intrinsic.fy = 480 * 0.5f;
        c.intrinsic.cx = rectified_width * 0.5f;
        c.intrinsic.cy = rectified_height * 0.5f;
    }
    const int mosaic_width = rectified_width * 3;
    const int mosaic_height = rectified_height * 2;
    std::vector<uint8_t> mosaic(mosaic_width * mosaic_height * 3);

    ArcAdsRectifyFrame* rectify_frame = NULL;
    ArcAdsRectifyFrame_create(
        &rectify_frame, ads_cam_calibs.size(), ads_rectified_cam_calibs.size(),
        ads_cam_calibs.data(), ads_rectified_cam_calibs.data(), NULL,
        1);  // (int) ads_rectified_cam_calibs.size());
#endif

#if ENABLE_VIZ
    auto win = viz::namedWindow("BEV", viz::WindowMode::WM_2D);
    win->setViewMatrix(xgl::ModelMatrixf::rotationZ(xgl::deg2rad(-90.f)) *
                       xgl::ModelMatrixf::rotationX(xgl::deg2rad(180.f)));
    // win->add("grid", new viz::Grid({16, 10}, {10.f, 10.f}));
    win->add("ego", viz::DBT<viz::Cube>(viz::Cube::MONO)
                        .scale(vehicle_calib.length, vehicle_calib.width,
                               vehicle_calib.height)
                        .color(viz::Color::green())
                        .build());

    auto scene = new viz::Scene();

    auto win2 = viz::namedWindow("PV1", viz::WindowMode::WM_2D);
    win2->setProjectionMatrix2D(rectified_width * 3, rectified_height * 2);
    std::vector<viz::Viewport*> vps(NVIEWS);
    for (size_t i = 0; i < NVIEWS; ++i) {
        const auto& cc = ads_rectified_cam_calibs[i];
        const auto& c = cc.intrinsic;
        vps[i] = win2->namedViewport(
            ads_h264::get_camera_name_str((ads_h264::CameraName)cc.placement),
            viz::RenderMode::RM_AR);
        float T_BS[4][4];
        ArcAdsPose3f_getMatrix(&cc.T_ego_camera, T_BS[0]);
        const xgl::Matrix4f T_BS_mat(
            T_BS[0][0], T_BS[0][1], T_BS[0][2], T_BS[0][3], T_BS[1][0],
            T_BS[1][1], T_BS[1][2], T_BS[1][3], T_BS[2][0], T_BS[2][1],
            T_BS[2][2], T_BS[2][3], T_BS[3][0], T_BS[3][1], T_BS[3][2],
            T_BS[3][3]);
        const int rows = (int)i / 3;
        const int cols = (int)i % 3;
        vps[i]->setZprFixed(true);
        vps[i]->setSharedScene(scene);
        vps[i]->setViewMatrix(T_BS_mat.inv());
        vps[i]->setProjectionMatrixAR(c.width, c.height, c.fx, c.fy, c.cx,
                                      c.cy);
        vps[i]->setViewportPosition(
            (float)c.width * cols, (float)c.height * rows, (float)c.width,
            (float)c.height, viz::ViewportCoordinate::WORLD_SPACE);
        vps[i]->setBorderSize(2);
        vps[i]->setBorderColor(viz::Color::white());
    }

    win->setSharedScene(scene);
#endif

    // Play
    bool playing = true;
    bool one_frame_forward = true;
    uint32_t start_frame_id = 0;
    const int num_frames = clip->get_num_frames();
    (void)playing;
    if (start_frame_id > 0) {
        SPDLOG_INFO("Skip {} frames.", start_frame_id);
        clip->seek(start_frame_id);
    }
    int required_next_frame_idx = start_frame_id;

#if ENABLE_VIZ
    auto player_slider_func = [=, &playing, &required_next_frame_idx,
                               &one_frame_forward] {
        if (ImGui::Button("<<")) {
            required_next_frame_idx = std::max(required_next_frame_idx - 1, 0);
            one_frame_forward = true;
            playing = false;
        }
        ImGui::SameLine();
        if (ImGui::Button(playing ? "||" : ">")) {
            playing = !playing;
        }
        ImGui::SameLine();
        if (ImGui::Button(">>")) {
            required_next_frame_idx =
                std::min(required_next_frame_idx + 1, num_frames - 1);
            one_frame_forward = true;
            playing = false;
        }
        ImGui::SameLine();

        char title[64];
        sprintf(title, "%d/%d##FrameIdx", required_next_frame_idx,
                num_frames - 1);
        ImGui::PushItemWidth(-80);
        if (ImGui::SliderInt(title, &required_next_frame_idx, 0,
                             num_frames - 1)) {
            one_frame_forward = true;
            playing = false;
        }
    };
    win->addWidget("PlayerSilder", player_slider_func);
#endif

    ads_h264::H264RawFrame h264_raw_frame;
    ads_h264::ImageView h264_vframe;
    ads_h264::CameraFrame h264_cam_frame;
    ads_h264::BufferHeader h264_algo_buf;
    ads_h264::V_AdsVehicleCanSignal h264_can_signals;
    ArcAdsCameraFrame camera_frame = {};
    while (true) {
#if ENABLE_VIZ
        // Skip or Process Frame Decision
        if (!playing && !one_frame_forward) {
            viz::waitKey(100);
            continue;
        }
#endif
        // Frame Seek
        int next_frame_idx = clip->get_next_frame_idx();
        if (required_next_frame_idx != next_frame_idx) {
            clip->seek(required_next_frame_idx);
            next_frame_idx = required_next_frame_idx;
        }

        if (!clip->read(h264_raw_frame, &h264_vframe)) {
            SPDLOG_INFO("frame: {}", next_frame_idx);
            continue;
        }

        clip->parse_camera_frame(h264_raw_frame, h264_vframe, h264_cam_frame);

        // Auto-Advance
        if (playing) {
            if (required_next_frame_idx == num_frames - 1)
                playing = false;
            else
                required_next_frame_idx =
                    std::min(required_next_frame_idx + 1, num_frames - 1);
        }
        one_frame_forward = false;

        // [0812 comment] // 读取导航信息
        // std::vector<demo_utils::NavInfo> m_kNavInfos;
        // std::vector<demo_utils::NamedBufferHeader> kNamedBuffers;
        // convert_named_buffers(h264_raw_frame, kNamedBuffers);
        // demo_utils::parseNavInfo(kNamedBuffers, m_kNavInfos);

        // if (m_kNavInfos.size() > 0) {
        //     for (size_t i = 0; i < m_kNavInfos.size(); i++) {
        //         if (m_kNavInfos[i].has_route_info) {
        //             ArcAdsNavModel_processNavRouteInfo(
        //                 nav_model_engine, &m_kNavInfos[i].route_info);
        //         }
        //         if (m_kNavInfos[i].has_nav_data) {
        //             ArcAdsNavModel_processNavDataInfo(
        //                 nav_model_engine, &m_kNavInfos[i].nav_data_info);
        //         }
        //         if (m_kNavInfos[i].has_tts_msg &&
        //             m_kNavInfos[i].tts_msg.gmt_time > 0) {
        //             ArcAdsNavTTSActionInfo kTTSInfo;
        //             ArcAdsNavTTSActionRec_process(nav_tts_action_engine,
        //                                           &m_kNavInfos[i].tts_msg,
        //                                           &kTTSInfo);
        //             ArcAdsNavModel_processNavTTSActionInfo(nav_model_engine,
        //                                                    &kTTSInfo);
        //         }
        //     }
        // }

        // deserializeSenseFrame
        bool found_algo_buffer = true;
        found_algo_buffer =
            clip->parse_algo_buffer(h264_raw_frame, h264_algo_buf);
        if (found_algo_buffer) {
            if (h264_algo_buf.data == nullptr) {
                SPDLOG_WARN("No algodebug buffer exist");
            }

            Buffer buf;
            buf.data = h264_algo_buf.data;
            buf.size = h264_algo_buf.size;
            buf.pos = 0;
            ArcAdsSenseFrame_deserialize(sense_frame, VectorBuffer_sgetn, &buf);
        }

        const ArcAdsTime time = ArcAdsSenseFrame_getTime(sense_frame);
        const uint32_t frame_id = ArcAdsSenseFrame_getFrameId(sense_frame);
        SPDLOG_INFO("frame-{}: time={}, frame_id={}", required_next_frame_idx,
                    time, frame_id);

        const ArcAdsEgoMotionMeasurement* ego_motion =
            ArcAdsSenseFrame_getEgoMotionMeasurement(sense_frame);
        const ArcAdsObjectMeasurements* objects =
            ArcAdsSenseFrame_getObjectMeasurements(sense_frame);
        const ArcAdsLocalMapMeasurementV2* local_map =
            ArcAdsSenseFrame_getLocalMapMeasurement(sense_frame);
        const ArcAdsTrafficLightLogicalAssembly*
            traffic_light_logical_assembly =
                ArcAdsSenseFrame_getTrafficLightLogicalAssembly(sense_frame);
        const ArcAdsSpeedLimits* speed_limits =
            ArcAdsSenseFrame_getSpeedLimits(sense_frame);

        const ArcAdsObjectMeasurements* object_measurements =
            ArcAdsSenseFrame_getObjectMeasurements(sense_frame);

        // Process vehicle can signals.
        if (clip->parse_can_signals(h264_raw_frame, h264_can_signals)) {
            for (const auto& signal : h264_can_signals) {
                const auto ret = ArcAdsPlanning_processVehicleCanSignal(
                    planning_engine, &signal);
                assert(ret == ARCADS_OK &&
                       "ArcAdsPlanning_processVehicleCanSignal() failed.");
                SPDLOG_INFO("signal.light_flags:{}", signal.light_flags);
            }
        }

        // 1. 运行navi_model
        ArcAdsNavModel_process(nav_model_engine, &kReferenceLines, ego_motion,
                               local_map, nullptr);

        // 2. 运行预测
        //  steering, reference_lines 暂时设置为nullptr
        ArcAdsPredictionEngine_process(
            pred_engine, multi_modal_prediction, ego_motion, objects, local_map,
            traffic_light_logical_assembly, speed_limits, nullptr, nullptr);

        // 3. 运行规划
        ArcAdsPlanning_process(planning_engine, out_trajectory, ego_motion,
                               object_measurements, local_map,
                               traffic_light_logical_assembly, speed_limits,
                               &kReferenceLines, multi_modal_prediction);

#if ENABLE_VIZ
        // INFO LEFT_TOP
        win->add("frame_id",
                 viz::DBT<viz::TextOverlay>(
                     fmt::format("frame-{}: time={}, frame_id={}",
                                 required_next_frame_idx, time, frame_id))
                     .translate(10, 10)
                     .build());
        // OBJECTS
        {
            viz::CollectionNamed* viz_objects = new viz::CollectionNamed();
            for (uint32_t i = 0; i < object_measurements->num_objects; ++i) {
                const ArcAdsObjectMeasurement& obj =
                    object_measurements->objects[i];
                Eigen::Vector3f kPos(obj.longitudinal_distance,
                                     obj.lateral_distance, 1);
                Eigen::Vector3f kDir(cosf(obj.yaw), sinf(obj.yaw), 0);
                kPos += obj.length / 2 * kDir;

                viz_objects->add(
                    fmt::format("{:2d}", obj.id),
                    viz::DBT<viz::Cube>(viz::Cube::MONO)
                        .scale(obj.length, obj.width, obj.height)
                        .rotate(xgl::ModelMatrixf::rotationZ(obj.yaw)
                                    .topLeftCorner<3, 3>())
                        .translation(kPos[0], kPos[1],
                                     obj.altitude_distance + obj.height / 2)
                        .color(viz::Color::red())
                        .lineWidth(2)
                        .polygonMode(XGL_STATE_POLYGON_LINE)
                        .build());
            }
            scene->add("objects", viz_objects);
        }
        // LOCAL MAP
        {
            const ArcAdsLocalMapMeasurementV2* local_maps =
                ArcAdsSenseFrame_getLocalMapMeasurement(sense_frame);

            // lanes -> 车道中心线点链
            viz::CollectionNamed* viz_center_lanes = new viz::CollectionNamed();
            viz::CollectionNamed* viz_center_lanes_ids =
                new viz::CollectionNamed();
            for (uint32_t i = 0; i < local_maps->num_lanes; ++i) {
                const ArcAdsLaneMeasurementV2& lane = local_maps->lanes[i];

                viz::V_Vector3f points;
                points.reserve(lane.num_pts);
                // for (const auto& pt : lane.pts)
                for (uint32_t j = 0; j < lane.num_pts; j++) {
                    points.emplace_back(lane.pts[j].x, lane.pts[j].y, 0.f);
                }
                const viz::Vector3f& mid_pt =
                    points[static_cast<int>(lane.num_pts / 2)];
                const viz::V_Color colors(points.size(), viz::Color::blue());
                viz_center_lanes->add(
                    fmt::format("id_{:2d}", lane.id),
                    viz::DBT<viz::Lines>(viz::Lines::LINE_STRIP, points, colors)
                        .build());
                viz_center_lanes_ids->add(
                    fmt::format("id_{:2d}_label", lane.id),
                    viz::DBT<viz::Text3D>(fmt::format("{:2d}", lane.id))
                        .scale(1.f)
                        .rotateZ(-90)
                        .translation(mid_pt.x, mid_pt.y, 0.f)
                        .color(viz::Color::white())
                        .build());
            }
            scene->add("center_lanes", viz_center_lanes);
            viz_center_lanes->add("center_lanes_id", viz_center_lanes_ids);

            // lane_dividers -> 边界线点
            DrawLines(scene, "lane_dividers", local_maps->lane_dividers,
                      local_maps->num_lane_dividers, viz::Color::orange());
            DrawLines(scene, "road_boundaries", local_maps->road_boundaries,
                      local_maps->num_road_boundaries,
                      viz::Color::orange_red());
        }
        // REFERENCE LINE
        DrawReferenceLines(scene, "Reference lines", kReferenceLines);
        // Prediction
        {
            // Ego
            viz::CollectionNamed* viz_pred_ego = new viz::CollectionNamed();
            for (uint32_t modal_num = 0;
                 modal_num < multi_modal_prediction->num_predictions;
                 ++modal_num) {
                ArcAdsEgoPrediction ego_pred =
                    multi_modal_prediction->predictions[modal_num]
                        .ego_prediction;

                viz::V_Vector3f points;
                points.reserve(ego_pred.trajectory.num_points);
                for (uint32_t i = 0; i < ego_pred.trajectory.num_points; ++i) {
                    const ArcAdsPredictionTrajectoryPoint& ego_pred_pt =
                        ego_pred.trajectory.points[i];
                    points.emplace_back(ego_pred_pt.x, ego_pred_pt.y, 0.f);
                }
                const viz::V_Color colors(points.size(), viz::Color::green());
                viz_pred_ego->add(
                    fmt::format("{:2d}", modal_num),
                    viz::DBT<viz::Lines>(viz::Lines::LINE_STRIP, points, colors)
                        .color(viz::Color::green())
                        .build());
            }
            // scene->add("pred_ego", viz_pred_ego);

            // Other Agents
            viz::CollectionNamed* viz_pred_agents = new viz::CollectionNamed();
            for (uint32_t modal_num = 0;
                 modal_num < multi_modal_prediction->num_predictions;
                 ++modal_num) {
                const ArcAdsPrediction& pred =
                    multi_modal_prediction->predictions[modal_num];
                for (uint32_t i = 0; i < pred.num_objects; ++i) {
                    const ArcAdsObjectPrediction& obj_pred = pred.objects[i];

                    viz::V_Vector3f points;
                    points.reserve(obj_pred.trajectory.num_points);
                    for (uint32_t j = 0; j < obj_pred.trajectory.num_points;
                         ++j) {
                        const ArcAdsPredictionTrajectoryPoint& obj_pred_pt =
                            obj_pred.trajectory.points[j];
                        points.emplace_back(obj_pred_pt.x, obj_pred_pt.y, 0.f);
                    }
                    const viz::V_Color colors(points.size(),
                                              viz::Color::purple());
                    viz_pred_agents->add(
                        fmt::format("{:2d}", obj_pred.id),
                        viz::DBT<viz::Lines>(viz::Lines::LINE_STRIP, points,
                                             colors)
                            .color(viz::Color::purple())
                            .build());
                }
            }
            scene->add("pred_agents", viz_pred_agents);
        }

        // Planning Path
        DrawPlanningPath(scene, "Planning Path", out_trajectory->path_points,
                         static_cast<int>(out_trajectory->num_path_points));

        DrawPlanningTrajectory(
            scene, "Planning Trajectory", out_trajectory->trajectory_points,
            static_cast<int>(out_trajectory->num_trajectory_points),
            viz::Color::yellow());

        // CAMERA
        camera_frame.frame_id = h264_cam_frame.frame_id;
        camera_frame.num_images = (uint32_t)h264_cam_frame.images.size();
        for (size_t i = 0; i < h264_cam_frame.images.size(); ++i) {
            camera_frame.images[i].placement = ads_cam_calibs[i].placement;
            camera_frame.images[i].time = h264_cam_frame.images[i].trigger_time;
            ArcAdsImage_release(camera_frame.images[i].image);
            ArcAdsImage_createFromData(&camera_frame.images[i].image,
                                       ARCADS_IMAGE_FORMAT_NV12,
                                       h264_cam_frame.images[i].image.width,
                                       h264_cam_frame.images[i].image.height,
                                       h264_cam_frame.images[i].image.planes,
                                       h264_cam_frame.images[i].image.steps);
        }
        ArcAdsRectifyFrame_processRaw(rectify_frame, &camera_frame,
                                      mosaic.data(), rectified_width * 3,
                                      ARCADS_IMAGE_FORMAT_BGR);

        // Viz PV in win2
        for (size_t i = 0; i < NVIEWS; ++i) {
            const auto& c = ads_rectified_cam_calibs[i];
            auto vp = vps[i];
            vp->setBackgroundImage(new viz::Image2D(viz::ImageTexture{
                viz::ImageFormat::BGR,
                rectified_width,
                rectified_height,
                mosaic.data() + rectified_width * rectified_height * 3 * i,
                rectified_width * 3,
            }));
        }

        viz::spin();

        if (viz::shouldQuit()) break;
#endif  // ENABLE_VIZ
    }

    // ArcAdsNavLaneGuideRec_destroy(pkLaneGuideRecEngine);

    // Clean up
    ArcAdsSenseFrame_destroy(sense_frame);
    ArcAdsNavTTSActionRec_destroy(nav_tts_action_engine);
    ArcAdsNavModel_destroy(nav_model_engine);
    ArcAdsPredictionEngine_destroy(pred_engine);
    ArcAdsPlanning_destroy(planning_engine);
    delete out_trajectory;
    // __lsan_do_leak_check();  //
    // 打印ASan报告（即使无泄漏，也输出Stats/SUMMARY）
    return 0;
}
//--------------------------------------------------------------------------------