# linux-x64
docker run -v $(pwd):/mnt -it --rm -e ARTIFACTORY_USER=$ARTIFACTORY_USER -e ARTIFACTORY_PSWD=$ARTIFACTORY_PSWD artifactory.arcsoft.com.cn:6555/lny1856/ubuntu-mesa-gtk2-ffmpeg:focal /bin/bash
cd /mnt
python ci_build.py -p linux -a x64 -b build2

# qnx-aarch64
docker run -v $(pwd):/mnt -it --rm -e ARTIFACTORY_USER=$ARTIFACTORY_USER -e ARTIFACTORY_PSWD=$ARTIFACTORY_PSWD artifactory.arcsoft.com.cn:6555/lny1856/qnx-sdp:710 /bin/bash

