@echo off

set ARCBUILD_ROOT=D:\soft\toolchains\qnx710
call %ARCBUILD_ROOT%\qnxsdp-env.bat

set BUILD_DIR=build-qnx710
cmake -P cmake/arcbuild.cmake -p qnx710 -S . -B %BUILD_DIR% -G Ninja -DCMAKE_BUILD_TYPE=Release

@REM with symbols
@REM cmake --build %BUILD_DIR%

@REM no symbols
@REM cmake -P cmake/arcpkg.cmake pack %BUILD_DIR% > pack.log 2>&1
cmake -P cmake/arcpkg.cmake pack %BUILD_DIR%

@REM pack specified target
@REM cmake --build build-qnx710 --target ads-ads_h264_demo-pack

@REM cmake --build build-qnx710 --target ads-ads_h264_demo-pack > pack.log 2>&1

@REM Packing as arcsdk, required by test-build-qnx710.cmd
cmake --build %BUILD_DIR% --target arcsoft_ads_sdk