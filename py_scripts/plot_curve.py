from bokeh.plotting import figure, output_file, save
from bokeh.models import HoverTool
from bokeh.palettes import Category10
import json

# 加载JSON数据
with open('../data/data.json', 'r') as f:
    data = json.load(f)

# 提取数据
time = [i * 0.2 for i in range(len(data['st_samples_s']))]  # 时间轴 delta_t=0.2
s = data['st_samples_s']  # 位移
v = data['st_samples_v']  # 速度
a = data['st_samples_a']  # 加速度

# 配置输出HTML文件
output_file("../data/motion_plot.html", title="运动状态分析")

# 创建图形
p = figure(
    title="运动状态分析 (位移/速度/加速度)",
    x_axis_label="时间 (s)",
    y_axis_label="数值",
    width=1000,
    height=600,
    tools="pan,box_zoom,wheel_zoom,reset,save"
)


# 绘制三条曲线（使用不同颜色）
colors = Category10[3]
p.line(time, s, legend_label="位移 (s)", line_width=2, color=colors[0], name='s')
p.line(time, v, legend_label="速度 (v)", line_width=2, color=colors[1], name='v')
p.line(time, a, legend_label="加速度 (a)", line_width=2, color=colors[2], name='a')

# 添加数据点
p.circle(time, s, size=4, color=colors[0], alpha=0.5)
p.circle(time, v, size=4, color=colors[1], alpha=0.5)
p.circle(time, a, size=4, color=colors[2], alpha=0.5)

# 调整图例位置和样式
p.legend.location = "top_left"
p.legend.click_policy = "hide"  # 点击图例可隐藏/显示曲线

# 保存HTML文件
save(p)
print("图表已保存为 motion_plot.html")