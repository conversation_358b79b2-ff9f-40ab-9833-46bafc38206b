import json
import re
import warnings
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.animation import FuncAnimation
import os

# 全局设置：调整线条样式
plt.rcParams['lines.linewidth'] = 1.5
plt.rcParams['lines.markersize'] = 4

# 重试配置
RETRY_DELAY = 1  # 1秒后重试
MAX_RETRIES = None  # 最大重试次数，None表示无限重试

def check_json_file(file_path):
    """检查JSON文件是否为空和基本有效性"""
    is_empty = True
    is_valid = False
    
    try:
        with open(file_path, 'r') as f:
            pass
    except FileNotFoundError:
        print(f'文件不存在: {file_path}')
        return is_empty, is_valid
    
    if os.path.getsize(file_path) == 0:
        print(f'文件为空: {file_path}')
        return is_empty, is_valid
    
    with open(file_path, 'r') as f:
        content = f.read(100)
    
    content_trimmed = content.strip()
    if not content_trimmed:
        print(f'文件只包含空白字符: {file_path}')
        return is_empty, is_valid
    
    is_empty = False
    
    if len(content_trimmed) > 0 and (content_trimmed[0] in ['{', '[']):
        is_valid = True
    else:
        print(f'JSON格式可能无效（不以 {{ 或 [ 开头）: {file_path}')
    
    return is_empty, is_valid

def parse_json_points(json_data):
    """解析JSON数据中的lower_points_i和upper_points_i"""
    lower_points_total = []
    upper_points_total = []
    
    lower_pattern = re.compile(r'lower_points_(\d+)')
    upper_pattern = re.compile(r'upper_points_(\d+)')
    
    max_i = -1
    for key in json_data.keys():
        match = lower_pattern.match(key)
        if match:
            index = int(match.group(1))
            if index > max_i:
                max_i = index
        match = upper_pattern.match(key)
        if match:
            index = int(match.group(1))
            if index > max_i:
                max_i = index
    
    for i in range(max_i + 1):
        lower_field = f'lower_points_{i}'
        upper_field = f'upper_points_{i}'
        
        if lower_field in json_data:
            lower_points_total.append(json_data[lower_field])
        else:
            warnings.warn(f'JSON中缺少字段: {lower_field}')
            lower_points_total.append([])
        
        if upper_field in json_data:
            upper_points_total.append(json_data[upper_field])
        else:
            warnings.warn(f'JSON中缺少字段: {upper_field}')
            upper_points_total.append([])
    
    print(f'成功解析 {max_i + 1} 组点障碍物数据')
    return lower_points_total, upper_points_total, max_i  # 返回最大索引用于ID匹配

def convert_cell_to_struct_array(obstacle_id_sets):
    """改进的障碍物ID解析，确保与点数据正确匹配"""
    obstacles_sets = []
    
    # 直接处理obstacle_id_sets作为列表
    if isinstance(obstacle_id_sets, list):
        for item in obstacle_id_sets:
            if isinstance(item, dict):
                # 查找包含obstacle_id的字段
                for key, value in item.items():
                    if 'obstacle_id' in key:
                        try:
                            # 尝试将ID转换为整数
                            obstacles_sets.append(int(value))
                        except (ValueError, TypeError):
                            obstacles_sets.append(value)  # 保留原始值
            else:
                obstacles_sets.append(item)
    elif isinstance(obstacle_id_sets, dict):
        # 处理字典类型的ID集合
        id_fields = [key for key in obstacle_id_sets.keys() if 'obstacle_id_' in key]
        id_fields.sort(key=lambda x: int(x.split('_')[-1]))  # 按数字排序
        
        for field in id_fields:
            try:
                obstacles_sets.append(int(obstacle_id_sets[field]))
            except (ValueError, TypeError):
                obstacles_sets.append(obstacle_id_sets[field])
    
    print(f"解析到 {len(obstacles_sets)} 个障碍物ID")
    return obstacles_sets

def parse_lowest_highest_bounds(total_data):
    """解析最低和最高边界"""
    if 'lowest_highest_bound_st' not in total_data:
        raise ValueError('输入TotalData必须包含lowest_highest_bound_st字段')
    
    cell_array = total_data['lowest_highest_bound_st']
    n = len(cell_array)
    
    result = [[] for _ in range(5)]
    
    for i in range(n):
        current_cell = cell_array[i]
        
        if isinstance(current_cell, dict):
            lower_higher_fields = []
            for field in current_cell.keys():
                if field.startswith('lower_higher_'):
                    value = current_cell[field]
                    if isinstance(value, (int, float)) and not np.isnan(value):
                        lower_higher_fields.append(field)
            
            lower_higher_fields.sort(key=lambda x: int(x.replace('lower_higher_', '')))
            
            values = [current_cell[field] for field in lower_higher_fields]
            result[i] = values
        else:
            warnings.warn(f'第{i+1}个cell不是字典，已填充空列表')
            result[i] = []
    
    for i in range(n, 5):
        result[i] = []
    
    print(f'解析完成，生成了包含 {len(result)} 个cell的数组')
    return result

def process_and_plot(ax1, ax2_1, ax2_2, ax2_3, file_path):
    """处理数据并绘制图形，返回是否成功"""
    try:
        # 清除所有轴内容
        for ax in [ax1, ax2_1, ax2_2, ax2_3]:
            ax.clear()
        
        # 检查JSON文件
        is_empty, is_valid = check_json_file(file_path)
        if is_empty or not is_valid:
            return False
        
        # 读取并解析JSON数据
        with open(file_path, 'r') as f:
            total_data = json.load(f)
        
        # 解析点数据并获取最大索引
        lower_points_total, upper_points_total, max_point_index = parse_json_points(total_data)
        lower_higher_sets = parse_lowest_highest_bounds(total_data)
        unit_t = 1.0
        
        # 解析障碍物ID（改进版本）
        obstacle_id_sets = total_data.get('obstacle_id_sets', {})
        obstacles_sets = convert_cell_to_struct_array(obstacle_id_sets)
        
        # 处理速度规划数据
        exist_speed_plan_data = 'SpeedPlanData' in total_data
        exist_speed_plan_optimizer_data = 'SpeedPlanOptimizerData' in total_data
        
        if not exist_speed_plan_data:
            print("速度规划失败")
        if not exist_speed_plan_optimizer_data:
            print("速度优化失败")
        
        speed_plan_data = total_data.get('SpeedPlanData', [])
        speed_plan_optimizer_data = total_data.get('SpeedPlanOptimizerData', [])
        
        # 提取速度规划数据
        sp_t, sp_s, sp_v, sp_a, sp_da = [], [], [], [], []
        for item in speed_plan_data:
            sp_t.append(item.get('t', 0))
            sp_s.append(item.get('s', 0))
            sp_v.append(item.get('v', 0))
            sp_a.append(item.get('a', 0))
            sp_da.append(item.get('da', 0))
        
        # 提取速度优化数据
        spo_t, spo_s, spo_v, spo_a, spo_da = [], [], [], [], []
        for item in speed_plan_optimizer_data:
            spo_t.append(item.get('t', 0))
            spo_s.append(item.get('s', 0))
            spo_v.append(item.get('v', 0))
            spo_a.append(item.get('a', 0))
            spo_da.append(item.get('da', 0))
        
        # 绘制fig2：速度相关图
        ax2_1.plot(sp_t, sp_v, '-o', label='Speed Plan', linewidth=1.2, markersize=3)
        ax2_1.plot(spo_t, spo_v, '-*', label='Optimized', linewidth=1.2, markersize=3)
        ax2_1.set_xlabel("Time")
        ax2_1.set_ylabel("Velocity")
        ax2_1.legend()
        
        # 加速度-时间图
        ax2_2.plot(spo_t, spo_a, '-o', color='green', linewidth=1.2, markersize=3)
        ax2_2.set_xlabel("Time")
        ax2_2.set_ylabel("Acc")
        
        # 加加速度-时间图
        ax2_3.plot(spo_t, spo_da, '-o', color='red', linewidth=1.2, markersize=3)
        ax2_3.set_xlabel("Time")
        ax2_3.set_ylabel("dAcc")
        
        # 绘制fig1：s-t图
        ax1.plot(sp_t, sp_s, '-o', label='Speed Plan', linewidth=1.2, markersize=3)
        ax1.plot(spo_t, spo_s, '-*', label='Optimized', linewidth=1.2, markersize=3)
        
        # 绘制障碍物边界和ID（改进的ID匹配逻辑）
        for i in range(len(lower_points_total)):
            lower_points = lower_points_total[i]
            upper_points = upper_points_total[i]
            
            # 改进的障碍物ID匹配
            if i < len(obstacles_sets):
                # 尝试将ID转换为整数
                try:
                    obstacle_id = int(obstacles_sets[i])
                except (ValueError, TypeError):
                    obstacle_id = obstacles_sets[i]  # 保留原始值
            else:
                # 当ID集合长度不足时，使用"?"标识未知ID
                obstacle_id = "?"
                print(f"警告：障碍物 {i} 没有对应的ID数据")
            
            # 只绘制有数据的障碍物
            if lower_points or upper_points:
                for j in range(len(lower_points)):
                    # 绘制lower_points连接线
                    if j < len(lower_points) - 1:
                        ax1.plot(
                            [lower_points[j]['t'], lower_points[j+1]['t']],
                            [lower_points[j]['s'], lower_points[j+1]['s']],
                            '-h', color='orange', linewidth=0.8
                        )
                    else:
                        if j < len(upper_points):  # 检查upper_points索引有效性
                            ax1.plot(
                                [lower_points[j]['t'], upper_points[j]['t']],
                                [lower_points[j]['s'], upper_points[j]['s']],
                                '-h', color='orange', linewidth=0.8
                            )
                    
                    # 第一个点的特殊处理
                    if j == 0 and j < len(upper_points):
                        ax1.plot(
                            [lower_points[j]['t'], upper_points[j]['t']],
                            [lower_points[j]['s'], upper_points[j]['s']],
                            '-h', color='orange', linewidth=0.8
                        )
                    
                    # 绘制upper_points连接线
                    if j < len(upper_points) - 1:
                        ax1.plot(
                            [upper_points[j]['t'], upper_points[j+1]['t']],
                            [upper_points[j]['s'], upper_points[j+1]['s']],
                            '-h', color='orange', linewidth=0.8
                        )
                
                # 添加障碍物ID文本（只显示数字）
                if upper_points:
                    text_x = upper_points[0]['t']
                    text_y = upper_points[0]['s'] + 2.0
                    ax1.text(
                        text_x, 
                        text_y, 
                        f"{obstacle_id}",  # 只显示ID数字
                        fontsize=10,
                        bbox=dict(facecolor='white', edgecolor='gray', pad=0.3, alpha=0.7)
                    )
        
        # 绘制最低最高边界
        for i in range(len(lower_higher_sets)):
            time_pos = i * unit_t  # 正确的时间轴位置
            
            for j in range(len(lower_higher_sets[i])):
                if i == 0 and j == 0:
                    ax1.plot(
                        [time_pos], 
                        [lower_higher_sets[i][j]], 
                        '^', color='purple', markersize=6, label='Bounds'
                    )
                    if j + 1 < len(lower_higher_sets[i]):
                        ax1.plot(
                            [0, 4], 
                            [lower_higher_sets[i][j+1]]*2,
                            '-', color='purple', linewidth=1.0, linestyle='--'
                        )
                    break
                else:
                    ax1.plot(
                        [time_pos], 
                        [lower_higher_sets[i][j]], 
                        '^', color='purple', markersize=6
                    )
        
        # 设置s-t图属性
        ax1.set_xlabel("Time")
        ax1.set_ylabel("S")
        ax1.set_xlim([0, 4])
        ax1.set_ylim([-5, 110])
        ax1.legend(fontsize=8)
        
        return True
        
    except Exception as e:
        print(f"处理数据时发生错误: {str(e)}")
        return False

def main():
    # 创建两个独立图形
    fig1, ax1 = plt.subplots(figsize=(8, 6))
    fig2, (ax2_1, ax2_2, ax2_3) = plt.subplots(3, 1, figsize=(8, 10))
    
    # 确保图形可以交互并刷新
    fig1.canvas.draw()
    fig2.canvas.draw()
    
    # 文件路径
    file_path = '../data/data.json'
    
    # 重试计数器
    retry_count = 0
    
    def update(frame):
        nonlocal retry_count
        
        # 尝试处理和绘制数据
        success = process_and_plot(ax1, ax2_1, ax2_2, ax2_3, file_path)
        
        # 强制刷新两个图形
        fig1.canvas.draw()
        fig2.canvas.draw()
        
        # 调整布局
        fig1.tight_layout()
        fig2.tight_layout()
        
        # 如果失败且未达到最大重试次数，等待后重试
        if not success:
            retry_count += 1
            print(f"将在 {RETRY_DELAY} 秒后进行第 {retry_count} 次重试...")
            
            # 检查是否达到最大重试次数
            if MAX_RETRIES is not None and retry_count >= MAX_RETRIES:
                print(f"已达到最大重试次数 ({MAX_RETRIES})，程序将退出")
                plt.close('all')  # 关闭所有图形窗口
                return []
            
            # 等待重试延迟
            plt.pause(RETRY_DELAY)
            return update(frame)  # 递归调用以重试
        
        # 重置重试计数器
        retry_count = 0
        return [ax1, ax2_1, ax2_2, ax2_3]
    
    # 创建动画
    ani = FuncAnimation(
        fig1,
        update,
        interval=300,
        blit=False
    )
    
    plt.show()

if __name__ == "__main__":
    main()
    