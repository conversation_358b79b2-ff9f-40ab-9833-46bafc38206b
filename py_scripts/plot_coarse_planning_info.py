from bokeh.plotting import figure
from bokeh.models import Legend
from bokeh.palettes import Category10
from bokeh.server.server import Server
from bokeh.application import Application
from bokeh.application.handlers.function import FunctionHandler
import json
import socket

# 获取服务器本地 IP（用于配置白名单）
def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('**************', 1))
        ip = s.getsockname()[0]
    except Exception:
        ip = '127.0.0.1'
    finally:
        s.close()
    return ip

# 加载数据
with open('../data/data.json') as f:
    data = json.load(f)

# 准备数据
time = [i * 0.2 for i in range(len(data['st_samples_s']))]
s = data['st_samples_s']  # 位移
v = data['st_samples_v']  # 速度
a = data['st_samples_a']  # 加速度

# 创建 Bokeh 文档的函数
def create_plot(doc):
    p = figure(
        title="Coarse Planning Info",
        x_axis_label="时间 (t)",
        y_axis_label="数值",
        width=1000,
        height=600,
        tools="pan,box_zoom,wheel_zoom,reset",
        toolbar_location="above"
    )

    colors = Category10[3]
    items = []
    for i, (values, label) in enumerate(zip([s, v, a], ["位移 (s)", "速度 (v)", "加速度 (a)"])):
        scatter = p.scatter(time, values, size=4, color=colors[i], alpha=0.8, name=label.lower(), marker="circle")
        line = p.line(time, values, line_color=colors[i], line_width=2, name=f"{label.lower()}_line")
        items.append((label, [scatter, line]))

    legend = Legend(items=items, location="top_left")
    p.add_layout(legend, 'right')
    legend.click_policy = "hide"

    doc.add_root(p)

# 服务器配置
server_ip = get_local_ip()
port = 5011
allowed_origins = [f"{server_ip}:{port}", "**************:5011", "localhost:5011"]

server = Server(
    applications={'/': Application(FunctionHandler(create_plot))},
    port=port,
    address="0.0.0.0",
    allow_websocket_origin=allowed_origins,
    autostart=False,
    use_index=False
)

def start_server():
    server.start()
    print(f"\n{'=' * 60}\n数据可视化已完成！请从本地浏览器访问：\nhttp://**************:{port}\n{'=' * 60}\n")
    try:
        server.io_loop.start()
    except KeyboardInterrupt:
        print("\n服务已正常停止")
        server.io_loop.stop()

if __name__ == '__main__':
    start_server()
    