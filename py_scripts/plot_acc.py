import re
import matplotlib.pyplot as plt

# 初始化存储数据的列表
time_list = []
localization_velocity = []
chassis_velocity = []
time_match_v = []
localization_acc = []
chassis_acc = []
time_match_a = []

# 读取日志文件
with open('acc_plot.log', 'r') as file:
    for line in file:
        # 匹配时间戳
        time_match = re.match(r'(\d{2}:\d{2}:\d{2}\.\d+)', line)
        if time_match:
            time_str = time_match.group(1)
            # 将时间转换为秒数（从日志开始计算的相对时间）
            if not time_list:  # 第一个时间点
                start_time = time_str
                time_list.append(0.0)
            else:
                # 计算时间差（简化处理，实际可能需要更精确的时间解析）
                time_diff = (float(time_str.split(':')[-1]) - float(start_time.split(':')[-1]))
                time_list.append(time_list[-1] + time_diff)
            
            # 匹配速度行
            if 'localizaiton_velocity' in line:
                vel_matches = re.findall(r'localizaiton_velocity: ([\d.-]+) chassis_velocity: ([\d.-]+) time_match_v: ([\d.-]+)', line)
                if vel_matches:
                    localization_velocity.append(float(vel_matches[0][0]))
                    chassis_velocity.append(float(vel_matches[0][1]))
                    time_match_v.append(float(vel_matches[0][2]))
            
            # 匹配加速度行
            elif 'localization_acc' in line:
                acc_matches = re.findall(r'localization_acc: ([\d.-]+) chassis_acc: ([\d.-]+) time_match_a: ([\d.-]+)', line)
                if acc_matches:
                    localization_acc.append(float(acc_matches[0][0]))
                    chassis_acc.append(float(acc_matches[0][1]))
                    time_match_a.append(float(acc_matches[0][2]))

# 绘制速度图表
plt.figure(1)
plt.plot(time_list[:len(localization_velocity)], localization_velocity, label='localization_velocity')
plt.plot(time_list[:len(chassis_velocity)], chassis_velocity, label='chassis_velocity')
plt.plot(time_list[:len(time_match_v)], time_match_v, label='time_match_v')
plt.xlabel('Time (s)')
plt.ylabel('Velocity')
plt.title('Velocity Comparison')
plt.legend()
plt.grid(True)

# 绘制加速度图表
plt.figure(2)
plt.plot(time_list[:len(localization_acc)], localization_acc, label='localization_acc')
plt.plot(time_list[:len(chassis_acc)], chassis_acc, label='chassis_acc')
plt.plot(time_list[:len(time_match_a)], time_match_a, label='time_match_a')
plt.xlabel('Time (s)')
plt.ylabel('Acceleration')
plt.title('Acceleration Comparison')
plt.legend()
plt.grid(True)

plt.show()
