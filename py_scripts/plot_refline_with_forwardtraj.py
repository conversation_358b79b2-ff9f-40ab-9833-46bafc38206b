import matplotlib.pyplot as plt
import json
import re

# 加载数据
with open('../data/data.json') as f:
    data = json.load(f)

# 提取 forward_simulated_traj_*（支持多个）
traj_lines = []
for key in data:
    if re.match(r'^forward_simulated_traj_\d+$', key):
        traj = data[key]
        traj_lines.append({
            'name': key,
            'x': [point['x'] for point in traj],
            'y': [point['y'] for point in traj],
            'theta': [point['theta'] for point in traj],
            'kappa': [point['kappa'] for point in traj],
        })

# 提取参考线 reference_line_*
ref_lines = []
for key in data:
    if re.match(r'^reference_line_\d+$', key):
        ref = data[key]
        ref_lines.append({
            'name': key,
            'x': [point['x'] for point in ref],
            'y': [point['y'] for point in ref]
        })

# fallback：如果没有 reference_line_*，尝试单个 reference_line
if not ref_lines and 'reference_line' in data:
    ref = data['reference_line']
    ref_lines.append({
        'name': 'reference_line',
        'x': [point['x'] for point in ref],
        'y': [point['y'] for point in ref]
    })

# ========== 图1: XY轨迹 ==========
plt.figure(figsize=(12, 8))
for ref in ref_lines:
    plt.plot(ref['x'], ref['y'], linestyle='--', label=ref['name'], alpha=0.6)
for traj in traj_lines:
    plt.plot(traj['x'], traj['y'], label=traj['name'])
plt.xlabel("X")
plt.ylabel("Y")
plt.title("Multiple Trajectories and Reference Lines")
plt.axis("equal")
plt.legend()
plt.grid(True)
plt.tight_layout()

# ========== 图2: Theta 曲线 ==========
plt.figure(figsize=(12, 4))
for traj in traj_lines:
    plt.plot(traj['theta'], label=f"{traj['name']} - theta")
plt.title("Heading Angle (theta) of Forward Simulated Trajectories")
plt.xlabel("Index")
plt.ylabel("Theta [rad]")
plt.grid(True)
plt.legend()
plt.tight_layout()

# ========== 图3: Kappa 曲线 ==========
plt.figure(figsize=(12, 4))
for traj in traj_lines:
    plt.plot(traj['kappa'], label=f"{traj['name']} - kappa")
plt.title("Curvature (kappa) of Forward Simulated Trajectories")
plt.xlabel("Index")
plt.ylabel("Kappa [1/m]")
plt.grid(True)
plt.legend()
plt.tight_layout()

plt.show()
