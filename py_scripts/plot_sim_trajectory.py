from bokeh.plotting import figure
from bokeh.models import Legend
from bokeh.palettes import Category10
from bokeh.server.server import Server
from bokeh.application import Application
from bokeh.application.handlers.function import FunctionHandler
import json
import socket
import re

# 获取服务器本地 IP（用于配置白名单）
def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('**************', 1))
        ip = s.getsockname()[0]
    except Exception:
        ip = '127.0.0.1'
    finally:
        s.close()
    return ip

# 加载数据
with open('../data/data.json') as f:
    data = json.load(f)

# 准备数据 - 提取x,y路径点
trajectory = data.get('forward_simulated_traj', [])
x_traj = [point['x'] for point in trajectory]
y_traj = [point['y'] for point in trajectory]

# 准备数据 - 提取 ruckig_traj 下的坐标点
ruckig_traj = data.get('ruckig_traj', [])
x_ruckig = [point['x'] for point in ruckig_traj]
y_ruckig = [point['y'] for point in ruckig_traj]

# 准备数据 - 提出SL 优化路径点
piecewise_jerk_path = data.get('piecewise_jerk_path', [])
x_piecewise_jerk_path = [point['x'] for point in piecewise_jerk_path]
y_piecewise_jerk_path = [point['y'] for point in piecewise_jerk_path]

# 准备数据 - 提取所有参考线（reference_line_0, reference_line_1等）
ref_lines = []
for key in data.keys():
    if re.match(r'^reference_line_\d+$', key):
        ref_line = data[key]
        ref_lines.append({
            'name': key,
            'x': [point['x'] for point in ref_line],
            'y': [point['y'] for point in ref_line]
        })

# 如果没有特定编号的参考线，使用默认的reference_line
if not ref_lines and 'reference_line' in data:
    ref_lines.append({
        'name': 'reference_line',
        'x': [point['x'] for point in data['reference_line']],
        'y': [point['y'] for point in data['reference_line']]
    })

# 计算数据范围（可选，用于固定比例尺）
all_x = []
all_y = []
for ref in ref_lines:
    all_x.extend(ref['x'])
    all_y.extend(ref['y'])
all_x += x_traj + x_ruckig
all_y += y_traj + y_ruckig

x_range = (min(all_x) - 1, max(all_x) + 1)  # 适当扩展范围
y_range = (min(all_y) - 1, max(all_y) + 1)  # 适当扩展范围

# 创建 Bokeh 文档的函数
def create_plot(doc):
    # 创建图形对象，设置标题、标签、尺寸和工具
    p = figure(
        title="Path Planning Visualization",
        x_axis_label="X Position",
        y_axis_label="Y Position",
        width=1000,
        height=600,
        tools="pan,box_zoom,wheel_zoom,reset",
        toolbar_location="above",
        # 禁用比例尺缩放，确保x和y轴比例一致
        match_aspect=True,
        # 固定x和y轴的范围（可选）
        x_range=x_range,
        y_range=y_range
    )

    # 图例项列表
    legend_items = []
    
    # 绘制所有参考线
    colors = Category10[10]  # 使用10种颜色循环
    for i, ref in enumerate(ref_lines):
        color = colors[i % len(colors)]
        scatter_ref = p.scatter(ref['x'], ref['y'], size=4, color=color, alpha=0.6)
        line_ref = p.line(ref['x'], ref['y'], line_color=color, line_width=2, line_dash="dashed")
        legend_items.append((ref['name'], [scatter_ref, line_ref]))

    # 绘制轨迹点
    scatter_traj = p.scatter(x_traj, y_traj, size=4, color="blue", alpha=0.8)
    line_traj = p.line(x_traj, y_traj, line_color="blue", line_width=2)
    legend_items.append(("Trajectory", [scatter_traj, line_traj]))

    # 绘制 ruckig_traj 下的坐标点
    scatter_ruckig = p.scatter(x_ruckig, y_ruckig, size=4, color="red", alpha=0.8)
    line_ruckig = p.line(x_ruckig, y_ruckig, line_color="red", line_width=2)
    legend_items.append(("Ruckig Trajectory", [scatter_ruckig, line_ruckig]))

    # 绘制 piecewise_jerk_path 下的坐标点
    scatter_piecewise_jerk = p.scatter(x_piecewise_jerk_path, y_piecewise_jerk_path, size=4, color="green", alpha=0.8)
    line_piecewise_jerk = p.line(x_piecewise_jerk_path, y_piecewise_jerk_path, line_color="green", line_width=2)
    legend_items.append(("Piecewise Jerk Path", [scatter_piecewise_jerk, line_piecewise_jerk]))

    # 添加图例
    legend = Legend(items=legend_items, location="top_left")
    p.add_layout(legend, 'right')
    legend.click_policy = "hide"

    doc.add_root(p)

# 服务器配置
# server_ip = get_local_ip()
server_ip = "localhost"

port = 5011
allowed_origins = [
    f"{server_ip}:{port}",
    "**************:5011",
    "localhost:5011"
]

server = Server(
    applications={'/': Application(FunctionHandler(create_plot))},
    port=port,
    address="0.0.0.0",
    allow_websocket_origin=allowed_origins,
    autostart=False,
    use_index=False
)

def start_server():
    server.start()
    print(f"\n{'=' * 60}\nPath visualization is ready! Open in browser:\nhttp://{server_ip}:{port}\n{'=' * 60}\n")
    try:
        server.io_loop.start()
    except KeyboardInterrupt:
        print("\nServer stopped gracefully")
        server.io_loop.stop()

if __name__ == '__main__':
    start_server()