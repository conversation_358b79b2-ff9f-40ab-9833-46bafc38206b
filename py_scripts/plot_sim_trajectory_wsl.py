from bokeh.plotting import output_file, save, figure
from bokeh.models import Legend
from bokeh.palettes import Category10
import json
import re
import matplotlib.pyplot as plt
import numpy as np

# 加载数据
with open('../data/data.json') as f:
    data = json.load(f)

# 准备数据
path_left_bound = data.get('path_left_bound', [])
path_left_bound_x = [point['x'] for point in path_left_bound]
path_left_bound_y = [point['y'] for point in path_left_bound]

path_right_bound = data.get('path_right_bound', [])
path_right_bound_x = [point['x'] for point in path_right_bound]
path_right_bound_y = [point['y'] for point in path_right_bound]

piecewise_jerk_path = data.get('piecewise_jerk_path', [])
x_piecewise_jerk_path = [point['x'] for point in piecewise_jerk_path]
y_piecewise_jerk_path = [point['y'] for point in piecewise_jerk_path]

obs_sl_polygons = []
for key in data.keys():
    if re.match(r'^obs_sl_polygon_\d+$', key):
        obs_sl_polygon = data[key]
        obs_sl_polygons.append({
            'name': key,
            'x': [point['x'] for point in obs_sl_polygon],
            'y': [point['y'] for point in obs_sl_polygon]
        })

ref_lines = []
for key in data.keys():
    if re.match(r'^reference_line_\d+$', key):
        ref_line = data[key]
        ref_lines.append({
            'name': key,
            'x': [point['x'] for point in ref_line],
            'y': [point['y'] for point in ref_line]
        })
if not ref_lines and 'reference_line' in data:
    ref_lines.append({
        'name': 'reference_line',
        'x': [point['x'] for point in data['reference_line']],
        'y': [point['y'] for point in data['reference_line']]
    })

# 自动匹配等比例范围
all_x = path_left_bound_x + path_right_bound_x + x_piecewise_jerk_path
all_y = path_left_bound_y + path_right_bound_y + y_piecewise_jerk_path
for ref in ref_lines:
    all_x.extend(ref['x'])
    all_y.extend(ref['y'])

min_x, max_x = min(all_x), max(all_x)
min_y, max_y = min(all_y), max(all_y)
range_x = max_x - min_x
range_y = max_y - min_y
max_range = max(range_x, range_y)
padding = 1.0

x_center = (min_x + max_x) / 2
y_center = (min_y + max_y) / 2

x_range = (x_center - max_range / 2 - padding, x_center + max_range / 2 + padding)
y_range = (y_center - max_range / 2 - padding, y_center + max_range / 2 + padding)

# 创建图形对象
p = figure(
    title="Path Planning Visualization",
    x_axis_label="X Position",
    y_axis_label="Y Position",
    width=1000,
    height=600,
    tools="pan,box_zoom,wheel_zoom,reset",
    toolbar_location="above",
    match_aspect=True,
    x_range=x_range,
    y_range=y_range
)

legend_items = []
colors = Category10[10]

for i, ref in enumerate(obs_sl_polygons):
    color = colors[i % len(colors)]
    scatter_ref = p.scatter(ref['x'], ref['y'], size=4, color=color, alpha=0.6)
    line_ref = p.line(ref['x'], ref['y'], line_color=color, line_width=2)  # 去掉 line_dash
    legend_items.append((ref['name'], [scatter_ref, line_ref]))


# 绘制参考线
for i, ref in enumerate(ref_lines):
    color = colors[i % len(colors)]
    scatter_ref = p.scatter(ref['x'], ref['y'], size=4, color=color, alpha=0.6)
    line_ref = p.line(ref['x'], ref['y'], line_color=color, line_width=2, line_dash="dashed")
    legend_items.append((ref['name'], [scatter_ref, line_ref]))

# 左边界：细实线
line_left_bound = p.line(
    path_left_bound_x, path_left_bound_y,
    line_color="blue", line_width=1
)
legend_items.append(("path_left_bound", [line_left_bound]))

# 右边界：细实线
line_right_bound = p.line(
    path_right_bound_x, path_right_bound_y,
    line_color="red", line_width=1
)
legend_items.append(("path_right_bound", [line_right_bound]))


scatter_piecewise_jerk = p.scatter(x_piecewise_jerk_path, y_piecewise_jerk_path, size=4, color="green", alpha=0.8)
line_piecewise_jerk = p.line(x_piecewise_jerk_path, y_piecewise_jerk_path, line_color="green", line_width=2)
legend_items.append(("Piecewise Jerk Path", [scatter_piecewise_jerk, line_piecewise_jerk]))

legend = Legend(items=legend_items, location="top_left")
p.add_layout(legend, 'right')
legend.click_policy = "hide"

# 输出 HTML
output_file("plot.html")
save(p)

print("✅ plot.html 文件已生成，可在浏览器中打开查看")


curvature = np.array(y_piecewise_jerk_path)  # 你的曲率列表
# 设置步长为 0.5
ds = 0.5

# 差分计算曲率变化率
d_curvature = np.diff(curvature)
curvature_rate = d_curvature / ds

# 为了让数组长度匹配（和曲率一样长），可以在末尾补一个值
curvature_rate = np.append(curvature_rate, curvature_rate[-1])  # 或 curvature_rate.append(0)

# 画图
fig, axs = plt.subplots(2, 1, figsize=(10, 8))

# 第一个子图：曲率
axs[0].plot(curvature)
axs[0].set_title('Curvature')
axs[0].set_xlabel('Index')
axs[0].set_ylabel('Curvature')
axs[0].grid(True)

# 第二个子图：曲率变化率
axs[1].plot(curvature_rate)
axs[1].set_title('Curvature Rate (dκ/ds), ds=0.5')
axs[1].set_xlabel('Index')
axs[1].set_ylabel('dκ/ds')
axs[1].grid(True)

plt.tight_layout()
plt.show()



# fig, axs = plt.subplots(1, 2, figsize=(12, 6))

# # 第一个子图：曲率
# axs[0].plot(y_piecewise_jerk_path)
# axs[0].set_title('Curvature')
# axs[0].set_xlabel('Index')
# axs[0].set_ylabel('Curvature')
# axs[0].grid(True)

# # 第二个子图：航向角
# axs[1].plot(x_piecewise_jerk_path)
# axs[1].set_title('Yaw')
# axs[1].set_xlabel('Index')
# axs[1].set_ylabel('Yaw')
# axs[1].grid(True)

# # 自动布局
# plt.tight_layout()
# plt.show()
