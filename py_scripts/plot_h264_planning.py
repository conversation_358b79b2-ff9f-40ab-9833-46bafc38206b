import json
import glob
from bokeh.plotting import figure, curdoc, output_file, save
from bokeh.models import ColumnDataSource, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Legend
from bokeh.layouts import column, row
import pandas as pd
from bokeh.palettes import Category10
import re


# 加载数据
with open('../data/data.json') as f:
    data = json.load(f)

# 获取frame_id
frame_id = data.get("frame_id", 0)

print(frame_id)



