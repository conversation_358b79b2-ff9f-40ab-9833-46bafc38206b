from bokeh.plotting import output_file, save, figure
from bokeh.models import Legend, ColumnDataSource
from bokeh.palettes import Category10
import json


# 加载数据（实际使用时取消注释以下行）
with open('../data/data.json') as f:
    data = json.load(f)


# 提取ST边界点
lower_points = data.get('STBoundaryLowerPoints', [])
upper_points = data.get('STBoundaryUpperPoints', [])
speed_data = data.get('SpeedPlannData', [])

# 解析坐标数据
lower_s = [p['s'] for p in lower_points]
lower_t = [p['t'] for p in lower_points]
upper_s = [p['s'] for p in upper_points]
upper_t = [p['t'] for p in upper_points]
speed_s = [p['s'] for p in speed_data]
speed_t = [p['t'] for p in speed_data]
speed_v = [p['v'] for p in speed_data]

# 构建闭合多边形路径（严格按照指定顺序）
closed_path_t = lower_t + [upper_t[-1]] + upper_t[::-1] + [lower_t[0]]
closed_path_s = lower_s + [upper_s[-1]] + upper_s[::-1] + [lower_s[0]]

# 自动计算等比例范围
all_s = lower_s + upper_s + speed_s + closed_path_s
all_t = lower_t + upper_t + speed_t + closed_path_t

min_s, max_s = min(all_s), max(all_s)
min_t, max_t = min(all_t), max(all_t)
range_s = max_s - min_s
range_t = max_t - min_t
max_range = max(range_s, range_t)
padding = 0.5  # 边界填充量

s_center = (min_s + max_s) / 2
t_center = (min_t + max_t) / 2

s_range = (s_center - max_range / 2 - padding, s_center + max_range / 2 + padding)
t_range = (t_center - max_range / 2 - padding, t_center + max_range / 2 + padding)

# 创建图形对象
p = figure(
    title="ST Boundary Closed Polygon",
    x_axis_label="Time (t)",
    y_axis_label="Displacement (s)",
    width=1500,
    height=600,
    tools="pan,box_zoom,wheel_zoom,reset",
    toolbar_location="above",
    match_aspect=True,
    x_range=t_range,
    y_range=s_range
)

legend_items = []
colors = Category10[10]

# 绘制ST边界多边形（仅边框，无内部连线）
line_polygon = p.line(closed_path_t, closed_path_s, 
                      line_color=colors[0], line_width=2, line_alpha=0.8)
legend_items.append(("ST Boundary", [line_polygon]))

# 绘制ST边界点
scatter_lower = p.scatter(lower_t, lower_s, size=6, color=colors[0], alpha=0.8)
scatter_upper = p.scatter(upper_t, upper_s, size=6, color=colors[1], alpha=0.8)
legend_items.append(("Lower Points", [scatter_lower]))
legend_items.append(("Upper Points", [scatter_upper]))

# 绘制速度规划曲线
scatter_speed = p.scatter(speed_t, speed_s, size=6, color=colors[2], alpha=0.8)
line_speed = p.line(speed_t, speed_s, line_color=colors[2], line_width=2, line_dash="dashed")
legend_items.append(("Speed Planning", [scatter_speed, line_speed]))

# 添加速度值标签
label_indices = range(0, len(speed_t), max(1, len(speed_t) // 5))
label_x = [speed_t[i] for i in label_indices]
label_y = [speed_s[i] for i in label_indices]
label_text = [f"v={speed_v[i]:.2f}" for i in label_indices]

source = ColumnDataSource(data=dict(x=label_x, y=label_y, text=label_text))
p.text(x='x', y='y', text='text', source=source,
       text_color=colors[2], text_align="center", text_baseline="bottom")

# 设置图例
legend = Legend(items=legend_items, location="top_left")
p.add_layout(legend, 'right')
legend.click_policy = "hide"

# 输出HTML
output_file("st_planner.html")
save(p)

print("✅ st_planner.html 文件已生成，可在浏览器中打开查看")