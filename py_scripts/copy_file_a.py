import os
import shutil

# 设置源文件夹和目标文件夹路径
source_folder = "../build"
target_folder = "../../ArcSim-share-0.1.1/extern_libs/arcsoft_ads_planning"

# 要同步的文件名列表
files_to_sync = ["libarcsoft_ads_planning.a", "libosqp.a"]

def sync_specific_files(source, target, filenames):
    for filename in filenames:
        src_path = os.path.join(source, filename)
        dst_path = os.path.join(target, filename)

        if os.path.exists(src_path):
            shutil.copy2(src_path, dst_path)
            print(f"Copied: {src_path} → {dst_path}")
        else:
            print(f"File not found in source: {src_path}")


if __name__ == '__main__':
    sync_specific_files(source_folder, target_folder, files_to_sync)