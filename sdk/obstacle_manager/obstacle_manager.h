
/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __OBSTACLE_DECIDER_H__
#define __OBSTACLE_DECIDER_H__

#include "common/basic_types.h"
#include "common/math/index_list.h"
#include "framework/session.h"

using Polygon2d = arcsoft::common::Polygon2d;

namespace arcsoft {
namespace planning {

class ObstacleManager {
public:
    explicit ObstacleManager(framework::Session* session);
    ~ObstacleManager() = default;

    bool Process();

    double LookUpTableThredRatio(const double veh_speed_kph) const;

private:
    framework::Session* session_;

    void Fusion();

    void CalculateFrenetObstacles();

    void GetObstacleCorners(double x, double y, double yaw, double length,
                            double width, Polygon2d& polygon);
    std::vector<double> veh_thred_ration_brk_pnt = {0.0, 5, 15, 150};
    std::vector<double> veh_thred_ration_table = {0.5, 0.5, 1, 1};
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __OBSTACLE_DECIDER_H__ */
