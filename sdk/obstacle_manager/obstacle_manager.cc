/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "obstacle_manager.h"

namespace arcsoft {
namespace planning {

ObstacleManager::ObstacleManager(framework::Session* session)
    : session_(session) {}

// 障碍物管理模块，处理障碍物信息
bool ObstacleManager::Process() {
    // 将感知与预测结果进行匹配
    Fusion();

    // 计算障碍物的Frenet坐标，再将其与lane进行绑定
    CalculateFrenetObstacles();

    return true;
}

void ObstacleManager::Fusion() {
    auto& obstacles = session_->mutable_obstacles();
    obstacles.Clear();
    const auto& init_point = session_->planning_start_point();
    auto& data_manager = session_->data_manager();
    // 将感知与预测结果进行匹配
    for (const auto& perception_obstacle :
         session_->local_view().perception_obstacles) {
        const uint32_t obs_id = perception_obstacle.id;
        for (const auto& raw_prediction :
             session_->local_view().prediction_trajectories) {
            const auto& raw_obstacle_trajectory = raw_prediction.trajectory;
            if (raw_prediction.id == obs_id) {
                std::vector<TrajectoryPoint> obs_trajectory;
                obs_trajectory.reserve(raw_obstacle_trajectory.num_points + 1);
                uint8_t obstacle_stop_index =
                    raw_obstacle_trajectory.num_points;
                for (size_t i = 0; i < raw_obstacle_trajectory.num_points;
                     ++i) {
                    TrajectoryPoint obstacle_traj_pt;
                    obstacle_traj_pt.x = raw_obstacle_trajectory.points[i].x;
                    obstacle_traj_pt.y = raw_obstacle_trajectory.points[i].y;
                    obstacle_traj_pt.heading =
                        raw_obstacle_trajectory.points[i].yaw;
                    obstacle_traj_pt.t = raw_obstacle_trajectory.points[i].t;
                    obstacle_traj_pt.v =
                        raw_obstacle_trajectory.points[i].velocity;
                    obstacle_traj_pt.a =
                        raw_obstacle_trajectory.points[i].acceleration;
                    obs_trajectory.emplace_back(obstacle_traj_pt);
                }
                // [TEMP ADD] 添加最后一个点
                if (raw_obstacle_trajectory.num_points > 0) {
                    const auto& last_point =
                        raw_obstacle_trajectory
                            .points[raw_obstacle_trajectory.num_points - 1];
                    TrajectoryPoint obstacle_traj_pt;
                    obstacle_traj_pt.x = last_point.x;
                    obstacle_traj_pt.y = last_point.y;
                    obstacle_traj_pt.heading = last_point.yaw;
                    obstacle_traj_pt.t = last_point.t + 0.1;
                    obstacle_traj_pt.v = last_point.velocity;
                    obstacle_traj_pt.a = last_point.acceleration;
                    obs_trajectory.emplace_back(obstacle_traj_pt);
                }

                // const bool is_obstacle_static =
                //     perception_obstacle.absolute_velocity <= 0.5 ? true :
                //     false;
                double veh_speed_kph = std::abs(init_point.v * 3.6);
                if (veh_speed_kph < 5) veh_speed_kph = 5;
                const double veh_thred_ration =
                    LookUpTableThredRatio(veh_speed_kph);
                const double kit_veh_thred_in =
                    veh_thred_ration * (3.5 + 14 * veh_speed_kph / 130) / 3.6;
                const double kit_veh_thred_out =
                    veh_thred_ration * (3.5 + 1 + 35 * veh_speed_kph / 130) /
                    3.6;
                const bool is_obstacle_static =
                    perception_obstacle.absolute_velocity <= kit_veh_thred_in
                        ? true
                        : false;
                // AINFO << "obstacle id: " << obs_id
                //       << ", is_static: " << is_obstacle_static
                //       << ", absolute_velocity: "
                //       << perception_obstacle.absolute_velocity
                //       << " kit_veh_thred_in: " << kit_veh_thred_in
                //       << " veh_speed_kph: " << veh_speed_kph
                //       << " veh_thred_ration: " << veh_thred_ration;

                double obstacle_longitudinal_distance_center =
                    perception_obstacle.longitudinal_distance +
                    (perception_obstacle.length / 2.0) *
                        cos(perception_obstacle.yaw);
                double obstacle_lateral_distance_center =
                    perception_obstacle.lateral_distance +
                    (perception_obstacle.length / 2.0) *
                        sin(perception_obstacle.yaw);

                Polygon2d polygon;
                GetObstacleCorners(obstacle_longitudinal_distance_center,
                                   obstacle_lateral_distance_center,
                                   perception_obstacle.yaw,
                                   perception_obstacle.length,
                                   perception_obstacle.width, polygon);

                Obstacle obstacle(
                    obs_id, is_obstacle_static,
                    obstacle_longitudinal_distance_center,
                    obstacle_lateral_distance_center, perception_obstacle.yaw,
                    perception_obstacle.absolute_velocity,
                    perception_obstacle.absolute_acceleration,
                    perception_obstacle.length, perception_obstacle.width,
                    obs_trajectory, polygon);
                obstacles.Add(obstacle.id(), obstacle);
                break;
            }
        }
    }

    // for (const auto& obstacle : obstacles.Items()) {
    //     AINFO << "obstacle id: " << obstacle->id << " x: " <<
    //     obstacle->center_x
    //           << " y: " << obstacle->center_y;
    //     for (const auto& traj_pt : obstacle->trajectory) {
    //         AINFO << "trajectory pt.x: " << traj_pt.x << " y: " << traj_pt.y
    //               << " heading: " << traj_pt.heading << " v: " << traj_pt.v;
    //     }
    // }

    return;
}

void ObstacleManager::CalculateFrenetObstacles() {
    auto& obstacles = session_->mutable_obstacles();

    // 计算障碍物的Frenet坐标，再将其与lane进行绑定
    constexpr double kObastcleByLaneMinDis = 8.0;
    auto& reference_lines_dict =
        session_->mutable_reference_lines().MutableDict();
    for (auto& [id, ref_line] : reference_lines_dict) {
        const auto* frenet_coord = ref_line->frenet_coord();

        if (frenet_coord == nullptr) {
            continue;
        }

        for (const auto& obstacle : obstacles.Items()) {
            FrenetObstacle frenet_obstacle;
            SLPoint frenet_point;

            frenet_obstacle.id = obstacle->id();
            frenet_obstacle.is_static = obstacle->is_static();

            frenet_obstacle.type = PerceptionObstacleType::UNKNOWN;
            frenet_obstacle.sl_boundary.boundary_point.clear();

            frenet_coord->CartCoord2FrenetCoord(
                Point2D(obstacle->center_x(), obstacle->center_y()),
                frenet_point);

            frenet_obstacle.s =
                frenet_point.s - frenet_coord->ego_frenet_sl().s;
            frenet_obstacle.l = frenet_point.l;

            // 计算SLBoundary
            double start_s(std::numeric_limits<double>::max());
            double end_s(std::numeric_limits<double>::lowest());
            double start_l(std::numeric_limits<double>::max());
            double end_l(std::numeric_limits<double>::lowest());
            for (const auto& point : obstacle->polygon().points()) {
                // AINFO << "obstacle corner point: (" << point.x() << ", "
                //       << point.y() << ")";
                SLPoint polygon_corner_sl;
                frenet_coord->CartCoord2FrenetCoord(
                    Point2D{point.x(), point.y()}, polygon_corner_sl);

                start_s =
                    std::fmin(start_s, polygon_corner_sl.s -
                                           frenet_coord->ego_frenet_sl().s);
                end_s = std::fmax(end_s, polygon_corner_sl.s -
                                             frenet_coord->ego_frenet_sl().s);
                start_l = std::fmin(start_l, polygon_corner_sl.l);
                end_l = std::fmax(end_l, polygon_corner_sl.l);

                frenet_obstacle.sl_boundary.boundary_point.emplace_back(SLPoint(
                    polygon_corner_sl.s - frenet_coord->ego_frenet_sl().s,
                    polygon_corner_sl.l));
            }

            frenet_obstacle.sl_boundary.set_start_s(start_s);
            frenet_obstacle.sl_boundary.set_end_s(end_s);
            frenet_obstacle.sl_boundary.set_start_l(start_l);
            frenet_obstacle.sl_boundary.set_end_l(end_l);

            bool within_range = false;
            for (const auto& sl_pt :
                 frenet_obstacle.sl_boundary.boundary_point) {
                if (std::abs(sl_pt.l) < kObastcleByLaneMinDis) {
                    within_range = true;
                    break;
                }
            }

            if (within_range) {
                // AINFO << "obstacle id: " << frenet_obstacle.id
                //       << " is_static: " << frenet_obstacle.is_static
                //       << " ego_s: " << frenet_coord->ego_frenet_sl().s
                //       << " ego_l: " << frenet_coord->ego_frenet_sl().l;

                ref_line->add_frenet_obstacles(frenet_obstacle);
            }
        }
    }
}

void ObstacleManager::GetObstacleCorners(double x, double y, double yaw,
                                         double length, double width,
                                         Polygon2d& polygon)

{
    std::vector<Vec2d> corners;
    double half_l = length / 2.0;
    double half_w = width / 2.0;

    // 定义局部坐标下的角点（以中心点为原点）
    std::vector<std::pair<double, double>> local_corners = {
        {+half_l, +half_w},  // front-left
        {-half_l, +half_w},  // rear-left
        {-half_l, -half_w},  // rear-right
        {+half_l, -half_w}   // front-right
    };

    double cos_yaw = std::cos(yaw);
    double sin_yaw = std::sin(yaw);

    for (const auto& corner : local_corners) {
        double local_x = corner.first;
        double local_y = corner.second;

        // 旋转 + 平移到全局坐标系
        double global_x = x + local_x * cos_yaw - local_y * sin_yaw;
        double global_y = y + local_x * sin_yaw + local_y * cos_yaw;

        corners.push_back({global_x, global_y});
    }

    // for (const auto& corner : corners) {
    //     AINFO << "Obstacle corner: (" << corner.x() << ", " << corner.y() <<
    //     ")"
    //           << " x: " << x << " y: " << y << " yaw: " << yaw
    //           << " length: " << length << " width: " << width;
    // }
    polygon = Polygon2d(corners);
}

double ObstacleManager::LookUpTableThredRatio(
    const double veh_speed_kph) const {
    // 1. 检查容器是否为空
    if (veh_thred_ration_brk_pnt.empty() || veh_thred_ration_table.empty()) {
        return 22.0;  // 默认安全值
    }

    // 2. 检查输入是否为有效数值
    if (!std::isfinite(veh_speed_kph)) {
        return veh_thred_ration_table.front();  // 保守值
    }

    // 3. 检查边界情况
    if (veh_speed_kph <= veh_thred_ration_brk_pnt.front()) {
        return veh_thred_ration_table.front();
    }
    if (veh_speed_kph >= veh_thred_ration_brk_pnt.back()) {
        return veh_thred_ration_table.back();
    }

    auto it = std::lower_bound(veh_thred_ration_brk_pnt.begin(),
                               veh_thred_ration_brk_pnt.end(), veh_speed_kph);
    if (it ==
        veh_thred_ration_brk_pnt.begin()) {  // 理论上不会发生（因前置检查）
        return veh_thred_ration_table.front();
    }
    if (it == veh_thred_ration_brk_pnt.end()) {  // 理论上不会发生（因前置检查）
        return veh_thred_ration_table.back();
    }
    size_t upper_idx = it - veh_thred_ration_brk_pnt.begin();
    size_t lower_idx = upper_idx - 1;

    // 使用提供的lerp函数进行线性插值
    return math::lerp(veh_thred_ration_table[lower_idx],
                      veh_thred_ration_brk_pnt[lower_idx],
                      veh_thred_ration_table[upper_idx],
                      veh_thred_ration_brk_pnt[upper_idx], veh_speed_kph);
}

}  // namespace planning
}  // namespace arcsoft