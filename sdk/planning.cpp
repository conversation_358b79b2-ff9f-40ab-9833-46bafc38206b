#include "planning.hpp"

#include <stdio.h>

#include <Eigen/Dense>
#include <Eigen/Geometry>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <string>  // 包含<string>以便使用std::string

namespace ads {
namespace pln {

using VehicleLightFLAG = arcsoft::common::VehicleLightFLAG;
using VehicleGear = arcsoft::common::VehicleGear;
using AutoDriveModeState = arcsoft::common::AutoDriveModeState;

constexpr uint32_t ModalIndex = 1;
constexpr double kFilterWeight = 0.2;

Planning::Planning() {
    // 进行Task注册
    REGISTER_TASK(BehaviorGenerator);

    REGISTER_TASK(PathOptimizer);

    REGISTER_TASK(STGraphGenerator);

    REGISTER_TASK(SpeedPlanner);

    REGISTER_TASK(SpeedOptimizer);

    REGISTER_TASK(TrajectoryEvaluator);
}

Planning::~Planning() {
    // TODO
}

static FILE* g_log_file = nullptr;
void cleanup_logger() {
    if (g_log_file) {
        fclose(g_log_file);
        g_log_file = nullptr;
    }
}

void init_logger() {
    static FILE* log_handle = nullptr;

    static auto cleanup_log_files = []() {
        if (log_handle) {
            fflush(log_handle);
            fclose(log_handle);
            log_handle = nullptr;
        }
    };

    static auto minimalist_logger = [](const MLogMsg* msg, void* udata) {
        fprintf(stdout, "%s\n", msg->text);
        fflush(stdout);

        if (log_handle) {
            fprintf(log_handle, "%s\n", msg->text);
            fflush(log_handle);
        }
    };

    // a. 创建日志目录
    fs::path log_dir = "../data/log";
    try {
        if (!fs::exists(log_dir)) fs::create_directories(log_dir);
    } catch (const fs::filesystem_error& e) {
        fprintf(stderr, "[MLOG_INIT] Error creating log directory: %s\n",
                e.what());
    }

    // c. 生成公共的时间戳
    std::string time_stamp;
    {
        auto now = std::chrono::system_clock::now();
        auto in_time_t = std::chrono::system_clock::to_time_t(now);
        std::tm buf;
#ifdef _MSC_VER
        localtime_s(&buf, &in_time_t);
#else
        localtime_r(&in_time_t, &buf);
#endif
        std::stringstream ss;
        ss << std::put_time(&buf, "%Y-%m-%d_%H-%M-%S");
        time_stamp = ss.str();
    }

    fs::path log_path = log_dir / ("info_" + time_stamp + ".log");

    log_handle = fopen(log_path.string().c_str(), "a");
    if (!log_handle) {
        fprintf(stderr, "[MLOG_INIT] Failed to open log file: %s.\n",
                log_path.string().c_str());
    }

    // e. 注册清理和回调函数
    atexit(cleanup_log_files);
    mlog_set_logger(minimalist_logger, nullptr, nullptr);

    AINFO << "Logger initialized. All logs will be written to: "
          << fs::absolute(log_path).string();
}

std::vector<ArcAdsNavReferenceLinePoint> CreateStraightLine(float length,
                                                            float interval) {
    std::vector<ArcAdsNavReferenceLinePoint> line_points;

    // 假设直线沿x轴方向
    float x = 0.0;
    float y = 0.0;

    while (x <= length) {
        ArcAdsNavReferenceLinePoint pt;
        pt.x = x;
        pt.y = y;
        pt.distance_to_left_divider = 5.0;
        pt.distance_to_right_divider = 5.0;

        line_points.emplace_back(pt);
        x += interval;
    }

    return line_points;
}

// [TODO] 暂时构造输入，用于debug
bool update_fake_input(arcsoft::framework::Session* session) {
    // 外部输入
    auto& local_view = session->mutable_local_view();

    // ReferenceLine
    auto& reference_lines = session->mutable_reference_lines();
    ReferenceLine reference_line;
    reference_line.set_raw_points(CreateStraightLine(100.0, 0.25));
    reference_lines.Add(1, reference_line);

    //
    TrajectoryPoint planning_start_point;
    planning_start_point.x = 0.0;
    planning_start_point.y = 0.0;
    planning_start_point.v = 5.0;
    session->set_planning_start_point(planning_start_point);

    return true;
}

ArcAdsStatus Planning::Init() {
    // 日志初始化
    init_logger();

    // DLOG_NS::set_callback(
    //     [](const DLogMsg& msg, void* data) { printf("test: %s\n", msg.text);
    //     }, nullptr);

    // 跨帧数据
    session_ = std::make_shared<arcsoft::framework::Session>();

    // 参考线平滑
    reference_line_manager_ =
        std::make_shared<ReferenceLineManager>(session_.get());

    // 障碍物决策
    obstacle_manager_ = std::make_shared<ObstacleManager>(session_.get());

    // 变道意图决策
    lane_change_decider_ = std::make_shared<LaneChangeDecider>(session_.get());

    // 变道状态机
    scenario_state_machine_ =
        std::make_shared<ScenarioStateMachine>(session_.get());
    scenario_state_machine_->Init();

    auto_drive_mode_state_machine_ =
        std::make_shared<AutoDriveModeStateMachine>(session_.get());

    return ARCADS_STATUS_OK;
}

bool Planning::update_reference_lines(
    const ArcAdsNavReferenceLines* nav_reference_lines) {
    if (nav_reference_lines == nullptr) {
        AERROR << "Null nav_reference_lines pointer!";
        return false;
    }

    const uint32_t num_lines = nav_reference_lines->num_lines;
    if (num_lines == 0 || num_lines > ARCADS_NAV_REF_LINE_MAX_NUM) {
        AERROR << "Invalid num_lines: " << num_lines
               << ", max allowed: " << ARCADS_NAV_REF_LINE_MAX_NUM;
        return false;
    }
    AINFO << "Nav Reference Lines Num: " << num_lines;

    auto& reference_lines = session_->mutable_reference_lines();
    reference_lines.Clear();

    for (uint32_t i = 0; i < num_lines; ++i) {
        const auto& current_line = nav_reference_lines->lines[i];

        if (current_line.num_points == 0 ||
            current_line.num_points > ARCADS_NAV_REF_LINE_POINT_MAX_NUM) {
            AWARN << "Skipping line " << i
                  << " due to invalid num_points: " << current_line.num_points;
            continue;
        }

        // AINFO << " Navi_model reference line " << i
        //       << " num_points: " << current_line.num_points;

        std::vector<ArcAdsNavReferenceLinePoint> points_vec(
            current_line.points, current_line.points + current_line.num_points);

        ReferenceLine reference_line;
        reference_line.set_id(current_line.id);
        reference_line.set_is_on_route(current_line.is_highlight);
        reference_line.set_left_lane_id(current_line.left_line_id);
        reference_line.set_right_lane_id(current_line.right_line_id);
        reference_line.set_raw_points(points_vec);
        reference_lines.Add(current_line.id, reference_line);
    }

    auto& local_view = session_->mutable_local_view();
    local_view.nav_model_status = true;
    local_view.frame_id = nav_reference_lines->frame_id;
    local_view.time = nav_reference_lines->time;

    return true;
}

bool Planning::update_obstacles_info(
    const ArcAdsObjectMeasurements* objects,
    const ArcAdsMultiModalPrediction* multi_modal_prediction) {
    AINFO << "Obstalces Num: " << objects->num_objects;

    auto& local_view = session_->mutable_local_view();
    local_view.perception_obstacles.clear();
    std::vector<ArcAdsObjectMeasurement> obstacles_vec(
        objects->objects, objects->objects + objects->num_objects);
    local_view.perception_obstacles = obstacles_vec;

    const auto& modal_size = multi_modal_prediction->num_predictions;
    if (modal_size < ModalIndex) {
        AERROR << "Prediction modal size: " << modal_size << " is less than "
               << ModalIndex;
        return false;
    }

    // 使用单模态预测输出结果
    local_view.prediction_trajectories.clear();
    const auto& single_modal_prediction =
        multi_modal_prediction->predictions[ModalIndex];
    std::vector<ArcAdsObjectPrediction> prediction_trajectories_vec(
        single_modal_prediction.objects,
        single_modal_prediction.objects + single_modal_prediction.num_objects);
    local_view.prediction_trajectories = prediction_trajectories_vec;

    local_view.perception_obstacles_status = true;
    return true;
}

TrajectoryPoint Planning::trajectory_stitcher() {
    auto& local_view = session_->local_view();
    const auto& vehicle_chassis = local_view.vehicle_chassis;
    const auto& vehicle_state = local_view.vehicle_state;
    const double current_timestamp = arcsoft::common::get_system_time();
    const double planning_cycle_time = 0.1;  // 假设规划周期为100ms
    const size_t preserved_points_num = 20;  // 假设保留20个点
    bool replan_by_offset = true;

    // for (const auto& pt : last_trajectory_current_frame_) {
    //     AINFO << "last_trajectory_current_frame_ point: "
    //           << "x: " << pt.x << ", y: " << pt.y << ", heading: " <<
    //           pt.heading
    //           << " t: " << pt.t << ", s: " << pt.s << ", v: " << pt.v
    //           << ", a: " << pt.a;
    // }

    auto last_publishable_trajectory = PublishableTrajectory(
        last_trajectory_header_,
        DiscretizedTrajectory(last_trajectory_current_frame_));

    std::string replan_reason;
    const bool is_simulation_mode = session_->is_simulation_mode();
    stitching_trajectory_ = TrajectoryStitcher::ComputeStitchingTrajectory(
        vehicle_chassis, vehicle_state, current_timestamp, planning_cycle_time,
        preserved_points_num, replan_by_offset, &last_publishable_trajectory,
        is_simulation_mode, &replan_reason);

    AINFO << "best_trajectory_result size: "
          << session_->best_trajectory_result().size();

    // for (auto it = stitching_trajectory_.rbegin();
    //      it != stitching_trajectory_.rend(); ++it) {
    //     const auto& pt = *it;  // 获取当前 TrajectoryPoint
    //     AINFO << "stitching_trajectory point: "
    //           << "x: " << pt.x << ", y: " << pt.y << ", heading: " <<
    //           pt.heading
    //           << " t: " << pt.t << ", s: " << pt.s << ", v: " << pt.v
    //           << ", a: " << pt.a;
    // }

    AINFO << "replan_reason: " << replan_reason;
    return stitching_trajectory_.back();
}

bool Planning::update_ego_pose(const ArcAdsEgoMotionMeasurement* ego_motion) {
    // 更新vehicle_state
    auto& local_view = session_->mutable_local_view();
    const auto& vehicle_config =
        arcsoft::common::VehicleConfigHelper::Instance().GetVehicleParam();
    local_view.vehicle_state.global_pose = ego_motion->pose;
    local_view.vehicle_state.x = 0.0;
    local_view.vehicle_state.y = 0.0;
    local_view.vehicle_state.heading = 0.0;
    local_view.vehicle_state.kappa = 0.0;
    // 待传入车轮转角信息
    // local_view.vehicle_state.kappa =
    //     tan(local_view.vehicle_chassis.steer_angle) /
    //     vehicle_config.wheel_base;

    local_view.vehicle_state.linear_velocity = ego_motion->linear_velocity.x;
    local_view.vehicle_state.linear_acceleration =
        ego_motion->linear_acceleration.x;
    local_view.vehicle_state.angular_velocity = ego_motion->angular_velocity.x;

    double ego_center_x = local_view.vehicle_state.x +
                          vehicle_config.length / 2.0 -
                          vehicle_config.back_edge_to_center;
    double ego_center_y = local_view.vehicle_state.y;
    local_view.vehicle_state.box = arcsoft::common::Box2d(
        arcsoft::common::Vec2d(ego_center_x, ego_center_y),
        local_view.vehicle_state.heading, vehicle_config.length,
        vehicle_config.width);

    // 将上一帧轨迹转换为当前帧坐标系
    transform_trajectory(local_view.vehicle_state.global_pose);

    TrajectoryPoint planning_start_point = trajectory_stitcher();

    AINFO << "vehicle_chassis.lon_velocity: "
          << local_view.vehicle_chassis.lon_velocity
          << " vehicle_chassis.lon_acceleration: "
          << local_view.vehicle_chassis.lon_acceleration;

    // auto& data_manager = session_->data_manager();
    // data_manager.append_double_value(
    //     "raw_acc", local_view.vehicle_state.linear_acceleration);
    // data_manager.append_double_value("filtered_acc", planning_start_point.a);
    // data_manager.save();

    AINFO << " planning_start_point.x: " << planning_start_point.x
          << " y: " << planning_start_point.y
          << " heading: " << planning_start_point.heading
          << " v: " << planning_start_point.v
          << " a: " << planning_start_point.a;

    session_->set_planning_start_point(planning_start_point);
    last_ego_pose_ = local_view.vehicle_state.global_pose;

    local_view.localization_status = true;

    return true;
}

// 从 ArcAdsGlobalPose3d 构造 Eigen::Affine3d 变换矩阵
Eigen::Affine3d PoseToTransform(const ArcAdsGlobalPose3d& pose) {
    // 平移部分
    Eigen::Translation3d translation(pose.x, pose.y, pose.z);

    // 旋转部分 (Z-Y-X 欧拉角)
    Eigen::Quaterniond rotation =
        Eigen::AngleAxisd(pose.yaw, Eigen::Vector3d::UnitZ()) *  // Z 旋转 (yaw)
        Eigen::AngleAxisd(pose.pitch,
                          Eigen::Vector3d::UnitY()) *  // Y 旋转 (pitch)
        Eigen::AngleAxisd(pose.roll,
                          Eigen::Vector3d::UnitX());  // X 旋转 (roll)

    // 组合平移 + 旋转
    return translation * rotation;
}

// 计算当前帧到上一帧的相对变换
Eigen::Affine3d compute_relative_transform(
    const ArcAdsGlobalPose3d& current_pose,
    const ArcAdsGlobalPose3d& last_pose) {
    // 当前帧的逆变换 (world -> current)
    Eigen::Affine3d current_inv = PoseToTransform(current_pose).inverse();

    // 上一帧的变换 (world -> last)
    Eigen::Affine3d last = PoseToTransform(last_pose);

    // 相对变换: current <- last
    return current_inv * last;
}

void Planning::transform_trajectory(
    const ArcAdsGlobalPose3d& current_ego_pose) {
    if (session_ == nullptr) {
        return;
    }
    const auto& last_trajectory = session_->best_trajectory_result();

    if (last_trajectory.size() <= 0) {
        last_trajectory_header_ = arcsoft::common::get_system_time();
        return;
    }

    last_trajectory_current_frame_.clear();

    // 计算相对变换
    Eigen::Affine3d T_current_last =
        compute_relative_transform(current_ego_pose, last_ego_pose_);

    for (const TrajectoryPoint& last_point : last_trajectory) {
        TrajectoryPoint current_point;
        // --- 1. 变换 (x, y) ---
        Eigen::Vector3d last_pos(last_point.x, last_point.y, 0.0);
        Eigen::Vector3d current_pos = T_current_last * last_pos;
        current_point.x = current_pos.x();
        current_point.y = current_pos.y();

        // --- 2. 变换 heading ---
        // 上一帧的 heading 是相对于上一帧自车的，需加上 last_ego_pose.yaw
        // 得到全局 heading， 再减去 current_pose.yaw 得到当前帧的相对 heading
        double global_heading = last_ego_pose_.yaw + last_point.heading;
        current_point.heading = global_heading - current_ego_pose.yaw;
        // 归一化到 [-π, π]
        current_point.heading = std::atan2(std::sin(current_point.heading),
                                           std::cos(current_point.heading));

        // --- 3. 其他字段直接复制 ---
        current_point.curvature = last_point.curvature;
        current_point.t = last_point.t;
        current_point.v = last_point.v;
        current_point.a = last_point.a;
        current_point.jerk = last_point.jerk;
        current_point.s = last_point.s;
        current_point.l = last_point.l;
        current_point.frenet_valid = last_point.frenet_valid;

        last_trajectory_current_frame_.push_back(current_point);
    }
}

bool Planning::update_trajectory_info(
    ArcAdsPlanningTrajectory* out_trajectory) {
    const auto& best_path_result = session_->best_path_result();
    const auto& best_trajectory_result = session_->best_trajectory_result();
    const auto& planning_start_point = session_->planning_start_point();
    AINFO << "best_path_result size: " << best_path_result.size()
          << ", best_trajectory_result size: " << best_trajectory_result.size();

    if (best_path_result.size() <= 0 || best_trajectory_result.size() <= 0) {
        AERROR << "update trajectory info is failed! ";
        last_trajectory_current_frame_.clear();
        return false;
    }

    // 转换路径点
    uint32_t num_path_points = static_cast<uint32_t>(best_path_result.size());
    out_trajectory->num_path_points = num_path_points;

    for (uint32_t i = 0; i < num_path_points; i++) {
        const auto& path_point = best_path_result[i];
        out_trajectory->path_points[i].x = static_cast<float>(path_point.x);
        out_trajectory->path_points[i].y = static_cast<float>(path_point.y);
        out_trajectory->path_points[i].s = static_cast<float>(path_point.s);
        out_trajectory->path_points[i].yaw =
            static_cast<float>(path_point.heading);
        out_trajectory->path_points[i].curvature =
            static_cast<float>(path_point.curvature);
    }

    // 转换轨迹点
    uint32_t num_trajectory_points =
        static_cast<uint32_t>(best_trajectory_result.size());
    out_trajectory->num_trajectory_points = num_trajectory_points;
    DLOG_ARRAY_BEGIN("planning_trajectory");
    for (uint32_t i = 0; i < num_trajectory_points; ++i) {
        const auto& trajectory_point = best_trajectory_result[i];

        out_trajectory->trajectory_points[i].path_point.x =
            static_cast<float>(trajectory_point.x);
        out_trajectory->trajectory_points[i].path_point.y =
            static_cast<float>(trajectory_point.y);
        out_trajectory->trajectory_points[i].path_point.s =
            static_cast<float>(trajectory_point.s);
        out_trajectory->trajectory_points[i].path_point.yaw =
            static_cast<float>(trajectory_point.heading);
        out_trajectory->trajectory_points[i].path_point.curvature =
            static_cast<float>(trajectory_point.curvature);
        out_trajectory->trajectory_points[i].t =
            static_cast<float>(trajectory_point.t);
        out_trajectory->trajectory_points[i].velocity =
            static_cast<float>(trajectory_point.v);
        out_trajectory->trajectory_points[i].acceleration =
            static_cast<float>(trajectory_point.a);
        if (trajectory_point.t <= 1.0 && trajectory_point.t >= -0.2) {
            AINFO << " t: " << trajectory_point.t
                  << " trajectory result x: " << trajectory_point.x
                  << " y: " << trajectory_point.y
                  << " heading: " << trajectory_point.heading
                  << " kappa: " << trajectory_point.curvature
                  << " v: " << trajectory_point.v
                  << " a: " << trajectory_point.a;
        }
        DLOG_DICT_BEGIN();
        DLOG_DICT_ITEM("x", trajectory_point.x);
        DLOG_DICT_ITEM("y", trajectory_point.y);
        DLOG_DICT_ITEM("s", trajectory_point.s);
        DLOG_DICT_ITEM("theta", trajectory_point.heading);
        DLOG_DICT_ITEM("kappa", trajectory_point.curvature);
        DLOG_DICT_ITEM("t", trajectory_point.t);
        DLOG_DICT_ITEM("v", trajectory_point.v);
        DLOG_DICT_ITEM("a", trajectory_point.a);
        DLOG_DICT_END();

        // AINFO << "trajectory result x: " << trajectory_point.x
        //       << " y: " << trajectory_point.y << " t: " <<
        //       trajectory_point.t
        //       << " heading: " << trajectory_point.heading
        //       << " kappa: " << trajectory_point.curvature
        //       << " v: " << trajectory_point.v << " a: " <<
        //       trajectory_point.a;
        // AINFO << "current_frame trajectory result s: " <<
        // trajectory_point.s
        //       << " t: " << trajectory_point.t << " v: " <<
        //       trajectory_point.v
        //       << " a: " << trajectory_point.a;
        if (trajectory_point.t > 0.29 && trajectory_point.t < 0.31) {
            AINFO << "[Planning Speed Debug]"
                  << " planning_start_point.v: " << planning_start_point.v
                  << " a: " << planning_start_point.a
                  << " vel_03s: " << trajectory_point.v
                  << " acc_03s: " << trajectory_point.a;
        }
    }
    DLOG_ARRAY_END("planning_trajectory");

    last_trajectory_current_frame_ = best_trajectory_result;

    return true;
}

ArcAdsStatus Planning::LoadAutoDriveMode(
    const ArcAdsAutoDriveMode auto_drive_mode) {
    auto& local_view = session_->mutable_local_view();

    switch (auto_drive_mode) {
        case ArcAdsAutoDriveMode::ARCADS_AUTO_DRIVE_MODE_INVALID:
        case ArcAdsAutoDriveMode::ARCADS_AUTO_DRIVE_MODE_OFF:
            local_view.vehicle_state.driving_mode = AutoDriveModeState::OFF;
            break;

        case ArcAdsAutoDriveMode::ARCADS_AUTO_DRIVE_MODE_ACC:
            local_view.vehicle_state.driving_mode = AutoDriveModeState::ACC;
            break;

        case ArcAdsAutoDriveMode::ARCADS_AUTO_DRIVE_MODE_ACC_LCC:
            local_view.vehicle_state.driving_mode = AutoDriveModeState::LCC;
            break;

        case ArcAdsAutoDriveMode::ARCADS_AUTO_DRIVE_MODE_NOA:
            local_view.vehicle_state.driving_mode = AutoDriveModeState::NOA;
            break;

        default:
            break;
    }
    AINFO << "vehicle_state_driving_mode: "
          << state_to_string(local_view.vehicle_state.driving_mode);

    return ARCADS_STATUS_OK;
}

ArcAdsStatus Planning::LoadHMISpeedLimit(const float speed_limit) {
    auto& local_view = session_->mutable_local_view();
    local_view.vehicle_chassis.hmi_speed_limit =
        static_cast<double>(speed_limit) / 3.6;
    AINFO << "vehicle_chassis_hmi_speed_limit: "
          << local_view.vehicle_chassis.hmi_speed_limit;
    return ARCADS_STATUS_OK;
}

ArcAdsStatus Planning::LoadHMITimeGap(const float time_gap) {
    auto& local_view = session_->mutable_local_view();
    local_view.vehicle_chassis.hmi_time_gap = static_cast<double>(time_gap);
    AINFO << "vehicle_chassis_hmi_time_gap: "
          << local_view.vehicle_chassis.hmi_time_gap;
    return ARCADS_STATUS_OK;
}

ArcAdsVehicleLightFlag Planning::UpdateTurnSignal(
    const ArcAdsVehicleLightFlags& light_flag_raw) {
    if (light_flag_queue_.size() >= MAX_QUEUE_SIZE_) {
        light_flag_queue_.pop_front();
    }
    light_flag_queue_.push_back(light_flag_raw);

    bool has_left = false;
    bool has_right = false;

    for (int flag : light_flag_queue_) {
        if (flag ==
            ArcAdsVehicleLightFlag::ARCADS_VEHICLE_LIGHT_FLAG_LEFT_TURN) {
            has_left = true;
            break;
        } else if (flag == ArcAdsVehicleLightFlag::
                               ARCADS_VEHICLE_LIGHT_FLAG_RIGHT_TURN) {
            has_right = true;
            break;
        }
    }

    if (has_right)
        return ArcAdsVehicleLightFlag::ARCADS_VEHICLE_LIGHT_FLAG_RIGHT_TURN;
    else if (has_left)
        return ArcAdsVehicleLightFlag::ARCADS_VEHICLE_LIGHT_FLAG_LEFT_TURN;
    else
        return ArcAdsVehicleLightFlag::ARCADS_VEHICLE_LIGHT_FLAG_NONE;
}

// ArcAdsStatus Planning::LoadEgoMotion(
//     const ArcAdsEgoMotionMeasurement* ego_motion) {
//     // 更新vehicle_state
//     auto& local_view = session_->mutable_local_view();
//     local_view.ClearVehicleState();
//     const auto& vehicle_config =
//         arcsoft::common::VehicleConfigHelper::Instance().GetVehicleParam();
//     local_view.vehicle_state.global_pose = ego_motion->pose;
//     local_view.vehicle_state.x = 0.0;
//     local_view.vehicle_state.y = 0.0;
//     local_view.vehicle_state.heading = 0.0;
//     local_view.vehicle_state.kappa = 0.0;
//     // 待传入车轮转角信息
//     // local_view.vehicle_state.kappa =
//     //     tan(local_view.vehicle_chassis.steer_angle) /
//     //     vehicle_config.wheel_base;

//     local_view.vehicle_state.linear_velocity = ego_motion->linear_velocity.x;
//     local_view.vehicle_state.linear_acceleration =
//         ego_motion->linear_acceleration.x;
//     local_view.vehicle_state.angular_velocity =
//     ego_motion->angular_velocity.x;

//     local_view.localization_status = true;

//     return ARCADS_STATUS_OK;
// }

ArcAdsStatus Planning::LoadVehicleCanSignal(
    const ArcAdsVehicleCanSignal* vehicle_can_signal) {
    auto& local_view = session_->mutable_local_view();
    // local_view.ClearVehicleChassis();

    // 序列保持 0.5s 50个数据
    const auto& light_flag = UpdateTurnSignal(vehicle_can_signal->light_flags);

    // 1. 转向灯信号
    if (light_flag == ArcAdsVehicleLightFlag::ARCADS_VEHICLE_LIGHT_FLAG_NONE) {
        local_view.vehicle_chassis.vehicle_light_flag = VehicleLightFLAG::OFF;
    } else if (light_flag ==
               ArcAdsVehicleLightFlag::ARCADS_VEHICLE_LIGHT_FLAG_LEFT_TURN) {
        local_view.vehicle_chassis.vehicle_light_flag =
            VehicleLightFLAG::LEFT_TURN;
    } else if (light_flag ==
               ArcAdsVehicleLightFlag::ARCADS_VEHICLE_LIGHT_FLAG_RIGHT_TURN) {
        local_view.vehicle_chassis.vehicle_light_flag =
            VehicleLightFLAG::RIGHT_TURN;
    }
    AINFO << "local_view.vehicle_chassis.vehicle_light_flag: "
          << local_view.vehicle_chassis.vehicle_light_flag;

    // 2. 档位
    switch (vehicle_can_signal->gear) {
        case ArcAdsVehicleGear::ARCADS_VEHICLE_GEAR_N:
            local_view.vehicle_chassis.vehicle_gear = VehicleGear::GEAR_N;
            break;
        case ArcAdsVehicleGear::ARCADS_VEHICLE_GEAR_D:
            local_view.vehicle_chassis.vehicle_gear = VehicleGear::GEAR_D;
            break;
        case ArcAdsVehicleGear::ARCADS_VEHICLE_GEAR_P:
            local_view.vehicle_chassis.vehicle_gear = VehicleGear::GEAR_P;
            break;
        case ArcAdsVehicleGear::ARCADS_VEHICLE_GEAR_R:
            local_view.vehicle_chassis.vehicle_gear = VehicleGear::GEAR_R;
            break;

        default:
            break;
    }

    // 3. 转角
    local_view.vehicle_chassis.steer_angle =
        vehicle_can_signal->steering.angle * (M_PI / 180.0);

    local_view.vehicle_chassis.lon_velocity =
        vehicle_can_signal->wheel.speed / 3.6;
    local_view.vehicle_chassis.lon_acceleration =
        vehicle_can_signal->imu.lng_accel;

    return ARCADS_STATUS_OK;
}

ArcAdsStatus Planning::Process(
    ArcAdsPlanningTrajectory* out_trajectory,
    const ArcAdsEgoMotionMeasurement* ego_motion,
    const ArcAdsObjectMeasurements* objects,
    const ArcAdsLocalMapMeasurementV2* local_map,
    const ArcAdsTrafficLightLogicalAssembly* traffic_light_logical_assembly,
    const ArcAdsSpeedLimits* speed_limits,
    const ArcAdsNavReferenceLines* nav_reference_lines,
    const ArcAdsMultiModalPrediction* multi_modal_prediction) {
    std::fill_n(reinterpret_cast<char*>(out_trajectory),
                sizeof(ArcAdsPlanningTrajectory), 0);
    out_trajectory->auto_drive_mode =
        ArcAdsAutoDriveMode::ARCADS_AUTO_DRIVE_MODE_OFF;

    auto start_time = std::chrono::system_clock::now();

    auto& data_manager = session_->data_manager();
    data_manager.Clear();

    AINFO << "---------------------Planning "
             "START--------------------------------";
    AINFO << "[sz add] frame_id: " << nav_reference_lines->frame_id;
    AINFO << "is_simulation_mode: " << session_->is_simulation_mode();
    DLOG_DICT_BEGIN("planning", session_->local_view().frame_id,
                    session_->local_view().time);

    session_->set_planning_status(
        arcsoft::planning::PlanningStatus::NOT_INITIALIZED);

    // 清空历史输入信息
    auto& local_view = session_->mutable_local_view();
    local_view.ClearOtherFields();
    AINFO << "vehicle_state_driving_mode: "
          << state_to_string(local_view.vehicle_state.driving_mode);

    // 更新自车定位信息(轨迹拼接)
    if (!update_ego_pose(ego_motion)) {
        last_trajectory_current_frame_.clear();
        AERROR << "Ego Pose Update Failed! ";
        return ARCADS_STATUS_OK;
    }

    // 更新Nav_model输入
    if (!update_reference_lines(nav_reference_lines)) {
        AERROR << "Nav Reference Line Update Failed! ";
        return ARCADS_STATUS_OK;
    };

    // 更新感知及预测输入
    if (!update_obstacles_info(objects, multi_modal_prediction)) {
        AERROR << "Obstacles and Prediction Trajectory Update Failed! ";
        return ARCADS_STATUS_OK;
    }

    session_->cnt_ = cnt_;

    // Planning 算法部分
    auto start_time1 = std::chrono::system_clock::now();
    if (!reference_line_manager_->Process()) {
        AERROR << "Reference Line Manager Failed! ";
        last_trajectory_current_frame_.clear();
        return ARCADS_STATUS_OK;
    }
    auto end_time1 = std::chrono::system_clock::now();
    std::chrono::duration<double> diff1 = end_time1 - start_time1;
    AINFO << "Reference Line Manager used time: " << diff1.count() * 1000
          << " ms.";

    auto start_time2 = std::chrono::system_clock::now();
    if (!obstacle_manager_->Process()) {
        AERROR << "Obstacle Manager Failed! ";
        last_trajectory_current_frame_.clear();
        return ARCADS_STATUS_OK;
    }
    auto end_time2 = std::chrono::system_clock::now();
    std::chrono::duration<double> diff2 = end_time2 - start_time2;
    AINFO << "Obstacle Manager used time: " << diff2.count() * 1000 << " ms.";

    auto start_time3 = std::chrono::system_clock::now();
    if (!lane_change_decider_->Process()) {
        AERROR << "Lane Change Decider Failed! ";
        last_trajectory_current_frame_.clear();
        return ARCADS_STATUS_OK;
    }
    AINFO << "best_trigger_decision(): "
          << session_->best_trigger_decision().request_type;

    auto end_time3 = std::chrono::system_clock::now();
    std::chrono::duration<double> diff3 = end_time3 - start_time3;
    AINFO << "Obstacle Manager used time: " << diff3.count() * 1000 << " ms.";

    scenario_state_machine_->Process();
    if (session_->planning_status() !=
        arcsoft::planning::PlanningStatus::SUCCESS) {
        AERROR << "Task Pipeline failed! No Trajectory Generated! ";
        AINFO << "Planning failed, clearing last trajectory to prevent "
                 "incorrect stitching.";
        last_trajectory_current_frame_.clear();
        data_manager.save();

        return false;
    }

    // 智驾模式切换状态机
    auto_drive_mode_state_machine_->Process();

    // 可视化数据存储
    data_manager.save();
    session_->append_stitching_trajectory(stitching_trajectory_);

    // 更新当前帧规划数据
    update_trajectory_info(out_trajectory);
    // for (uint32_t i = 0; i < out_trajectory->num_path_points; ++i) {
    //     AINFO << "current_frame path point: "
    //           << "x: " << out_trajectory->path_points[i].x
    //           << ", y: " << out_trajectory->path_points[i].y
    //           << ", s: " << out_trajectory->path_points[i].s
    //           << ", yaw: " << out_trajectory->path_points[i].yaw
    //           << ", curvature: " <<
    //           out_trajectory->path_points[i].curvature;
    // }

    // 设置auto_drive_mode
    const auto& auto_drive_mode_state = session_->auto_drive_mode_state();
    out_trajectory->auto_drive_mode =
        ArcAdsAutoDriveMode::ARCADS_AUTO_DRIVE_MODE_NOA;

    // 设置 scenario_state
    const auto& scenario_state = session_->scenario_state_info();
    if (ScenarioStateEnum::CRUISE_WAIT ==
        scenario_state.target_scenario_state) {
        out_trajectory->scenario_state = ArcAdsPlanningScenarioState::
            ARCADS_PLANNING_SCENARIO_STATE_LANE_CHANGE_WAIT;
    } else if (ScenarioStateEnum::CRUISE_CHANGE ==
               scenario_state.target_scenario_state) {
        out_trajectory->scenario_state = ArcAdsPlanningScenarioState::
            ARCADS_PLANNING_SCENARIO_STATE_LANE_CHANGING;
    } else {
        out_trajectory->scenario_state =
            ArcAdsPlanningScenarioState::ARCADS_PLANNING_SCENARIO_STATE_CRUISE;
    }
    AINFO << "out_trajectory->scenario_state: "
          << static_cast<int>(out_trajectory->scenario_state);

    last_trajectory_header_ = arcsoft::common::get_system_time();

    auto end_time = std::chrono::system_clock::now();
    std::chrono::duration<double> diff = end_time - start_time;
    AINFO << "Planning Module used time: " << diff.count() * 1000 << " ms.";
    DLOG_DICT_END("planning");
    return ARCADS_STATUS_OK;
}

ArcAdsStatus Planning::Reset() {
    // TODO
    return ARCADS_STATUS_OK;
}

}  // namespace pln
}  // namespace ads
