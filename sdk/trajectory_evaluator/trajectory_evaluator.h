/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __TRAJECTORY_EVALUATOR_H__
#define __TRAJECTORY_EVALUATOR_H__

#include "common/basic_types.h"
#include "framework/task.h"

namespace arcsoft {
namespace planning {

class TrajectoryEvaluator : public framework::Task {
public:
    TrajectoryEvaluator(const std::string& name, framework::Session* session)
        : Task(name, session){};
    ~TrajectoryEvaluator() = default;

    void Init() override;
    bool Process(const size_t candidate_transition_context_id) override;

private:
    bool combine_path_and_speed_data(
        const std::vector<PathData>& path_data_vec,
        const std::vector<SpeedData>& speed_data_vec,
        std::vector<TrajectoryPoints>& trajectory_data_vec);
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __TRAJECTORY_EVALUATOR_H__ */