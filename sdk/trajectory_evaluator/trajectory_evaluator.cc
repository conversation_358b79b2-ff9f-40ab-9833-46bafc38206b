/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "trajectory_evaluator.h"

#include "common/discretized_path.h"

namespace arcsoft {
namespace planning {

constexpr double kTimeResolution = 0.02;
constexpr double kTotalTimePeriod = 4.0;

void TrajectoryEvaluator::Init() {}

bool TrajectoryEvaluator::Process(
    const size_t candidate_transition_context_id) {
    // // 计算该context id下所有轨迹的score，并得到最小的cost
    // int cnt = session_->cnt_;
    // double curr_score = std::numeric_limits<double>::max();
    // if (cnt == 6) {
    //     curr_score = 0.0;
    // }

    // if (curr_score < session_->min_trajectory_cost()) {
    //     // [TODO] 将1改为candidate_transition_context_id
    //     session_->set_best_transition_context_id(1);
    // }

    // 融合path和speed信息，生成trajectory
    std::vector<TrajectoryPoints> trajectory_data_vec;
    const auto* path_data_vec =
        session_->path_data_vec(candidate_transition_context_id);
    const auto* speed_data_vec =
        session_->speed_data_optimizer_vec(candidate_transition_context_id);

    if (path_data_vec == nullptr || speed_data_vec == nullptr ||
        (path_data_vec->size() != speed_data_vec->size()) ||
        path_data_vec->size() <= 0) {
        AERROR << "Combine path and speed data failed, with "
                  "candidate_transition_context_id: "
               << candidate_transition_context_id;
        session_->set_best_path_result({});
        session_->set_best_trajectory_result({});
        session_->set_planning_status(
            PlanningStatus::TRAJECTORY_EVALUATOR_FAILED);
        session_->set_best_transition_context_id(0);
        return false;
    }

    combine_path_and_speed_data(*path_data_vec, *speed_data_vec,
                                trajectory_data_vec);

    // 计算每条trajectory score
    // [TODO] 目前仅有一条，因此不计算score，直接取front
    session_->set_best_path_result(path_data_vec->front());
    session_->set_best_trajectory_result(trajectory_data_vec.front());
    session_->set_planning_status(PlanningStatus::SUCCESS);

    // [TODO] 计算score后进行context_id赋值
    session_->set_best_transition_context_id(0);
    return true;
}

// 4s轨迹，0.02s一个点
bool TrajectoryEvaluator::combine_path_and_speed_data(
    const std::vector<PathData>& path_data_vec,
    const std::vector<SpeedData>& speed_data_vec,
    std::vector<TrajectoryPoints>& trajectory_data_vec) {
    const size_t trajectory_size = path_data_vec.size();
    const double kDenseTimeResolution = 0.1;
    for (size_t i = 0; i < trajectory_size; ++i) {
        const PathData& path_data = path_data_vec[i];
        const SpeedData& speed_data = speed_data_vec[i];
        TrajectoryPoints trajectory_points;
        const double time_horizon =
            speed_data.TotalTime() + kDenseTimeResolution * 1.0e-6;
        for (double curr_time = 0.0; curr_time < time_horizon;
             curr_time += kTimeResolution) {
            SpeedPoint speed_point;
            speed_data.EvaluateByTime(curr_time, &speed_point);
            const auto& discretized_path = DiscretizedPath(path_data);
            if (speed_point.s > discretized_path.Length()) {
                break;
            }

            PathPoint path_point = discretized_path.Evaluate(speed_point.s);

            //
            TrajectoryPoint trajectory_point;
            trajectory_point.x = path_point.x;
            trajectory_point.y = path_point.y;
            trajectory_point.heading = path_point.heading;
            trajectory_point.curvature = path_point.curvature;
            trajectory_point.s = speed_point.s;
            trajectory_point.v = speed_point.v;
            trajectory_point.a = speed_point.a;
            trajectory_point.t = speed_point.t;
            trajectory_points.emplace_back(trajectory_point);
        }

        trajectory_data_vec.emplace_back(trajectory_points);
    }

    return true;
}

}  // namespace planning

}  // namespace arcsoft