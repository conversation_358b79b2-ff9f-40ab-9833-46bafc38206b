/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "behavior_generator.h"

namespace arcsoft {
namespace planning {

// 参数设置
constexpr double kVelMaxThreshold = 27.7;  // 100km/h
constexpr double kAccMaxThreshold = 1.0;
constexpr double kVelCoef = 13.0;
constexpr double kAccCoef = 5.0;
constexpr double kJerkAccelMaxPositive = 0.6;
constexpr double kJerkAccelMaxNegative = 2.0;
constexpr double kJerkDecelMaxPositive = -3.0;
constexpr double kJerkDecelMaxNegative = -1.5;

void BehaviorGenerator::Init() {}

void BehaviorGenerator::set_ego_state(
    const TrajectoryPoint &planning_start_point,
    const FrenetCoordinateSystem *frenet_coordinate) {
    // 1. set vehicle param
    const auto &vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();

    // 2. set ego state
    AINFO << "ego state x: " << planning_start_point.x
          << " y: " << planning_start_point.y
          << " heading: " << planning_start_point.heading
          << " curvature: " << planning_start_point.curvature
          << " v: " << planning_start_point.v
          << " a: " << planning_start_point.a;
    ego_pose_.state.x = planning_start_point.x;
    ego_pose_.state.y = planning_start_point.y;
    ego_pose_.state.heading = planning_start_point.heading;
    ego_pose_.state.curvature = planning_start_point.curvature;

    const double front_wheel_angle_init =
        std::atan(planning_start_point.curvature * vehicle_config.wheel_base);
    ego_pose_.state.steer = front_wheel_angle_init;
    ego_pose_.state.steer_rate = 0.0;
    ego_pose_.state.v = planning_start_point.v;
    ego_pose_.state.acc = planning_start_point.a;

    // 3. set ego param
    ego_pose_.param.wheel_base = vehicle_config.wheel_base;
    ego_pose_.param.preview_time = 2.0;
    ego_pose_.param.look_ahead_distance_min = 3.0;

    constexpr double kVelocityLower = 18.0;
    constexpr double kVelocityUpper = 23.0;
    constexpr double kMaxPreviewDistanceLower = 10.0;
    constexpr double kMaxPreviewDistanceUpper = 20.0;
    constexpr double kGradient =
        (kMaxPreviewDistanceUpper - kMaxPreviewDistanceLower) /
        (kVelocityUpper - kVelocityLower);
    const double max_preview_distance = math::clamp(
        kGradient * (std::fabs(ego_pose_.state.v) - kVelocityLower) +
            kMaxPreviewDistanceLower,
        kMaxPreviewDistanceLower, kMaxPreviewDistanceUpper);
    ego_pose_.param.look_ahead_distance_max = max_preview_distance;

    // 4. update transform
    (void)frenet_coordinate->CartCoord2FrenetCoord(
        {ego_pose_.state.x, ego_pose_.state.y}, ego_frenet_);

    return;
}

// std::vector<std::pair<double, double>> BehaviorGenerator::plan_lateral(
//     const double target_l) {
//     // AINFO << "!!!!target_l: " << target_l;
//     // 创建Ruckig横向规划器
//     Ruckig<1> otg_d(0.2);
//     InputParameter<1> input_d;
//     OutputParameter<1> output_d;

//     // 设置初始状态
//     input_d.current_position = {ego_frenet_.l};
//     input_d.current_velocity = {0.0};
//     input_d.current_acceleration = {0.0};

//     // 设置目标状态
//     input_d.target_position = {target_l};
//     input_d.target_velocity = {0.0};
//     input_d.target_acceleration = {0.0};

//     // 设置约束
//     input_d.max_velocity = {1.2};
//     input_d.max_acceleration = {0.6};
//     input_d.max_jerk = {0.4};

//     // 存储结果
//     std::vector<std::pair<double, double>> traj_d;
//     double time = 0.0;

//     // 生成轨迹
//     while (time < 8.0 && otg_d.update(input_d, output_d) == Result::Working)
//     {
//         traj_d.emplace_back(std::make_pair(time, output_d.new_position[0]));
//         // AINFO << "!!!!Lateral Traj!!!! l: " << output_d.new_position[0];
//         //  更新状态
//         output_d.pass_to_input(input_d);
//         time += 0.2;
//     }

//     return traj_d;
// }

// std::vector<LongitudinalPoint> BehaviorGenerator::plan_longitudinal(
//     const double target_s, const double target_v) {
//     Ruckig<1> otg_s(0.2);
//     InputParameter<1> input_s;
//     input_s.degrees_of_freedom = 1;
//     OutputParameter<1> output_s;

//     input_s.current_position = {ego_frenet_.s};
//     input_s.current_velocity = {ego_pose_.state.v};
//     input_s.current_acceleration = {ego_pose_.state.acc};
//     // AINFO << "input_s.current_position: " <<
//     join(input_s.current_position)
//     //       << " input_s.current_velocity: " <<
//     join(input_s.current_velocity)
//     //       << " input_s.current_acceleration: "
//     //       << join(input_s.current_acceleration);

//     input_s.target_position = {target_s};
//     input_s.target_velocity = {target_v};
//     input_s.target_acceleration = {0.0};
//     // AINFO << "input_s.target_position: " << join(input_s.target_position)
//     //       << " input_s.target_velocity: " << join(input_s.target_velocity)
//     //       << " input_s.target_acceleration: "
//     //       << join(input_s.target_acceleration);

//     input_s.max_velocity = {28.0};
//     input_s.max_acceleration = {2.0};
//     input_s.max_jerk = {1.0};

//     std::vector<LongitudinalPoint> traj_s;
//     double time = 0.0;

//     while (time < 8.0 && otg_s.update(input_s, output_s) == Result::Working)
//     {
//         traj_s.emplace_back(time, output_s.new_position[0],
//                             output_s.new_velocity[0],
//                             output_s.new_acceleration[0],
//                             output_s.new_jerk[0]);

//         // AINFO << "Longitudinal Traj s: " << output_s.new_position[0]
//         //       << " v: " << output_s.new_velocity[0]
//         //       << " a: " << output_s.new_acceleration[0]
//         //       << " j: " << output_s.new_jerk[0];
//         output_s.pass_to_input(input_s);
//         time += 0.2;
//     }

//     return traj_s;
// }

// void BehaviorGenerator::TestRuckig(const FrenetCoordinateSystem
// *frenet_coord,
//                                    const double target_s, const double
//                                    target_l, const double target_v) {
//     auto lateral_traj = plan_lateral(target_l);

//     auto longitudinal_traj = plan_longitudinal(target_s, target_v);

//     AINFO << "t | s | l | v | a | j";
//     TrajectoryPoints ruckig_traj{};
//     for (size_t i = 0;
//          i < std::min(lateral_traj.size(), longitudinal_traj.size()); ++i) {
//         SLPoint desire_sl{longitudinal_traj[i].s, lateral_traj[i].second};
//         Point2D desire_cart{};
//         frenet_coord->FrenetCoord2CartCoord(desire_sl, desire_cart);
//         TrajectoryPoint traj_pt;
//         traj_pt.x = desire_cart.x;
//         traj_pt.y = desire_cart.y;
//         traj_pt.t = lateral_traj[i].first;
//         AINFO << traj_pt.t << " | " << desire_sl.s << " | " << desire_sl.l
//               << " | " << longitudinal_traj[i].v << " | "
//               << longitudinal_traj[i].a << " | " << longitudinal_traj[i].j;

//         ruckig_traj.emplace_back(traj_pt);
//     }

//     auto &data_manager = session_->data_manager();
//     data_manager.store_array("ruckig_traj", ruckig_traj);

//     return;
// }

// 功能：接收多个behavior candidate，为每个生成一条粗轨迹
bool BehaviorGenerator::Process(const size_t candidate_transition_context_id) {
    // 获取当前的状态
    const auto &candidate_transition_context =
        session_->candidate_transition_context(candidate_transition_context_id);

    // 根据当前状态选择reference_line
    AINFO << "current_lane_id: " << session_->current_lane()->id();
    const FrenetCoordinateSystem *frenet_coord =
        (candidate_transition_context.target_state !=
         ScenarioStateEnum::CRUISE_CHANGE)
            ? session_->current_lane()->frenet_coord()
            : session_->target_lane()->frenet_coord();

    if (frenet_coord == nullptr) {
        return false;
    }
    //
    const auto &planning_start_point = session_->planning_start_point();
    set_ego_state(planning_start_point, frenet_coord);

    // [temp] Fake input
    const double target_speed = 5.0;

    BehaviorCandidate behavior_candidate;
    constexpr double kNominalLateralSpeed = 0.4;
    constexpr double kNominalLonDistance = 10.0;
    behavior_candidate.target_s = std::fabs(ego_frenet_.l) *
                                      planning_start_point.v /
                                      kNominalLateralSpeed +
                                  kNominalLonDistance;
    AINFO << " behavior_candidate.target_s: " << behavior_candidate.target_s;

    behavior_candidate.target_l = 0.0;

    // // Method 1: Ruckig
    // auto start_1 = std::chrono::high_resolution_clock::now();
    // TestRuckig(frenet_coord, behavior_candidate.target_s,
    //            behavior_candidate.target_l, target_speed);
    // auto end_1 = std::chrono::high_resolution_clock::now();
    // auto duration1 =
    //     std::chrono::duration_cast<std::chrono::microseconds>(end_1 -
    //     start_1)
    //         .count();

    // AINFO << "Ruckig 函数耗时: " << duration1 << " 微秒";

    // Method 2: Forward Simulation
    auto start_2 = std::chrono::high_resolution_clock::now();
    generate_behavior(frenet_coord, target_speed, behavior_candidate);
    auto end_2 = std::chrono::high_resolution_clock::now();
    auto duration2 =
        std::chrono::duration_cast<std::chrono::microseconds>(end_2 - start_2)
            .count();

    AINFO << "Forward 函数耗时: " << duration2 << " 微秒";

    // [TODO] 给当前状态设置对应index的behavior_generator结果进session中

    return true;
}

// 仅生成粗轨迹，无bound_info
// 输出：behavior_candidate
bool BehaviorGenerator::generate_behavior(
    const FrenetCoordinateSystem *frenet_coord, const double target_speed,
    BehaviorCandidate &behavior_candidate) {
    // behavior下内容
    // target_speed
    // st_samples
    // t_step
    sample_st_array(target_speed, behavior_candidate);

    // behavior下内容
    // target_points
    if (!simulate_behavior(frenet_coord, behavior_candidate.target_s,
                           behavior_candidate)) {
        behavior_candidate.valid = false;
        return false;
    }

    if (!sample_trajectory(ego_frenet_.s, behavior_candidate)) {
        behavior_candidate.valid = false;
        return behavior_candidate.valid;
    }

    behavior_candidate.valid = !behavior_candidate.target_points.empty() &&
                               !behavior_candidate.trajectory.empty();

    return behavior_candidate.valid;
}

bool BehaviorGenerator::sample_trajectory(const double frenet_s0,
                                          BehaviorCandidate &behavior) {
    if (behavior.st_samples.s.empty()) {
        return false;
    }
    behavior.trajectory.clear();
    behavior.trajectory.reserve(behavior.st_samples.s.size());
    int heuristic = 0;
    int st_index = 0;
    for (const double s : behavior.st_samples.s) {
        BehaviorPoint point_interp;
        if (!fast_interp(behavior.target_points, s + frenet_s0, point_interp,
                         heuristic)) {
            return false;
        }
        point_interp.s = s;
        point_interp.frenet_valid = true;
        point_interp.vel =
            behavior.st_samples.v.at(static_cast<unsigned long>(st_index));
        point_interp.a =
            behavior.st_samples.a.at(static_cast<unsigned long>(st_index));
        st_index++;
        behavior.trajectory.emplace_back(point_interp);
        // AINFO << "interpolate trajectory point " << st_index
        //       << " s: " << point_interp.s << " l: " << point_interp.l
        //       << " x: " << point_interp.x << " y: " << point_interp.y
        //       << " heading: " << point_interp.heading
        //       << " curvature: " << point_interp.curvature
        //       << " v: " << point_interp.vel << " a: " << point_interp.a;
    }
    return true;
}

bool BehaviorGenerator::fast_interp(
    const std::vector<BehaviorPoint> &target_points, const double s_curr,
    BehaviorPoint &point_interp, int &heuristic) {
    if (target_points.empty()) {
        return false;
    }
    const BehaviorPoint *target_point_start;
    bool exceed_tp_range = false;
    if (s_curr >= target_points.back().s) {
        target_point_start = &target_points.back();
        heuristic = static_cast<int>(target_points.size() - 1U);
        exceed_tp_range = true;
    } else {
        auto cmp = [](const double b, const BehaviorPoint &a) {
            return b < a.s;
        };
        int tp_index = static_cast<int>(
            std::upper_bound(target_points.begin() + heuristic,
                             target_points.end(), s_curr, cmp) -
            target_points.begin());
        if (tp_index < 1) {
            return false;
        }
        heuristic = tp_index - 1;
        target_point_start =
            &target_points.at(static_cast<unsigned long>(heuristic));
    }
    if ((target_point_start == nullptr) ||
        (target_point_start->c_poly.size() < 4U)) {
        return false;
    }
    if (!exceed_tp_range && (target_point_start->ds < 1e-6)) {
        return false;
    }
    double point_heading_local = 0.0;
    double point_interp_x_local = s_curr - target_point_start->s;
    double point_interp_y_local = 0.0;
    double delta_s = 0.0;
    if (!exceed_tp_range) {
        delta_s = s_curr - target_point_start->s;
        point_interp_x_local = delta_s * target_point_start->ratio;
        double point_interp_x_local_2 =
            point_interp_x_local * point_interp_x_local;
        double point_interp_x_local_3 =
            point_interp_x_local_2 * point_interp_x_local;
        point_interp_y_local =
            target_point_start->c_poly[2U] * point_interp_x_local_2 +
            target_point_start->c_poly[3U] * point_interp_x_local_3;
        point_heading_local = math::fast_atan2(
            2.0 * target_point_start->c_poly[2U] * point_interp_x_local +
            3.0 * target_point_start->c_poly[3U] * point_interp_x_local_2);
    }
    point_interp.l =
        delta_s * target_point_start->frenet_ratio + target_point_start->l;
    point_interp.x = target_point_start->heading_cos * point_interp_x_local -
                     target_point_start->heading_sin * point_interp_y_local;
    point_interp.y = target_point_start->heading_sin * point_interp_x_local +
                     target_point_start->heading_cos * point_interp_y_local;
    point_interp.heading = target_point_start->heading + point_heading_local;
    point_interp.x += target_point_start->x;
    point_interp.y += target_point_start->y;
    point_interp.curvature = target_point_start->curvature;
    return true;
}

void BehaviorGenerator::sample_st_array(const double target_speed,
                                        BehaviorCandidate &behavior_candidate) {
    // t_step = 0.2s
    constexpr int kMaxSampleNum = 41;  // 0, 0.2...8.0s
    const double ego_speed = ego_pose_.state.v;
    const double ego_acc = ego_pose_.state.acc;
    double target_v = (target_speed < 0.0) ? std::fmax(ego_speed, 2.0)
                                           : std::fmax(target_speed, 2.0);

    behavior_candidate.target_speed = target_v;

    BehaviorSTPoint st_pt;
    st_pt.v = ego_speed;
    st_pt.a = ego_acc;

    auto &st_samples = behavior_candidate.st_samples;
    st_samples.s.reserve(kMaxSampleNum);
    st_samples.v.reserve(kMaxSampleNum);
    st_samples.a.reserve(kMaxSampleNum);
    st_samples.s.clear();
    st_samples.v.clear();
    st_samples.a.clear();
    st_samples.s.emplace_back(st_pt.s);
    st_samples.v.emplace_back(st_pt.v);
    st_samples.a.emplace_back(st_pt.a);

    const double half_t_step = 0.5 * t_step_;
    const double ds_target = t_step_ * target_v;
    double jerk_curr = 1.0;
    BehaviorSTPoint target_st;
    target_st.v = target_v;
    for (int t_index = 1; t_index < kMaxSampleNum; ++t_index) {
        double s_error = target_st.s - st_pt.s;
        double v_error = target_st.v - st_pt.v;
        double a_error = target_st.a - st_pt.a;
        double a_sign = s_error + kVelCoef * v_error + kAccCoef * a_error;
        if (a_sign > 0.0) {
            // need accelerate
            jerk_curr = (st_pt.a >= 0.0) ? kJerkAccelMaxPositive
                                         : kJerkAccelMaxNegative;
            st_pt.a =
                std::fmin(st_pt.a + t_step_ * jerk_curr, kAccMaxThreshold);
        } else {
            // need decelerate
            jerk_curr = (st_pt.a >= 0.0) ? kJerkDecelMaxPositive
                                         : kJerkDecelMaxNegative;
            st_pt.a =
                std::fmin(st_pt.a + t_step_ * jerk_curr, kAccMaxThreshold);
        }
        double v_new = std::fmax(
            std::fmin(st_pt.v + t_step_ * st_pt.a, kVelMaxThreshold), 0.0);
        st_pt.s += (v_new + st_pt.v) * half_t_step;
        st_pt.v = v_new;

        target_st.t += t_step_;
        target_st.s += ds_target;
        st_samples.s.emplace_back(st_pt.s);
        st_samples.v.emplace_back(st_pt.v);
        st_samples.a.emplace_back(st_pt.a);
    }

    auto &data_manager = session_->data_manager();
    data_manager.store_array("st_samples_s", st_samples.s);
    data_manager.store_array("st_samples_v", st_samples.v);
    data_manager.store_array("st_samples_a", st_samples.a);
    // data_manager.print_data("st_samples_v");

    return;
}

// bool BehaviorGenerator::simulate_behavior(
//     const FrenetCoordinateSystem *frenet_coord,
//     const TrajectoryPoints &target_sl, const STArray &st_samples,
//     BehaviorCandidate &behavior, const double preview_time) {
//     TrajectoryPoints sim_traj{};

//     ego_pose_.param.preview_time = preview_time;

//     const double total_sim_time = 8.0;
//     if (!ForwardSimulation::Simulation(frenet_coord, ego_pose_,
//     total_sim_time,
//                                        st_samples.v, target_sl, sim_traj)) {
//         behavior.valid = false;
//         AINFO << "[ERROR] Forward Simulation is failed! ";
//         return false;
//     }
//     auto &data_manager = session_->data_manager();
//     data_manager.store_array("forward_simulated_traj", sim_traj);
//     // if (!generate_target_point(sim_traj, behavior)) {
//     //     behavior.valid = false;
//     //     return false;
//     // }
//     return true;
// }

bool BehaviorGenerator::simulate_behavior(
    const FrenetCoordinateSystem *frenet_coord, const double target_s,
    BehaviorCandidate &behavior, const double preview_time) {
    TrajectoryPoints sim_traj{};
    TrajectoryPoints target_sl_list{};
    // 该函数计算target_sl信息，返回一系列sl坐标
    // target_s阶段纵向较密集，5m纵向间隔；后续10m纵向间隔
    if (!generate_target_sl(target_s, frenet_coord, target_sl_list,
                            behavior.target_l)) {
        behavior.valid = false;
        return false;
    }

    for (size_t i = 0; i < target_sl_list.size(); ++i) {
        AINFO << "[sz add] target_sl_list " << i
              << " s: " << target_sl_list[i].s << " l: " << target_sl_list[i].l;
    }

    ego_pose_.param.preview_time = preview_time;
    const double total_sim_time = 8.0;
    if (!ForwardSimulation::Simulation(frenet_coord, ego_pose_, total_sim_time,
                                       behavior.st_samples.v, target_sl_list,
                                       sim_traj)) {
        behavior.valid = false;
        AINFO << "[ERROR] Forward Simulation is failed! ";
        return false;
    }
    // auto &data_manager = session_->data_manager();
    // data_manager.store_array("forward_simulated_traj", sim_traj);

    // // debug
    // for (size_t i = 0; i < sim_traj.size(); ++i) {
    //     AINFO << "sim traj " << i << " x: " << sim_traj[i].x
    //           << " y: " << sim_traj[i].y << " s: " << sim_traj[i].s
    //           << " l: " << sim_traj[i].l << " heading: " <<
    //           sim_traj[i].heading
    //           << " curvature: " << sim_traj[i].curvature
    //           << " t: " << sim_traj[i].t << " v: " << sim_traj[i].v
    //           << " a: " << sim_traj[i].a;
    // }

    if (!generate_target_point(sim_traj, behavior)) {
        behavior.valid = false;
        return false;
    }
    return true;
}

bool BehaviorGenerator::generate_target_point(
    const TrajectoryPoints &sim_traj, BehaviorCandidate &behavior_candidate) {
    constexpr int kValidPtNum = 2;
    if (sim_traj.size() <= kValidPtNum) {
        return false;
    }
    const double kValidTrajectoryLengthThreshold = 5.0;
    if (sim_traj.at(sim_traj.size() - 1U).s - ego_frenet_.s <
        kValidTrajectoryLengthThreshold) {
        return false;
    }
    behavior_candidate.target_points.reserve(50U);
    behavior_candidate.target_points.clear();
    BehaviorPoint target_point;
    target_point.x = sim_traj.front().x;
    target_point.y = sim_traj.front().y;
    target_point.heading = sim_traj.front().heading;
    target_point.heading_cos = std::cos(target_point.heading);
    target_point.heading_sin = std::sin(target_point.heading);
    target_point.s = sim_traj.front().s;
    target_point.l = sim_traj.front().l;
    target_point.frenet_valid = sim_traj.front().frenet_valid;
    target_point.vel = sim_traj.front().v;
    BehaviorPoint target_point_last = target_point;
    for (std::size_t i = 2U; i < sim_traj.size(); i += 2) {
        // t_step for target_point is 0.4s;
        target_point.x = sim_traj[i].x;
        target_point.y = sim_traj[i].y;
        target_point.heading = sim_traj[i].heading;
        target_point.heading_cos = std::cos(target_point.heading);
        target_point.heading_sin = std::sin(target_point.heading);
        target_point.s = sim_traj[i].s;
        target_point.l = sim_traj[i].l;
        target_point.frenet_valid = sim_traj[i].frenet_valid;
        target_point.vel = sim_traj[i].v;
        update_target_point_c_poly(target_point, target_point_last);
        target_point_last.curvature =
            target_point_last.dheading / (target_point_last.ds);
        behavior_candidate.target_points.emplace_back(target_point_last);
        target_point_last = target_point;
    }
    behavior_candidate.target_points.emplace_back(target_point_last);
    return true;
}

void BehaviorGenerator::update_target_point_c_poly(
    const BehaviorPoint &target_point, BehaviorPoint &target_point_last) {
    target_point_last.dheading =
        target_point.heading - target_point_last.heading;
    target_point_last.ds = target_point.s - target_point_last.s;
    target_point_last.dl = target_point.l - target_point_last.l;
    target_point_last.dx = target_point.x - target_point_last.x;
    target_point_last.dy = target_point.y - target_point_last.y;
    if (math::equals_with_epsilon(target_point_last.dx, 0.0) &&
        math::equals_with_epsilon(target_point_last.dy, 0.0)) {
        return;
    }
    const double dx_local =
        target_point_last.heading_cos * target_point_last.dx +
        target_point_last.heading_sin * target_point_last.dy;
    const double dy_local =
        target_point_last.heading_cos * target_point_last.dy -
        target_point_last.heading_sin * target_point_last.dx;
    const double end_theta_local = target_point_last.dheading;
    if (std::cos(end_theta_local) <= 0.0) {
        return;
    }
    const double dy_dx_local = std::tan(end_theta_local);
    const double dx_local_2 = dx_local * dx_local;
    const double dx_local_3 = dx_local_2 * dx_local;
    target_point_last.c_poly.at(3U) =
        (dy_dx_local * dx_local - 2.0 * dy_local) / dx_local_3;
    target_point_last.c_poly[2U] =
        dy_local / dx_local_2 - target_point_last.c_poly[3U] * dx_local;
    target_point_last.dx_local = dx_local;
    target_point_last.dy_local = dy_local;
    target_point_last.ratio =
        (math::equals_with_epsilon(target_point_last.ds, 0.0))
            ? 1.0
            : target_point_last.dx_local / target_point_last.ds;
    target_point_last.frenet_ratio =
        (math::equals_with_epsilon(target_point_last.ds, 0.0))
            ? 1.0
            : target_point_last.dl / target_point_last.ds;
}

bool BehaviorGenerator::generate_target_sl(
    const double target_s, const FrenetCoordinateSystem *frenet_coord,
    TrajectoryPoints &target_sl_list, const double target_l) {
    const double ds_max = 5.0;
    const double ds_max2 = 10.0;
    double ds = ds_max;
    const double s_max_norminal = 250.0;
    const double s_max =
        std::fmin(frenet_coord->GetMaxS(), s_max_norminal + ego_frenet_.s);
    target_sl_list.clear();
    double s_curr = ego_frenet_.s;
    double l_curr = ego_frenet_.l;
    const double resolution = 0.25;
    const double s_bkp1 = target_s + ego_frenet_.s;
    if (target_s < 1e-8) {
        while (s_curr <= s_max) {
            TrajectoryPoint traj_pt;
            traj_pt.s = s_curr;
            traj_pt.l = l_curr;
            traj_pt.frenet_valid = true;
            target_sl_list.emplace_back(traj_pt);
            s_curr += ds;
            l_curr = target_l;
        }
    } else {
        const double heading_frenet =
            frenet_coord->GetRefCurveHeading(ego_frenet_.s);
        const double dheading = ego_pose_.state.heading - heading_frenet;
        AINFO << "[sz add] dheading: " << dheading;
        const double s_1 = target_s;
        const double s_2 = s_1 * s_1;
        const double s_3 = s_2 * s_1;
        const double beta = -1.5;
        const double alpha = 2.0;
        const double c1_3nd = std::tan(dheading);
        const double c0_3nd = ego_frenet_.l;
        const double c3_3nd = (c0_3nd + c1_3nd * s_1 - target_l) * alpha / s_3;
        const double c2_3nd = beta * c3_3nd * s_1;
        while (s_curr <= s_max) {
            // t_step = 0.2;
            ds = (s_curr < s_bkp1) ? (std::fmin(target_s * resolution, ds_max))
                                   : ds_max2;
            TrajectoryPoint traj_pt;
            traj_pt.s = s_curr;
            traj_pt.l = l_curr;
            traj_pt.frenet_valid = true;
            target_sl_list.emplace_back(traj_pt);
            s_curr += ds;
            if (s_curr >= s_bkp1) {
                l_curr = target_l;
            } else {
                const double s_path_curr = s_curr - ego_frenet_.s;
                const double s_path_curr_2 = s_path_curr * s_path_curr;
                const double s_path_curr_3 = s_path_curr_2 * s_path_curr;
                l_curr = c0_3nd + c1_3nd * s_path_curr +
                         c2_3nd * s_path_curr_2 + c3_3nd * s_path_curr_3;
                if (target_l > 0.0) {
                    l_curr = std::fmin(l_curr, target_l);
                } else {
                    l_curr = std::fmax(l_curr, target_l);
                }
            }
        }
    }
    return true;
}

}  // namespace planning
}  // namespace arcsoft
