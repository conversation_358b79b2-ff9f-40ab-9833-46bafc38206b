/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __BEHAVIOR_GENERATOR_H__
#define __BEHAVIOR_GENERATOR_H__

// #include <ruckig/ruckig.hpp>

#include "common/basic_types.h"
#include "common/forward_simulator.h"
#include "common/math/math_utils.h"
#include "common/vehicle_config_helper.h"
#include "framework/task.h"

namespace arcsoft {
namespace planning {

// using namespace ruckig;

struct LongitudinalPoint {
    double t = 0.0;
    double s = 0.0;
    double v = 0.0;
    double a = 0.0;
    double j = 0.0;
    LongitudinalPoint(double _t, double _s, double _v, double _a, double _j)
        : t(_t), s(_s), v(_v), a(_a), j(_j) {}
};

class BehaviorGenerator : public framework::Task {
public:
    BehaviorGenerator(const std::string& name, framework::Session* session)
        : Task(name, session) {}
    ~BehaviorGenerator() = default;

    void Init() override;
    bool Process(const size_t candidate_transition_context_id) override;

private:
    void set_ego_state(const TrajectoryPoint& planning_start_point,
                       const FrenetCoordinateSystem* frenet_coordinate);

    bool generate_behavior(const FrenetCoordinateSystem* frenet_coord,
                           const double target_speed,
                           BehaviorCandidate& behavior_candidate);

    void sample_st_array(const double target_speed,
                         BehaviorCandidate& behavior_candidate);

    bool simulate_behavior(const FrenetCoordinateSystem* frenet_coord,
                           const double target_s, BehaviorCandidate& behavior,
                           const double preview_time = 2.0);

    bool generate_target_sl(const double target_s,
                            const FrenetCoordinateSystem* frenet_coord,
                            TrajectoryPoints& target_sl, const double target_l);

    bool generate_target_point(const TrajectoryPoints& sim_traj,
                               BehaviorCandidate& behavior_candidate);

    void update_target_point_c_poly(const BehaviorPoint& target_point,
                                    BehaviorPoint& target_point_last);

    bool fast_interp(const std::vector<BehaviorPoint>& target_points,
                     const double s_curr, BehaviorPoint& point_interp,
                     int& heuristic);

    bool sample_trajectory(const double frenet_s0, BehaviorCandidate& behavior);

    std::vector<std::pair<double, double>> plan_lateral(const double target_l);

    std::vector<LongitudinalPoint> plan_longitudinal(const double target_s,
                                                     const double target_v);

    void TestRuckig(const FrenetCoordinateSystem* frenet_coord,
                    const double target_s, const double target_l,
                    const double target_v);

private:
    double t_step_ = 0.2;
    AgentPose ego_pose_{};
    SLPoint ego_frenet_{-100.0, 0.0};
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __BEHAVIOR_GENERATOR_H__ */