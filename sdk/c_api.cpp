#include <arcsoft_ads_planning.h>

#include "planning.hpp"

ArcAdsStatus ArcAdsPlanning_create(ArcAdsPlanning** out_engine,
                                   const ArcAdsVehicleCalib* vehicle_calib) {
    MLOG_V_NULLPTR(out_engine, ARCADS_STATUS_NULL_POINTER);

    ads::pln::Planning* planning = new ads::pln::Planning();
    *out_engine = (ArcAdsPlanning*)planning;

    const ArcAdsStatus ret = planning->Init();
    if (ret != ARCADS_STATUS_OK) {
        delete planning;
        planning = nullptr;
        *out_engine = nullptr;
        return ret;
    }

    return ARCADS_STATUS_OK;
}

void ArcAdsPlanning_destroy(ArcAdsPlanning* engine) {
    ads::pln::Planning* planning = (ads::pln::Planning*)engine;
    delete planning;
    planning = nullptr;
}

ArcAdsStatus ArcAdsPlanning_reset(ArcAdsPlanning* engine) {
    MLOG_V_NULLPTR(engine, ARCADS_STATUS_NULL_POINTER);
    ads::pln::Planning* planning = (ads::pln::Planning*)engine;
    return planning->Reset();
}

ARCADS_API ArcAdsStatus ArcAdsPlanning_processVehicleCanSignal(
    ArcAdsPlanning* engine, const ArcAdsVehicleCanSignal* vehicle_can_signal) {
    ads::pln::Planning* planning = (ads::pln::Planning*)engine;
    planning->LoadVehicleCanSignal(vehicle_can_signal);
    return ARCADS_OK;
}

ARCADS_API ArcAdsStatus ArcAdsPlanning_processEgoMotion(
    ArcAdsPlanning* engine, const ArcAdsEgoMotionMeasurement* ego_motion) {
    // ads::pln::Planning* planning = (ads::pln::Planning*)engine;
    // planning->LoadEgoMotion(ego_motion);
    return ARCADS_OK;
}

ARCADS_API ArcAdsStatus ArcAdsPlanning_requestAutoDriveMode(
    ArcAdsPlanning* engine, ArcAdsAutoDriveMode auto_drive_mode) {
    ads::pln::Planning* planning = (ads::pln::Planning*)engine;
    planning->LoadAutoDriveMode(auto_drive_mode);
    return ARCADS_OK;
}

ARCADS_API ArcAdsStatus ArcAdsPlanning_setMaxSpeedLimit(ArcAdsPlanning* engine,
                                                        float max_speed) {
    ads::pln::Planning* planning = (ads::pln::Planning*)engine;
    planning->LoadHMISpeedLimit(max_speed);
    return ARCADS_OK;
}

ARCADS_API ArcAdsStatus ArcAdsPlanning_setTimeGap(ArcAdsPlanning* engine,
                                                  float time_gap) {
    ads::pln::Planning* planning = (ads::pln::Planning*)engine;
    planning->LoadHMITimeGap(time_gap);
    return ARCADS_OK;
}

// [TODO]
ARCADS_API float ArcAdsPlanning_getTimeGap(ArcAdsPlanning* engine) {
    const float kTimeGap = 1.5f;
    return kTimeGap;
}

ARCADS_API float ArcAdsPlanning_getMaxSpeedLimit(ArcAdsPlanning* engine) {
    const float kMaxSpeedLimit = 22.2f;
    return kMaxSpeedLimit;
}

ArcAdsStatus ArcAdsPlanning_process(
    ArcAdsPlanning* engine, ArcAdsPlanningTrajectory* out_trajectory,
    const ArcAdsEgoMotionMeasurement* ego_motion,
    const ArcAdsObjectMeasurements* objects,
    const ArcAdsLocalMapMeasurementV2* local_map,
    const ArcAdsTrafficLightLogicalAssembly* traffic_light_logical_assembly,
    const ArcAdsSpeedLimits* speed_limits,
    const ArcAdsNavReferenceLines* reference_lines,
    const ArcAdsMultiModalPrediction* multi_modal_prediction) {
    MLOG_V_NULLPTR(engine, ARCADS_STATUS_NULL_POINTER);
    MLOG_V_NULLPTR(out_trajectory, ARCADS_STATUS_NULL_POINTER);
    MLOG_V_NULLPTR(objects, ARCADS_STATUS_NULL_POINTER);
    MLOG_V_NULLPTR(local_map, ARCADS_STATUS_NULL_POINTER);
    MLOG_V_NULLPTR(traffic_light_logical_assembly, ARCADS_STATUS_NULL_POINTER);
    MLOG_V_NULLPTR(speed_limits, ARCADS_STATUS_NULL_POINTER);
    MLOG_V_NULLPTR(reference_lines, ARCADS_STATUS_NULL_POINTER);
    MLOG_V_NULLPTR(multi_modal_prediction, ARCADS_STATUS_NULL_POINTER);

    ads::pln::Planning* planning = (ads::pln::Planning*)engine;
    ArcAdsStatus planning_status =
        planning->Process(out_trajectory, ego_motion, objects, local_map,
                          traffic_light_logical_assembly, speed_limits,
                          reference_lines, multi_modal_prediction);

    out_trajectory->time = arcsoft::common::get_uint_current_time();
    out_trajectory->frame_id = reference_lines->frame_id;
    return planning_status;
}