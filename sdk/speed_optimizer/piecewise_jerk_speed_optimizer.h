/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

/**
 * @file piecewise_jerk_speed_optimizer.h
 **/

#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "common/discretized_path.h"
#include "common/log.h"
#include "common/vehicle_config_helper.h"
#include "framework/task.h"
#include "piecewise_jerk_speed_problem.h"

namespace arcsoft {
namespace planning {

struct PiecewiseJerkSpeedOptimizerConfig {
    double acc_weight = 5.0;
    double jerk_weight = 500.0;
    double kappa_penalty_weight = 10.0;
    double ref_s_weight = 50.0;
    double ref_v_weight = 500.0;
    double follow_distance_buffer = 8.0;
};

class SpeedOptimizer : public framework::Task {
public:
    void Init() override;

    SpeedOptimizer(const std::string& name, framework::Session* session)
        : Task(name, session) {}
    ~SpeedOptimizer() override = default;
    bool Process(const size_t candidate_transition_context_id) override;
    bool SpeedOptimizerFun(SpeedData* const speed_data,
                           const PathData& path_data,
                           const StGraphData& st_graph_data);

private:
    void AdjustInitStatus(
        const std::vector<std::pair<double, double>> s_dot_bound,
        double delta_t, std::array<double, 3>& init_s);
    PiecewiseJerkSpeedOptimizerConfig config_;
    static constexpr double longitudinal_jerk_lower_bound = -4.0;
    static constexpr double longitudinal_jerk_upper_bound = 6.0;
    static constexpr double veh_speed_min_thd = 5;    // 单位 ： m/s
    static constexpr double path_length_min_thd = 3;  // 单位 ： m
};

}  // namespace planning
}  // namespace arcsoft
