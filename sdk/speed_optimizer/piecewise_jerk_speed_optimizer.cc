/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

/**
 * @file piecewise_jerk_fallback_speed.cc
 **/

#include "piecewise_jerk_speed_optimizer.h"

#include <algorithm>
#include <string>
#include <utility>
#include <vector>

namespace arcsoft {
namespace planning {

void SpeedOptimizer::Init() {}
bool SpeedOptimizer::SpeedOptimizerFun(SpeedData* const speed_data,
                                       const PathData& path_data,
                                       const StGraphData& st_graph_data) {
    SpeedData reference_speed_data = *speed_data;

    if (path_data.empty()) {
        const std::string msg = "Empty path data";
        AERROR << msg;
        return false;
    }
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();

    std::array<double, 3> init_s = {0.0, st_graph_data.init_point().v,
                                    st_graph_data.init_point().a};
    double delta_t = 0.1;
    double total_length = st_graph_data.path_length();
    double total_time = st_graph_data.total_time_by_conf();
    int num_of_knots = static_cast<int>(total_time / delta_t) + 1;
    // Update STBoundary
    const double kEpsilon = 0.01;
    std::vector<std::pair<double, double>> s_bounds;
    for (int i = 0; i < num_of_knots; ++i) {
        double curr_t = i * delta_t;
        double s_lower_bound = 0.0;
        double s_upper_bound = total_length;
        for (const STBoundary& boundary : st_graph_data.st_boundaries()) {
            double s_lower = 0.0;
            double s_upper = 0.0;
            if (!boundary.GetUnblockSRange(curr_t, &s_upper, &s_lower)) {
                continue;
            }
            switch (boundary.boundary_type()) {
                case STBoundary::BoundaryType::STOP:
                case STBoundary::BoundaryType::YIELD:
                    s_upper_bound = std::fmin(s_upper_bound, s_upper);
                    break;
                case STBoundary::BoundaryType::FOLLOW:
                    // TODO(Hongyi): unify follow buffer on decision side
                    s_upper_bound = std::fmin(s_upper_bound, s_upper);
                    break;
                case STBoundary::BoundaryType::OVERTAKE:
                    s_lower_bound = std::fmax(s_lower_bound, s_lower);
                    break;
                default:
                    break;
            }
        }
        s_upper_bound = std::fmax(s_upper_bound, s_lower_bound + kEpsilon);
        if (s_lower_bound > s_upper_bound) {
            const std::string msg =
                "s_lower_bound larger than s_upper_bound on STGraph";
            AERROR << msg;
            speed_data->clear();
            return false;
        }
        s_bounds.emplace_back(s_lower_bound, s_upper_bound);
    }

    // Update SpeedBoundary and ref_s
    std::vector<double> x_ref(num_of_knots, total_length);
    std::vector<double> dx_ref(
        num_of_knots, session_->local_view().vehicle_chassis.hmi_speed_limit);
    std::vector<double> dx_ref_weight(num_of_knots, config_.ref_v_weight);
    std::vector<double> penalty_dx;
    std::vector<std::pair<double, double>> s_dot_bounds;
    const SpeedLimit& speed_limit = st_graph_data.speed_limit();
    for (int i = 0; i < num_of_knots; ++i) {
        double curr_t = i * delta_t;
        // get path_s
        SpeedPoint sp(0, 0, 0, 0, 0);
        if (!reference_speed_data.EvaluateByTime(curr_t, &sp)) {
            sp.s = i > 0 ? x_ref[i - 1] : 0;
            sp.v = 0;
        }
        const double path_s = sp.s;
        x_ref[i] = path_s;
        // get curvature
        const auto& discretized_path = DiscretizedPath(path_data);
        PathPoint path_point = discretized_path.Evaluate(path_s);
        penalty_dx.push_back(std::fabs(path_point.curvature) *
                             config_.kappa_penalty_weight);
        // get v_upper_bound
        // static constexpr double planning_upper_speed_limit = 20.0;
        const double v_lower_bound = 0.0;
        double v_upper_bound =
            speed_limit.GetSpeedLimitByHardConstraints(path_s);
        dx_ref[i] = std::fmin(sp.v, std::fmin(v_upper_bound, dx_ref[i]));
        s_dot_bounds.emplace_back(v_lower_bound, std::fmax(v_upper_bound, 0.0));
        // AINFO << "[dls add] s_dot_bounds v_upper_bound: " << i << " : "
        //       << v_upper_bound << " dx_ref: " << dx_ref[i];
    }
    AdjustInitStatus(s_dot_bounds, delta_t, init_s);
    PiecewiseJerkSpeedProblem piecewise_jerk_problem(num_of_knots, delta_t,
                                                     init_s);
    piecewise_jerk_problem.set_weight_ddx(config_.acc_weight);
    piecewise_jerk_problem.set_weight_dddx(config_.jerk_weight);
    piecewise_jerk_problem.set_scale_factor({1.0, 10.0, 100.0});
    piecewise_jerk_problem.set_x_bounds(0.0, total_length);
    piecewise_jerk_problem.set_ddx_bounds(
        -vehicle_config.max_deceleration,
        std::fmax(init_s[2], vehicle_config.max_acceleration));
    piecewise_jerk_problem.set_dddx_bound(longitudinal_jerk_lower_bound,
                                          longitudinal_jerk_upper_bound);
    piecewise_jerk_problem.set_x_bounds(std::move(s_bounds));
    piecewise_jerk_problem.set_dx_ref(dx_ref_weight, dx_ref);
    piecewise_jerk_problem.set_x_ref(config_.ref_s_weight, std::move(x_ref));
    piecewise_jerk_problem.set_penalty_dx(penalty_dx);
    piecewise_jerk_problem.set_dx_bounds(std::move(s_dot_bounds));

    auto start_time1 = std::chrono::system_clock::now();
    // Solve the problem
    // static constexpr double planning_upper_speed_limit = 31.3;
    if (!piecewise_jerk_problem.Optimize()) {
        // const std::string msg = "Piecewise jerk speed optimizer failed!";
        // AERROR << msg << ".try to fallback.";
        auto end_time1 = std::chrono::system_clock::now();
        std::chrono::duration<double> diff1 = end_time1 - start_time1;
        AINFO << "[TimeCost] SpeedOptimizer Optimize 1-Step Failed used time: "
              << diff1.count() * 1000 << " ms.";

        auto start_time2 = std::chrono::system_clock::now();
        piecewise_jerk_problem.set_dx_bounds(
            0.0, std::fmax(planning_slack_speed_limit,
                           st_graph_data.init_point().v));
        if (!piecewise_jerk_problem.Optimize()) {
            auto end_time2 = std::chrono::system_clock::now();
            std::chrono::duration<double> diff2 = end_time2 - start_time2;
            AINFO << "[TimeCost] SpeedOptimizer Optimize 2-Step Failed used "
                     "time: "
                  << diff2.count() * 1000 << " ms.";

            AERROR << "Piecewise jerk speed optimizer failed!";
            speed_data->clear();
            return false;
        }
        auto end_time2 = std::chrono::system_clock::now();
        std::chrono::duration<double> diff2 = end_time2 - start_time2;
        AINFO << "[TimeCost] SpeedOptimizer Optimize 2-Step Succeed used time: "
              << diff2.count() * 1000 << " ms.";
    }
    auto end_time1 = std::chrono::system_clock::now();
    std::chrono::duration<double> diff1 = end_time1 - start_time1;
    AINFO << "[TimeCost] SpeedOptimizer Optimize All Finished used time: "
          << diff1.count() * 1000 << " ms.";

    // Extract output
    const std::vector<double>& s = piecewise_jerk_problem.opt_x();
    const std::vector<double>& ds = piecewise_jerk_problem.opt_dx();
    const std::vector<double>& dds = piecewise_jerk_problem.opt_ddx();
    // for (size_t i = 0; i < s.size(); ++i) {
    //     AINFO << "[sz add] " << i << " s: " << s[i] << " ds: " << ds[i]
    //           << " dds: " << dds[i];
    // }
    speed_data->clear();
    speed_data->AppendSpeedPoint(s[0], 0.0, ds[0], dds[0], 0.0);
    for (int i = 1; i < num_of_knots; ++i) {
        // Avoid the very last points when already stopped
        if (ds[i] <= 0.0) {
            break;
        }
        speed_data->AppendSpeedPoint(s[i], delta_t * i, ds[i], dds[i],
                                     (dds[i] - dds[i - 1]) / delta_t);
        if (1 == i) {
            speed_data->at(i - 1).da = speed_data->at(i).da;
        }
    }
    // for (size_t i = 0; i < speed_data->size(); ++i) {
    //     AINFO << "speed data " << i << " s: " << speed_data->at(i).s
    //           << " t: " << speed_data->at(i).t << " v: " <<
    //           speed_data->at(i).v
    //           << " a: " << speed_data->at(i).a;
    // }
    return true;
}

bool SpeedOptimizer::Process(const size_t candidate_transition_context_id) {
    auto& speed_data_optimizer_map =
        session_->mutable_speed_data_optimizer_map();
    speed_data_optimizer_map.clear();
    std::vector<SpeedData> speed_data_optimizer_vec;

    auto path_data_sets =
        session_->path_data_vec(candidate_transition_context_id);
    auto st_graph_data_sets =
        session_->st_graph_data_vec(candidate_transition_context_id);
    auto speed_data_sets =
        session_->speed_data_vec(candidate_transition_context_id);

    if (path_data_sets == nullptr || st_graph_data_sets == nullptr) {
        AERROR << "candidate_transition_context_id: "
               << candidate_transition_context_id << " path_data is nullptr! ";
        return false;
    }

    // 前期该vector中仅有一条speeddata
    for (size_t i = 0; i < st_graph_data_sets->size(); i++) {
        // 认为设定停车判断
        if (st_graph_data_sets->at(i).init_point().v < veh_speed_min_thd &&
            st_graph_data_sets->at(i).path_length() < path_length_min_thd) {
            SpeedData speed_data_modify;
            double path_s_unit = st_graph_data_sets->at(0).path_length() / 41;
            for (size_t j = 0; j <= 40; j += 1) {
                speed_data_modify.AppendSpeedPoint(j * path_s_unit, j * 0.1,
                                                   0.0, 0.0, 0.0);
            }
            // for (auto temp : speed_data_modify) {
            //     AINFO << "speed_data_modify_s: " << temp.s
            //           << " speed_data_modify_t: " << temp.t
            //           << " speed_data_modify_v: " << temp.v
            //           << " speed_data_modify_s: " << temp.a
            //           << " speed_data_modify_s: " << temp.da;
            // }
            speed_data_optimizer_vec.emplace_back(speed_data_modify);
            speed_data_optimizer_map.emplace(candidate_transition_context_id,
                                             speed_data_optimizer_vec);
            AINFO << "SpeedOptimizer Modify Keep Stop Success In :  "
                  << candidate_transition_context_id << " Index : " << i;
        } else {
            if (speed_data_sets == nullptr) continue;
            auto speed_data = speed_data_sets->at(i);
            auto path_data = path_data_sets->at(i);
            auto st_graph_data = st_graph_data_sets->at(i);
            // TODO 速度优化
            if (!SpeedOptimizerFun(&speed_data, path_data, st_graph_data)) {
                AINFO << "SpeedOptimizer Failed id: "
                      << candidate_transition_context_id;
                return false;
            }
            auto& data_manager = session_->data_manager();
            data_manager.store_array("SpeedPlanOptimizerData", speed_data);
            DLOG_ARRAY_BEGIN("SpeedPlanOptimizerData");
            for (auto temp : speed_data) {
                DLOG_DICT_BEGIN();
                DLOG_DICT_ITEM("t", temp.t);
                DLOG_DICT_ITEM("s", temp.s);
                DLOG_DICT_ITEM("v", temp.v);
                DLOG_DICT_ITEM("a", temp.a);
                DLOG_DICT_ITEM("da", temp.da);
                DLOG_DICT_END();
            }
            DLOG_ARRAY_END("SpeedPlanOptimizerData");

            speed_data_optimizer_vec.emplace_back(speed_data);
            AINFO << "SpeedOptimizer Success In :  "
                  << candidate_transition_context_id << " Index : " << i;
        }
    }
    // for (size_t j = 0; j < speed_data_optimizer_vec[0].size(); j++) {
    //     AINFO << "SpdOptimzer.t:" << speed_data_optimizer_vec[0][j].t
    //           << " SpdOptimzer.s:" << speed_data_optimizer_vec[0][j].s
    //           << " SpdOptimzer.v:" << speed_data_optimizer_vec[0][j].v
    //           << " SpdOptimzer.a:" << speed_data_optimizer_vec[0][j].a
    //           << " SpdOptimzer.da:" << speed_data_optimizer_vec[0][j].da;
    // }
    if (speed_data_optimizer_vec.size() < 1) {
        AERROR << "candidate_transition_context_id: "
               << candidate_transition_context_id << " speed_data is nullptr! ";
        return false;
    }
    speed_data_optimizer_map.emplace(candidate_transition_context_id,
                                     speed_data_optimizer_vec);
    // AINFO << "SpeedOptimizer Success   :  " <<
    // candidate_transition_context_id;
    return true;
}
void SpeedOptimizer::AdjustInitStatus(
    const std::vector<std::pair<double, double>> s_dot_bound, double delta_t,
    std::array<double, 3>& init_s) {
    double v_min = init_s[1];
    double v_max = init_s[1];
    double a_min = init_s[2];
    double a_max = init_s[2];
    double last_a_min = 0;
    double last_a_max = 0;
    for (size_t i = 1; i < s_dot_bound.size(); i++) {
        last_a_min = a_min;
        last_a_max = a_max;
        a_min = a_min + delta_t * longitudinal_jerk_upper_bound;
        a_max = a_max + delta_t * longitudinal_jerk_lower_bound;
        v_min = v_min + 0.5 * delta_t * (a_min + last_a_min);
        v_max = v_max + 0.5 * delta_t * (a_max + last_a_max);
        // AINFO << "[dls add] AdjustInitStatus i: " << i
        //       << " v_min: " << v_min << " v_max: " << v_max
        //       << " a_min: " << a_min << " a_max: " << a_max
        //       << " s_dot_bound: " << s_dot_bound[i].first << ", "
        //       << s_dot_bound[i].second;
        if (v_min < s_dot_bound[i].first ||
            (v_max > s_dot_bound[i].second && init_s[2] > 0)) {
            init_s[2] = 0;
            return;
        }
    }
}
}  // namespace planning
}  // namespace arcsoft
