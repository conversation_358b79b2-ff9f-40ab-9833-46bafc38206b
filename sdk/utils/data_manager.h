/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __DATA_MANAGER_H__
#define __DATA_MANAGER_H__

#include <fstream>
#include <iostream>
#include <string>
#include <vector>

#include "common/log.h"
#include "json/json.hpp"

namespace arcsoft {
namespace utils {

using json = nlohmann::json;

class DataManager {
private:
    json data_;
    std::string filename_;
    bool is_simulation_mode_;

public:
    DataManager(const std::string& filename) : filename_(filename) {
        data_ = json::object();
        save();
    }

    void set_simulation_mode(const bool is_simulation_mode) {
        is_simulation_mode_ = is_simulation_mode;
    }

    void Clear() { data_.clear(); }

    // 新增：持续追加double类型数据到数组（不清空历史）
    void append_double_value(const std::string& array_name, double value) {
        // 如果数组不存在则创建
        if (!data_.contains(array_name)) {
            data_[array_name] = json::array();
        }

        // 确保目标是一个数组
        if (data_[array_name].is_array()) {
            data_[array_name].push_back(value);
        } else {
            AWARN << "Target " << array_name
                  << " is not an array, creating new array";
            data_[array_name] = json::array({value});
        }
    }

    // 存储单个值
    template <typename T>
    void store_variable(const std::string& name, const T& value) {
        data_[name] = value;
    }

    // 存储数组
    template <typename T>
    void store_array(const std::string& name, const std::vector<T>& values) {
        data_[name] = values;
    }

    void store_array(const std::string& name,
                     const std::vector<planning::Point2D>& values) {
        json j_array = json::array();

        for (const auto& point : values) {
            json j_point;
            j_point["x"] = point.x;
            j_point["y"] = point.y;
            j_array.push_back(j_point);
        }

        data_[name] = j_array;
    }

    void store_array(const std::string& name,
                     const std::vector<planning::ReferenceLinePoint>& values) {
        json j_array = json::array();

        for (const auto& point : values) {
            json j_point;
            j_point["x"] = point.x;
            j_point["y"] = point.y;
            j_array.push_back(j_point);
        }

        data_[name] = j_array;
    }

    void store_array(
        const std::string& name,
        const std::vector<arcsoft::planning::TrajectoryPoint>& values) {
        json j_array = json::array();

        for (const auto& point : values) {
            json j_point;
            j_point["x"] = point.x;
            j_point["y"] = point.y;
            j_point["theta"] = point.heading;
            j_point["kappa"] = point.curvature;
            j_point["s"] = point.s;
            j_point["l"] = point.l;
            j_point["t"] = point.t;
            j_point["v"] = point.v;
            j_point["a"] = point.a;
            j_array.push_back(j_point);
        }

        data_[name] = j_array;
    }

    void store_array(const std::string& name,
                     const std::vector<planning::SpeedPoint>& values) {
        json j_array = json::array();

        for (const auto& point : values) {
            json j_point;
            j_point["s"] = point.s;
            j_point["t"] = point.t;
            j_point["v"] = point.v;
            j_point["a"] = point.a;
            j_point["da"] = point.da;
            j_array.push_back(j_point);
        }

        data_[name] = j_array;
    }

    void store_array(const std::string& name,
                     const std::vector<planning::STPoint>& values) {
        json j_array = json::array();
        for (const auto& point : values) {
            json j_point;
            j_point["s"] = point.s();
            j_point["t"] = point.t();
            j_array.push_back(j_point);
            // AINFO << name << " s: " << point.s() << " t: " << point.t();
        }

        data_[name] = j_array;
    }

    void store_array(const std::string& name,
                     const std::vector<std::string>& values) {
        json j_array = json::array();
        uint8_t count = 0;
        for (const auto& obstacle_id : values) {
            json j_point;
            j_point["obstacle_id_" + std::to_string(count)] = obstacle_id;
            j_array.push_back(j_point);
            count++;
        }

        data_[name] = j_array;
    }

    void store_array(const std::string& name,
                     const std::vector<std::vector<double>>& values) {
        json j_array = json::array();
        for (const auto& lower_higher : values) {
            uint8_t count = 0;
            json j_point;
            for (const auto& low_high : lower_higher) {
                j_point["lower_higher_" + std::to_string(count)] = low_high;
                // AINFO << "lower_higher_" << std::to_string(count) << " : " <<
                // low_high;
                count++;
            }
            j_array.push_back(j_point);
        }

        data_[name] = j_array;
    }

    // 保存到文件
    void save() {
        if (is_simulation_mode_) {
            std::ofstream output_file(filename_);
            if (output_file.is_open()) {
                output_file << data_.dump(4);  // 使用4空格缩进美化输出
            } else {
                AERROR << "Error: Could not open file for writing: "
                       << filename_;
            }
        }
    }

    // 获取存储的数据（用于调试）
    void print_data(const std::string& name) const {
        AINFO << "Stored data_:\n" << data_[name].dump(4);
    }

    // 新增：获取特定变量的值
    template <typename T>
    T get_variable(const std::string& name, const T& default_value) const {
        return data_.value(name, default_value);
    }

    // 新增：检查变量是否存在
    bool has_variable(const std::string& name) const {
        return data_.contains(name);
    }
};

}  // namespace utils
}  // namespace arcsoft

#endif /* __DATA_MANAGER_H__ */