/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __FRENET_COORDINATE_SYSTEM_H__
#define __FRENET_COORDINATE_SYSTEM_H__

#include <algorithm>
#include <cmath>
#include <stdexcept>
#include <vector>

#include "basic_types.h"
#include "log.h"
#include "math/path_matcher.h"

namespace arcsoft {
namespace planning {

class FrenetCoordinateSystem {
public:
    FrenetCoordinateSystem(
        const std::vector<ReferenceLinePoint>& reference_line_point)
        : reference_line_point_(reference_line_point), total_path_length_(0.0) {
        if (reference_line_point.empty()) {
            throw std::invalid_argument("Reference path cannot be empty");
        }

        // 预计算路径长度和累积长度
        PrecomputePathLengths();
    }
    ~FrenetCoordinateSystem() = default;

    bool CartCoord2FrenetCoord(const Point2D& cart, SLPoint& frenet) const;
    bool FrenetCoord2CartCoord(const SLPoint& frenet, Point2D& cart) const;

    bool CartCoord2FrenetCoord(
        const double x, const double y, const double v, const double a,
        const double theta, const double kappa,
        std::array<double, 3>* const ptr_s_condition,
        std::array<double, 3>* const ptr_d_condition) const;
    bool FrenetCoord2CartCoord(const SLPoint& frenet,
                               const std::array<double, 3>& s_condition,
                               const std::array<double, 3>& d_condition,
                               double* const ptr_x, double* const ptr_y,
                               double* const ptr_theta, double* const ptr_kappa,
                               double* const ptr_v, double* const ptr_a);

    // 返回Frenet坐标的最大s值
    double GetMaxS() const { return total_path_length_; }

    // 获取参考路径在s值处的航向角
    double GetRefCurveHeading(double s) const;

    const SLPoint& ego_frenet_sl() const { return ego_frenet_sl_; }

    void set_ego_frenet_sl(const SLPoint& ego_frenet_sl) {
        ego_frenet_sl_ = ego_frenet_sl;
    }

    // 给定 (x, y) 点相对于参考线的横向和投影点的航向角偏差
    bool GetLateralOffsetAndHeading(const double& x, const double& y,
                                    const double& heading,
                                    double& lateral_offset,
                                    double& heading_ref) const;

    // given sl point extract x, y, theta, kappa
    static double CalculateTheta(const double rtheta, const double rkappa,
                                 const double l, const double dl);

    static double CalculateKappa(const double rkappa, const double rdkappa,
                                 const double l, const double dl,
                                 const double ddl);

private:
    std::vector<ReferenceLinePoint> reference_line_point_;
    std::vector<double> path_lengths_;  // 累积路径长度
    double total_path_length_;          // 总路径长度（最大s值）

    // 该条参考线下自车的frenet坐标，注意：s为相对于参考线第一个点
    SLPoint ego_frenet_sl_;

    // 计算两点间距离
    double Distance(const Point2D& p1, const Point2D& p2) const {
        double dx = p1.x - p2.x;
        double dy = p1.y - p2.y;
        return std::sqrt(dx * dx + dy * dy);
    }

    // 预计算路径累积长度
    void PrecomputePathLengths();

    // 根据s值找到对应的路径段索引
    size_t FindSegmentIndex(double s) const;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __FRENET_COORDINATE_SYSTEM_H__ */
