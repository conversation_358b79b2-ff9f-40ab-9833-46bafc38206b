#include "discrete_points_math.h"

namespace arcsoft {
namespace planning {

bool DiscretePointsMath::ComputePathProfile(
    std::vector<ReferenceLinePoint>& xy_points) {
    if (xy_points.size() < 2) {
        return false;
    }

    std::vector<double> dxs;
    std::vector<double> dys;
    std::vector<double> y_over_s_first_derivatives;
    std::vector<double> x_over_s_first_derivatives;
    std::vector<double> y_over_s_second_derivatives;
    std::vector<double> x_over_s_second_derivatives;

    // Get finite difference approximated dx and dy for heading and kappa
    // calculation
    std::size_t points_size = xy_points.size();
    for (std::size_t i = 0; i < points_size; ++i) {
        double x_delta = 0.0;
        double y_delta = 0.0;
        if (i == 0) {
            x_delta = (xy_points[i + 1].x - xy_points[i].x);
            y_delta = (xy_points[i + 1].y - xy_points[i].y);
        } else if (i == points_size - 1) {
            x_delta = (xy_points[i].x - xy_points[i - 1].x);
            y_delta = (xy_points[i].y - xy_points[i - 1].y);
        } else {
            x_delta = 0.5 * (xy_points[i + 1].x - xy_points[i - 1].x);
            y_delta = 0.5 * (xy_points[i + 1].y - xy_points[i - 1].y);
        }
        dxs.push_back(x_delta);
        dys.push_back(y_delta);
    }

    // Heading calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        xy_points[i].heading = std::atan2(dys[i], dxs[i]);
    }

    // Get linear interpolated s for dkappa calculation
    double distance = 0.0;
    xy_points.front().s = distance;
    double fx = xy_points[0].x;
    double fy = xy_points[0].y;
    double nx = 0.0;
    double ny = 0.0;
    for (std::size_t i = 1; i < points_size; ++i) {
        nx = xy_points[i].x;
        ny = xy_points[i].y;
        double end_segment_s =
            std::sqrt((fx - nx) * (fx - nx) + (fy - ny) * (fy - ny));
        xy_points[i].s = end_segment_s + distance;
        distance += end_segment_s;
        fx = nx;
        fy = ny;
    }

    // Get finite difference approximated first derivative of y and x respective
    // to s for kappa calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        double xds = 0.0;
        double yds = 0.0;
        if (i == 0) {
            xds = (xy_points[i + 1].x - xy_points[i].x) /
                  (xy_points[i + 1].s - xy_points[i].s);
            yds = (xy_points[i + 1].y - xy_points[i].y) /
                  (xy_points[i + 1].s - xy_points[i].s);
        } else if (i == points_size - 1) {
            xds = (xy_points[i].x - xy_points[i - 1].x) /
                  (xy_points[i].s - xy_points[i - 1].s);
            yds = (xy_points[i].y - xy_points[i - 1].y) /
                  (xy_points[i].s - xy_points[i - 1].s);
        } else {
            xds = (xy_points[i + 1].x - xy_points[i - 1].x) /
                  (xy_points[i + 1].s - xy_points[i - 1].s);
            yds = (xy_points[i + 1].y - xy_points[i - 1].y) /
                  (xy_points[i + 1].s - xy_points[i - 1].s);
        }
        x_over_s_first_derivatives.push_back(xds);
        y_over_s_first_derivatives.push_back(yds);
    }

    // Get finite difference approximated second derivative of y and x
    // respective to s for kappa calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        double xdds = 0.0;
        double ydds = 0.0;
        if (i == 0) {
            xdds = (x_over_s_first_derivatives[i + 1] -
                    x_over_s_first_derivatives[i]) /
                   (xy_points[i + 1].s - xy_points[i].s);
            ydds = (y_over_s_first_derivatives[i + 1] -
                    y_over_s_first_derivatives[i]) /
                   (xy_points[i + 1].s - xy_points[i].s);
        } else if (i == points_size - 1) {
            xdds = (x_over_s_first_derivatives[i] -
                    x_over_s_first_derivatives[i - 1]) /
                   (xy_points[i].s - xy_points[i - 1].s);
            ydds = (y_over_s_first_derivatives[i] -
                    y_over_s_first_derivatives[i - 1]) /
                   (xy_points[i].s - xy_points[i - 1].s);
        } else {
            xdds = (x_over_s_first_derivatives[i + 1] -
                    x_over_s_first_derivatives[i - 1]) /
                   (xy_points[i + 1].s - xy_points[i - 1].s);
            ydds = (y_over_s_first_derivatives[i + 1] -
                    y_over_s_first_derivatives[i - 1]) /
                   (xy_points[i + 1].s - xy_points[i - 1].s);
        }
        x_over_s_second_derivatives.push_back(xdds);
        y_over_s_second_derivatives.push_back(ydds);
    }

    for (std::size_t i = 0; i < points_size; ++i) {
        double xds = x_over_s_first_derivatives[i];
        double yds = y_over_s_first_derivatives[i];
        double xdds = x_over_s_second_derivatives[i];
        double ydds = y_over_s_second_derivatives[i];
        double kappa =
            (xds * ydds - yds * xdds) /
            (std::sqrt(xds * xds + yds * yds) * (xds * xds + yds * yds) + 1e-6);
        xy_points[i].curvature = kappa;
    }

    // Dkappa calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        double dkappa = 0.0;
        if (i == 0) {
            dkappa = (xy_points[i + 1].curvature - xy_points[i].curvature) /
                     (xy_points[i + 1].s - xy_points[i].s);
        } else if (i == points_size - 1) {
            dkappa = (xy_points[i].curvature - xy_points[i - 1].curvature) /
                     (xy_points[i].s - xy_points[i - 1].s);
        } else {
            dkappa = (xy_points[i + 1].curvature - xy_points[i - 1].curvature) /
                     (xy_points[i + 1].s - xy_points[i - 1].s);
        }
        xy_points[i].dcurvature = dkappa;
    }
    return true;
}

bool DiscretePointsMath::ComputePathProfile(PathData& xy_points) {
    if (xy_points.size() < 2) {
        return false;
    }

    std::vector<double> dxs;
    std::vector<double> dys;
    std::vector<double> y_over_s_first_derivatives;
    std::vector<double> x_over_s_first_derivatives;
    std::vector<double> y_over_s_second_derivatives;
    std::vector<double> x_over_s_second_derivatives;

    // Get finite difference approximated dx and dy for heading and kappa
    // calculation
    std::size_t points_size = xy_points.size();
    for (std::size_t i = 0; i < points_size; ++i) {
        double x_delta = 0.0;
        double y_delta = 0.0;
        if (i == 0) {
            x_delta = (xy_points[i + 1].x - xy_points[i].x);
            y_delta = (xy_points[i + 1].y - xy_points[i].y);
        } else if (i == points_size - 1) {
            x_delta = (xy_points[i].x - xy_points[i - 1].x);
            y_delta = (xy_points[i].y - xy_points[i - 1].y);
        } else {
            x_delta = 0.5 * (xy_points[i + 1].x - xy_points[i - 1].x);
            y_delta = 0.5 * (xy_points[i + 1].y - xy_points[i - 1].y);
        }
        dxs.push_back(x_delta);
        dys.push_back(y_delta);
    }

    // Heading calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        xy_points[i].heading = std::atan2(dys[i], dxs[i]);
    }

    // Get linear interpolated s for dkappa calculation
    double distance = 0.0;
    xy_points.front().s = distance;
    double fx = xy_points[0].x;
    double fy = xy_points[0].y;
    double nx = 0.0;
    double ny = 0.0;
    for (std::size_t i = 1; i < points_size; ++i) {
        nx = xy_points[i].x;
        ny = xy_points[i].y;
        double end_segment_s =
            std::sqrt((fx - nx) * (fx - nx) + (fy - ny) * (fy - ny));
        xy_points[i].s = end_segment_s + distance;
        distance += end_segment_s;
        fx = nx;
        fy = ny;
    }

    // Get finite difference approximated first derivative of y and x respective
    // to s for kappa calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        double xds = 0.0;
        double yds = 0.0;
        if (i == 0) {
            xds = (xy_points[i + 1].x - xy_points[i].x) /
                  (xy_points[i + 1].s - xy_points[i].s);
            yds = (xy_points[i + 1].y - xy_points[i].y) /
                  (xy_points[i + 1].s - xy_points[i].s);
        } else if (i == points_size - 1) {
            xds = (xy_points[i].x - xy_points[i - 1].x) /
                  (xy_points[i].s - xy_points[i - 1].s);
            yds = (xy_points[i].y - xy_points[i - 1].y) /
                  (xy_points[i].s - xy_points[i - 1].s);
        } else {
            xds = (xy_points[i + 1].x - xy_points[i - 1].x) /
                  (xy_points[i + 1].s - xy_points[i - 1].s);
            yds = (xy_points[i + 1].y - xy_points[i - 1].y) /
                  (xy_points[i + 1].s - xy_points[i - 1].s);
        }
        x_over_s_first_derivatives.push_back(xds);
        y_over_s_first_derivatives.push_back(yds);
    }

    // Get finite difference approximated second derivative of y and x
    // respective to s for kappa calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        double xdds = 0.0;
        double ydds = 0.0;
        if (i == 0) {
            xdds = (x_over_s_first_derivatives[i + 1] -
                    x_over_s_first_derivatives[i]) /
                   (xy_points[i + 1].s - xy_points[i].s);
            ydds = (y_over_s_first_derivatives[i + 1] -
                    y_over_s_first_derivatives[i]) /
                   (xy_points[i + 1].s - xy_points[i].s);
        } else if (i == points_size - 1) {
            xdds = (x_over_s_first_derivatives[i] -
                    x_over_s_first_derivatives[i - 1]) /
                   (xy_points[i].s - xy_points[i - 1].s);
            ydds = (y_over_s_first_derivatives[i] -
                    y_over_s_first_derivatives[i - 1]) /
                   (xy_points[i].s - xy_points[i - 1].s);
        } else {
            xdds = (x_over_s_first_derivatives[i + 1] -
                    x_over_s_first_derivatives[i - 1]) /
                   (xy_points[i + 1].s - xy_points[i - 1].s);
            ydds = (y_over_s_first_derivatives[i + 1] -
                    y_over_s_first_derivatives[i - 1]) /
                   (xy_points[i + 1].s - xy_points[i - 1].s);
        }
        x_over_s_second_derivatives.push_back(xdds);
        y_over_s_second_derivatives.push_back(ydds);
    }

    for (std::size_t i = 0; i < points_size; ++i) {
        double xds = x_over_s_first_derivatives[i];
        double yds = y_over_s_first_derivatives[i];
        double xdds = x_over_s_second_derivatives[i];
        double ydds = y_over_s_second_derivatives[i];
        double kappa =
            (xds * ydds - yds * xdds) /
            (std::sqrt(xds * xds + yds * yds) * (xds * xds + yds * yds) + 1e-6);
        xy_points[i].curvature = kappa;
    }

    return true;
}

bool DiscretePointsMath::ComputeTrajectoryKappa(TrajectoryPoints& xy_points) {
    if (xy_points.size() < 2) {
        return false;
    }

    std::vector<double> y_over_s_first_derivatives;
    std::vector<double> x_over_s_first_derivatives;
    std::vector<double> y_over_s_second_derivatives;
    std::vector<double> x_over_s_second_derivatives;

    std::size_t points_size = xy_points.size();

    // Get finite difference approximated first derivative of y and x respective
    // to s for kappa calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        double xds = 0.0;
        double yds = 0.0;
        if (i == 0) {
            xds = (xy_points[i + 1].x - xy_points[i].x) /
                  (xy_points[i + 1].s - xy_points[i].s);
            yds = (xy_points[i + 1].y - xy_points[i].y) /
                  (xy_points[i + 1].s - xy_points[i].s);
        } else if (i == points_size - 1) {
            xds = (xy_points[i].x - xy_points[i - 1].x) /
                  (xy_points[i].s - xy_points[i - 1].s);
            yds = (xy_points[i].y - xy_points[i - 1].y) /
                  (xy_points[i].s - xy_points[i - 1].s);
        } else {
            xds = (xy_points[i + 1].x - xy_points[i - 1].x) /
                  (xy_points[i + 1].s - xy_points[i - 1].s);
            yds = (xy_points[i + 1].y - xy_points[i - 1].y) /
                  (xy_points[i + 1].s - xy_points[i - 1].s);
        }
        x_over_s_first_derivatives.push_back(xds);
        y_over_s_first_derivatives.push_back(yds);
    }

    // Get finite difference approximated second derivative of y and x
    // respective to s for kappa calculation
    for (std::size_t i = 0; i < points_size; ++i) {
        double xdds = 0.0;
        double ydds = 0.0;
        if (i == 0) {
            xdds = (x_over_s_first_derivatives[i + 1] -
                    x_over_s_first_derivatives[i]) /
                   (xy_points[i + 1].s - xy_points[i].s);
            ydds = (y_over_s_first_derivatives[i + 1] -
                    y_over_s_first_derivatives[i]) /
                   (xy_points[i + 1].s - xy_points[i].s);
        } else if (i == points_size - 1) {
            xdds = (x_over_s_first_derivatives[i] -
                    x_over_s_first_derivatives[i - 1]) /
                   (xy_points[i].s - xy_points[i - 1].s);
            ydds = (y_over_s_first_derivatives[i] -
                    y_over_s_first_derivatives[i - 1]) /
                   (xy_points[i].s - xy_points[i - 1].s);
        } else {
            xdds = (x_over_s_first_derivatives[i + 1] -
                    x_over_s_first_derivatives[i - 1]) /
                   (xy_points[i + 1].s - xy_points[i - 1].s);
            ydds = (y_over_s_first_derivatives[i + 1] -
                    y_over_s_first_derivatives[i - 1]) /
                   (xy_points[i + 1].s - xy_points[i - 1].s);
        }
        x_over_s_second_derivatives.push_back(xdds);
        y_over_s_second_derivatives.push_back(ydds);
    }

    for (std::size_t i = 0; i < points_size; ++i) {
        double xds = x_over_s_first_derivatives[i];
        double yds = y_over_s_first_derivatives[i];
        double xdds = x_over_s_second_derivatives[i];
        double ydds = y_over_s_second_derivatives[i];
        double kappa =
            (xds * ydds - yds * xdds) /
            (std::sqrt(xds * xds + yds * yds) * (xds * xds + yds * yds) + 1e-6);
        xy_points[i].curvature = kappa;
    }

    return true;
}

double DiscretePointsMath::ComputeTrajectoryMaxFabsKappa(
    TrajectoryPoints& xy_points) {
    if (xy_points.empty()) return 0.0;

    return std::fabs(std::max_element(xy_points.begin(), xy_points.end(),
                                      [](const auto& a, const auto& b) {
                                          return std::fabs(a.curvature) <
                                                 std::fabs(b.curvature);
                                      })
                         ->curvature);
}

double DiscretePointsMath::ComputeTrajectoryMeanFabsKappa(
    TrajectoryPoints& xy_points) {
    if (xy_points.empty()) return 0.0;

    double sum = std::accumulate(xy_points.begin(), xy_points.end(), 0.0,
                                 [](double acc, const auto& pt) {
                                     return acc + std::fabs(pt.curvature);
                                 });

    return sum / xy_points.size();
}

bool DiscretePointsMath::ComputeInterpolateTrajectory(
    TrajectoryPoints& xy_points, const double interpolation_interval) {
    xy_points = math::InterpolateTrajectory(xy_points, interpolation_interval);

    return true;
}

}  // namespace planning
}  // namespace arcsoft
