/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __DISCRETIZED_PATH_H__
#define __DISCRETIZED_PATH_H__

#include "basic_types.h"

namespace arcsoft {
namespace planning {

class DiscretizedPath : public std::vector<PathPoint> {
public:
    DiscretizedPath() = default;

    explicit DiscretizedPath(std::vector<PathPoint> path_points);

    double Length() const;

    PathPoint Evaluate(const double path_s) const;

    PathPoint EvaluateReverse(const double path_s) const;

protected:
    std::vector<PathPoint>::const_iterator QueryLowerBound(
        const double path_s) const;
    std::vector<PathPoint>::const_iterator QueryUpperBound(
        const double path_s) const;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __DISCRETIZED_PATH_H__ */