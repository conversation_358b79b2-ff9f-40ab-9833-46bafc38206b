/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __LOCAL_VIEW_H__
#define __LOCAL_VIEW_H__

#include "arcsoft_ads_ego_motion.h"
#include "arcsoft_ads_nav_model_base.h"
#include "arcsoft_ads_object_measurement.h"
#include "arcsoft_ads_prediction_base.h"
#include "arcsoft_ads_vehicle.h"

namespace arcsoft {
namespace common {

enum VehicleLightFLAG { OFF = 0, LEFT_TURN = 1, RIGHT_TURN = 2 };

enum VehicleGear { GEAR_N = 0, GEAR_D = 1, GEAR_P = 2, GEAR_R = 3 };
enum class AutoDriveModeState { OFF = 0, ACC = 1, LCC = 2, NOA = 3 };
enum class AutoDriveFeature { OFF = 0, DCLC_LEFT, DCLC_RIGHT };

struct VehicleChassis {
    VehicleLightFLAG vehicle_light_flag;
    VehicleGear vehicle_gear;
    double steer_angle;

    AutoDriveFeature driving_feature = AutoDriveFeature::OFF;
    double hmi_speed_limit = 33.3;  // 120km/h
    double hmi_time_gap = 1.5;      // 单位: s

    double lon_velocity = 0.0;
    double lon_acceleration = 0.0;
};

struct VehicleState {
    double x = 0.0;
    double y = 0.0;
    double kappa = 0.0;  // Curvature
    double heading = 0.0;
    double linear_velocity = 0.0;
    double linear_acceleration = 0.0;
    double angular_velocity = 0.0;
    common::Box2d box = {common::Vec2d(x, y), heading, 1.0, 1.0};
    AutoDriveModeState driving_mode =
        AutoDriveModeState::OFF;  // 0: OFF, 1: ACC, 2: LCC, 3: NOA

    ArcAdsGlobalPose3d global_pose{};
};

struct LocalView {
    VehicleState vehicle_state;
    std::vector<ArcAdsObjectPrediction> prediction_trajectories;
    std::vector<ArcAdsObjectMeasurement> perception_obstacles;
    VehicleChassis vehicle_chassis;

    bool localization_status = false;
    bool perception_obstacles_status = false;
    bool nav_model_status = false;
    bool prediction_status = false;
    bool chassis_status = false;
    uint64_t frame_id;
    uint32_t time;

    void ClearVehicleState() {
        vehicle_state = VehicleState();
        localization_status = false;
    }

    void ClearVehicleChassis() {
        vehicle_chassis = VehicleChassis();
        chassis_status = false;
    }

    void ClearOtherFields() {
        prediction_trajectories.clear();
        perception_obstacles.clear();

        nav_model_status = false;
        localization_status = false;
        perception_obstacles_status = false;
        prediction_status = false;
    }

    // 清空所有变量
    void ClearAll() {
        ClearVehicleState();
        ClearVehicleChassis();
        ClearOtherFields();
    }
};

}  // namespace common
}  // namespace arcsoft

#endif /* __LOCAL_VIEW_H__ */