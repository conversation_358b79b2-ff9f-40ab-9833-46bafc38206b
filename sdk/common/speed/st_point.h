/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __ST_POINT_H__
#define __ST_POINT_H__

#include <string>

#include "common/vec2d.h"

namespace arcsoft {
namespace planning {

class STPoint : public common::Vec2d {
    // x-axis: t; y-axis: s.
public:
    STPoint() = default;
    STPoint(const double s, const double t);
    explicit STPoint(const common::Vec2d& vec2d_point);

    double x() const = delete;
    double y() const = delete;

    double s() const;
    double t() const;
    void set_s(const double s);
    void set_t(const double t);
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __ST_POINT_H__ */