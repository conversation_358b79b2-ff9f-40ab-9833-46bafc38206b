/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __SPEED_LIMIT_H__
#define __SPEED_LIMIT_H__
#include <utility>
#include <vector>

namespace arcsoft {
namespace planning {

static constexpr double planning_upper_speed_limit = 22.2;  // 80km/h
static constexpr double planning_slack_speed_limit = 33.3;  // 120km/h
// [TODO] For Car Test
// static constexpr double planning_upper_speed_limit = 13.8;  // 50km/h
// static constexpr double planning_slack_speed_limit = 16.6;  // 60km/h

class SpeedLimit {
public:
    SpeedLimit() = default;

    void AppendSpeedLimitForHardConstraints(const double s, const double v);
    void AppendSpeedLimitForSoftConstraints(const double s, const double v);

    const std::vector<std::pair<double, double>>& speed_limit_points() const;

    double GetSpeedLimitByHardConstraints(const double s) const;
    double GetSpeedLimitBySoftConstraints(const double s) const;
    double GetMinSpeedLimitBySoftConstraints() const;

    void Clear();

private:
    // use a vector to represent speed limit
    // the first number is s, the second number is v
    // It means at distance s from the start point, the speed limit is v.
    std::vector<std::pair<double, double>> speed_limit_points_hard_constraints;
    std::vector<std::pair<double, double>> speed_limit_points_soft_constraints;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __SPEED_LIMIT_H__ */