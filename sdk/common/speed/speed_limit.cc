/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "speed_limit.h"

#include <algorithm>

namespace arcsoft {
namespace planning {

void SpeedLimit::AppendSpeedLimitForHardConstraints(const double s,
                                                    const double v) {
    speed_limit_points_hard_constraints.emplace_back(s, v);
}

void SpeedLimit::AppendSpeedLimitForSoftConstraints(const double s,
                                                    const double v) {
    speed_limit_points_soft_constraints.emplace_back(s, v);
}

const std::vector<std::pair<double, double>>& SpeedLimit::speed_limit_points()
    const {
    return speed_limit_points_hard_constraints;
}

double SpeedLimit::GetSpeedLimitByHardConstraints(const double s) const {
    auto compare_s = [](const std::pair<double, double>& point,
                        const double s) { return point.first < s; };

    auto it_lower = std::lower_bound(
        speed_limit_points_hard_constraints.begin(),
        speed_limit_points_hard_constraints.end(), s, compare_s);

    if (it_lower == speed_limit_points_hard_constraints.end()) {
        return (it_lower - 1)->second;
    }
    return it_lower->second;
}

double SpeedLimit::GetSpeedLimitBySoftConstraints(const double s) const {
    auto compare_s = [](const std::pair<double, double>& point,
                        const double s) { return point.first < s; };

    auto it_lower = std::lower_bound(
        speed_limit_points_soft_constraints.begin(),
        speed_limit_points_soft_constraints.end(), s, compare_s);

    if (it_lower == speed_limit_points_soft_constraints.end()) {
        return (it_lower - 1)->second;
    }
    return it_lower->second;
}

double SpeedLimit::GetMinSpeedLimitBySoftConstraints() const {
    double MinSpeedLimitByCurvature = 100.0;
    for (const auto& speed_limit_points : speed_limit_points_soft_constraints) {
        if (speed_limit_points.second < MinSpeedLimitByCurvature) {
            MinSpeedLimitByCurvature = speed_limit_points.second;
        }
    }
    return MinSpeedLimitByCurvature;
}

void SpeedLimit::Clear() {
    speed_limit_points_hard_constraints.clear();
    speed_limit_points_soft_constraints.clear();
}

}  // namespace planning
}  // namespace arcsoft
