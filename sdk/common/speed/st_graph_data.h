/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __ST_GRAPH_DATA_H__
#define __ST_GRAPH_DATA_H__

#include <tuple>
#include <vector>

#include "basic_types.h"
#include "speed_limit.h"
#include "st_boundary.h"

namespace arcsoft {
namespace planning {

constexpr double kDefaultCruiseSpeed = 16.6;  // 60km/h

class StGraphData {
public:
    StGraphData() = default;

    void LoadData(const std::vector<STBoundary>& st_boundaries,
                  const double min_s_on_st_boundaries,
                  const TrajectoryPoint& init_point,
                  const SpeedLimit& speed_limit, const double cruise_speed,
                  const double path_data_length,
                  const double total_time_by_conf);

    bool is_initialized() const { return init_; }

    const std::vector<STBoundary>& st_boundaries() const;

    double min_s_on_st_boundaries() const;

    const TrajectoryPoint& init_point() const;

    const SpeedLimit& speed_limit() const;

    double cruise_speed() const;

    double path_length() const;

    double total_time_by_conf() const;

    std::vector<STBoundary>& mutable_st_boundaries();

private:
    bool init_ = false;
    std::vector<STBoundary> st_boundaries_;
    double min_s_on_st_boundaries_ = 0.0;
    TrajectoryPoint init_point_;
    SpeedLimit speed_limit_;
    double cruise_speed_ = 0.0;
    double path_data_length_ = 0.0;
    double path_length_by_conf_ = 0.0;
    double total_time_by_conf_ = 0.0;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __ST_GRAPH_DATA_H__ */