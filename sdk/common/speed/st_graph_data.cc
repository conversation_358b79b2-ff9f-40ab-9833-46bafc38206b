/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "st_graph_data.h"

namespace arcsoft {
namespace planning {

void StGraphData::LoadData(const std::vector<STBoundary>& st_boundaries,
                           const double min_s_on_st_boundaries,
                           const TrajectoryPoint& init_point,
                           const SpeedLimit& speed_limit,
                           const double cruise_speed,
                           const double path_data_length,
                           const double total_time_by_conf) {
    init_ = true;
    st_boundaries_ = st_boundaries;
    min_s_on_st_boundaries_ = min_s_on_st_boundaries;
    init_point_ = init_point;
    speed_limit_ = speed_limit;
    cruise_speed_ = cruise_speed;
    path_data_length_ = path_data_length;
    total_time_by_conf_ = total_time_by_conf;
}

const std::vector<STBoundary>& StGraphData::st_boundaries() const {
    return st_boundaries_;
}

std::vector<STBoundary>& StGraphData::mutable_st_boundaries() {
    return st_boundaries_;
}

double StGraphData::min_s_on_st_boundaries() const {
    return min_s_on_st_boundaries_;
}

const TrajectoryPoint& StGraphData::init_point() const { return init_point_; }

const SpeedLimit& StGraphData::speed_limit() const { return speed_limit_; }

double StGraphData::cruise_speed() const {
    return cruise_speed_ > 0.0 ? cruise_speed_ : kDefaultCruiseSpeed;
}

double StGraphData::path_length() const { return path_data_length_; }

double StGraphData::total_time_by_conf() const { return total_time_by_conf_; }

}  // namespace planning
}  // namespace arcsoft
