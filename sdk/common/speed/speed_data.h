/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __SPEED_DATA_H__
#define __SPEED_DATA_H__

#include <string>
#include <vector>

#include "common/basic_types.h"

namespace arcsoft {
namespace planning {

class SpeedData : public std::vector<SpeedPoint> {
public:
    SpeedData() = default;

    virtual ~SpeedData() = default;

    explicit SpeedData(std::vector<SpeedPoint> speed_points);

    void AppendSpeedPoint(const double s, const double time, const double v,
                          const double a, const double da);

    bool EvaluateByTime(const double time, SpeedPoint* const speed_point) const;

    // Assuming spatial traversed distance is monotonous, which is the case for
    // current usage on city driving scenario
    bool EvaluateByS(const double s, SpeedPoint* const speed_point) const;

    double TotalTime() const;

    // Assuming spatial traversed distance is monotonous
    double TotalLength() const;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __SPEED_DATA_H__ */