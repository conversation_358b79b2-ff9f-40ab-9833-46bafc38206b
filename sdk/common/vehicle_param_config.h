/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __VEHICLE_PARAM_CONFIG_H__
#define __VEHICLE_PARAM_CONFIG_H__

#include <string>

namespace arcsoft {
namespace common {

struct VehicleParamConfig {
    std::string vehicle_id = "001";
    std::string vehicle_name = "LiXiangOne";
    double front_edge_to_center = 3.94;
    double back_edge_to_center = 1.09;
    double left_edge_to_center = 0.98;
    double right_edge_to_center = 0.98;

    double length = 5.03;
    double width = 1.96;
    double height = 1.6;

    double min_turn_radius = 6.25;
    double max_acceleration = 2.0;
    double max_deceleration = 6.0;

    double max_steer_angle = 0.488;  // 前轮最大a转角，28 degree
    double max_steer_angle_rate = 0.458;
    double min_steer_angle_rate = 0.2;
    double steer_ratio = 1.0;
    double wheel_base = 2.935;
};

}  // namespace common
}  // namespace arcsoft

#endif /* __VEHICLE_PARAM_CONFIG_H__ */