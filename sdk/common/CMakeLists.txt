cmake_minimum_required(VERSION 3.15)
project(common)
file(GLOB_RECURSE HDRS "*.h" "*.hpp")
file(GLOB_RECURSE SRCS "*.cpp" "*.c" "*.cc")
add_library(${PROJECT_NAME} OBJECT ${SRCS} ${HDRS})

# message(STATUS "GLOB SRCS 结果: ${SRCS}") # 查看匹配到的文件 添加编译定义
target_compile_definitions(
  ${PROJECT_NAME}
  PRIVATE
    VEHICLE_PARAM_CONFIG_PATH="${CMAKE_CURRENT_SOURCE_DIR}/configs/vehicle_param_config.json"
)
target_include_directories(${PROJECT_NAME} PUBLIC
  ${CMAKE_CURRENT_SOURCE_DIR}
  ${CMAKE_CURRENT_SOURCE_DIR}/..)
target_link_libraries(${PROJECT_NAME} PUBLIC arcpkg::mlog arcpkg::dlog ads::arcsoft_ads_data_types)
target_link_libraries(
  ${PROJECT_NAME}
  PUBLIC
  osqpstatic)