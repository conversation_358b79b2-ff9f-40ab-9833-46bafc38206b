/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "reference_line.h"

#include "utils.h"

namespace arcsoft {
namespace planning {

static constexpr size_t kSampleCount = 5;
static constexpr double kSampledInterval = 1.5;

void ReferenceLine::set_raw_points(
    const std::vector<ArcAdsNavReferenceLinePoint>& raw_points) {
    raw_points_ = raw_points;

    raw_length_ = 0.0;
    for (size_t i = 1; i < raw_points.size(); ++i) {
        const auto& last_raw_pt = raw_points[i - 1];
        const auto& curr_raw_pt = raw_points[i];
        const double distance = std::hypot(curr_raw_pt.x - last_raw_pt.x,
                                           curr_raw_pt.y - last_raw_pt.y);

        raw_length_ += distance;
    }
}

void ReferenceLine::set_smoothed_points(
    const std::vector<ReferenceLinePoint>& smoothed_points) {
    smoothed_points_ = smoothed_points;

    if (!smoothed_points_.empty()) {
        frenet_coord_ =
            std::make_shared<FrenetCoordinateSystem>(smoothed_points_);
        // AINFO << "set reference smoothed path ok! Number: "
        //       << smoothed_points.size();
    } else {
        frenet_coord_.reset();
    }
}

bool ReferenceLine::GetSLBoundary(const std::vector<Point2D>& box,
                                  double warm_start_s) {
    double start_s(std::numeric_limits<double>::max());
    double end_s(std::numeric_limits<double>::lowest());
    double start_l(std::numeric_limits<double>::max());
    double end_l(std::numeric_limits<double>::lowest());

    for (const auto& point : box) {
        SLPoint sl_point;
        if (!this->frenet_coord_->CartCoord2FrenetCoord(point, sl_point)) {
            AERROR << "Failed to get projection for point";
            return false;
        }
        start_s = std::fmin(start_s, sl_point.s);
        end_s = std::fmax(end_s, sl_point.s);
        start_l = std::fmin(start_l, sl_point.l);
        end_l = std::fmax(end_l, sl_point.l);
    }

    adc_sl_boundary_.set_start_s(start_s);
    adc_sl_boundary_.set_end_s(end_s);
    adc_sl_boundary_.set_start_l(start_l);
    adc_sl_boundary_.set_end_l(end_l);

    return true;
}

double ReferenceLine::CalculateHausdorffDistance(
    const std::vector<ReferenceLinePoint>& last_current_lane_points) {
    if (last_current_lane_points.size() < 0 || smoothed_points_.size() < 0) {
        return std::numeric_limits<double>::max();
    }

    double max_dist_current_to_last = 0.0;
    double last_test_x = 0.0;
    double last_test_y = 0.0;
    double curr_test_x = 0.0;
    double curr_test_y = 0.0;
    for (const auto& curr_pt : smoothed_points_) {
        if (curr_pt.s < frenet_coord_->ego_frenet_sl().s) {
            continue;
        }

        double min_dist = std::numeric_limits<double>::max();
        for (const auto& last_pt : last_current_lane_points) {
            double dist =
                std::hypot(last_pt.x - curr_pt.x, last_pt.y - curr_pt.y);
            if (dist < min_dist) {
                min_dist = dist;
                last_test_x = last_pt.x;
                last_test_y = last_pt.y;
                curr_test_x = curr_pt.x;
                curr_test_y = curr_pt.y;
            }
        }
        if (min_dist > max_dist_current_to_last) {
            max_dist_current_to_last = min_dist;
        }
    }

    // AINFO << "last_test_x: " << last_test_x << " last_test_y: " <<
    // last_test_y
    //       << " curr_test_x: " << curr_test_x << " curr_test_y: " <<
    //       curr_test_y;
    hausdorff_dis_ = max_dist_current_to_last;

    return max_dist_current_to_last;
}

const bool ReferenceLine::FindSideNearestReferenceLine(
    const std::vector<std::shared_ptr<ReferenceLine>>& candidate_lanes,
    const bool is_left_side, uint32_t& nearest_lane_id) const {
    static constexpr double kMinReferenceLineGap = 2.0;
    std::unordered_map<uint32_t, double> distance_map;
    double min_dist = std::numeric_limits<double>::max();
    uint32_t min_index = 0;

    if (candidate_lanes.empty()) {
        AINFO << "candidate_lanes is empty";
        return false;
    }

    if (EvaluateReferenceLineRawDistance(candidate_lanes, distance_map)) {
        for (const auto& [lane_id, distance] : distance_map) {
            if (is_left_side) {
                if ((distance > kMinReferenceLineGap * kSampleCount) &&
                    (std::fabs(distance) < min_dist)) {
                    min_dist = distance;
                    min_index = lane_id;
                }
            } else {
                if ((distance < -kMinReferenceLineGap * kSampleCount) &&
                    (std::fabs(distance) < min_dist)) {
                    min_dist = distance;
                    min_index = lane_id;
                }
            }
        }
    }
    nearest_lane_id = min_index;
    if (min_index != 0) {
        return true;
    }
    return false;
}

const bool ReferenceLine::FindNearestReferenceLine(
    const std::vector<std::shared_ptr<ReferenceLine>>& candidate_lanes,
    uint32_t& nearest_lane_id) const {
    std::unordered_map<uint32_t, double> distance_map;
    double min_dist = std::numeric_limits<double>::max();
    uint32_t min_index = 0;

    if (candidate_lanes.empty()) {
        AINFO << "candidate_lanes is empty";
        return false;
    }

    if (EvaluateReferenceLineRawDistance(candidate_lanes, distance_map)) {
        for (const auto& [lane_id, distance] : distance_map) {
            if (std::fabs(distance) < min_dist) {
                min_dist = std::fabs(distance);
                min_index = lane_id;
            }
        }
        nearest_lane_id = min_index;
        return true;
    }
    nearest_lane_id = 0;
    return false;
}

const bool ReferenceLine::EvaluateReferenceLineRawDistance(
    const std::vector<std::shared_ptr<ReferenceLine>>& candidate_lanes,
    std::unordered_map<uint32_t, double>& distance_map) const {
    static constexpr double kSampleInterval = 8.0;
    static constexpr size_t kSampleCount = 5;
    if (candidate_lanes.empty()) {
        AINFO << "candidate_lanes is empty";
        return false;
    }
    if (frenet_coord_ == nullptr) {
        AINFO << "frenet_coord_ is nullptr";
        return false;
    }
    if (smoothed_points_.size() <= kSampleCount) {
        AINFO << "smoothed_points_ size is less than " << kSampleCount;
        return false;
    }

    /// 1. 在目标reference line上前向找5个采样点，每个采样点间隔一定距离
    const auto ego_frenet_s = frenet_coord_->ego_frenet_sl().s;
    const auto ego_frenet_l = frenet_coord_->ego_frenet_sl().l;
    std::vector<ReferenceLinePoint> sample_points;
    double ref_s = ego_frenet_s;
    for (const auto& pt : smoothed_points_) {
        // AINFO << "zcq debug: " << pt.x << " " << pt.y;
        if (pt.s < ego_frenet_s) {
            continue;
        }
        if (pt.s - ref_s > kSampleInterval) {
            sample_points.emplace_back(pt);
            ref_s += kSampleInterval;
        }
        if (sample_points.size() >= kSampleCount) {
            break;
        }
    }

    if (sample_points.size() < kSampleCount) {
        AINFO << "sample_points size is less than " << kSampleCount;
        return false;
    }

    /// 2. 基于这五个采样点，向变道方向reference line query到5个对应的最近点
    for (size_t i = 0; i < candidate_lanes.size(); i++) {
        const auto& candidate_lane = candidate_lanes[i];
        if (candidate_lane == nullptr) {
            AINFO << "candidate_lane is nullptr";
            continue;
        }
        if (candidate_lane->smoothed_points().size() <= kSampleCount) {
            AINFO << "candidate_lanes smoothed_points size is less than "
                  << kSampleCount;
            continue;
        }
        for (const auto& sample_point : sample_points) {
            auto ref_pt = math::PathMatcher::MatchToPath(
                candidate_lane->smoothed_points(), sample_point.x,
                sample_point.y, false);
            /// 此处计算曼哈顿距离，即x和y的差值之和，降低计算量
            if (distance_map.count(candidate_lane->id()) == 0) {
                distance_map.insert({candidate_lane->id(), 0.0});
            }
            distance_map[candidate_lane->id()] +=
                ref_pt.x - sample_point.x + ref_pt.y - sample_point.y;
            // AINFO << "candidate_lane->id(): " << candidate_lane->id()
            //       << ", distance: " << distance_map[candidate_lane->id()]
            //       << " sample_point.x: " << sample_point.x
            //       << " sample_point.y: " << sample_point.y
            //       << " ref_pt.x: " << ref_pt.x << " ref_pt.y: " << ref_pt.y;
        }
    }
    return true;
}

// 根据当前s匹配参考线最近点，此处的s是从参考线的第一个点开始的
// ReferenceLinePoint ReferenceLine::GetNearestReferencePoint(
//     const double s) const {
//     if (smoothed_points_.empty()) {
//         AERROR << "Reference line is empty.";
//         return ReferenceLinePoint();
//     }
//     if (s < smoothed_points_.front().s - 1e-2) {
//         AWARN << "The requested s: " << s << " < 0.";
//         return smoothed_points_.front();
//     }
//     if (s > smoothed_points_.back().s + 1e-2) {
//         AWARN << "The requested s: " << s
//               << " > reference line length: " << smoothed_points_.back().s;
//         return smoothed_points_.back();
//     }

//     auto it_lower = std::lower_bound(
//         smoothed_points_.begin(), smoothed_points_.end(), s,
//         [](const ReferenceLinePoint& pt, const double s_val) {
//             return pt.s < s_val;  // 或者 pt.s() < s_val，取决于你定义
//         });

//     if (it_lower == smoothed_points_.begin()) {
//         return smoothed_points_.front();
//     }
//     auto index = std::distance(smoothed_points_.begin(), it_lower);
//     if (std::fabs(smoothed_points_[index - 1].s - s) <
//         std::fabs(smoothed_points_[index].s - s)) {
//         return smoothed_points_[index - 1];
//     }
//     return smoothed_points_[index];
// }

// 根据当前s匹配参考线最近点，此处的s是从参考线的第一个点开始的
ReferenceLinePoint ReferenceLine::GetNearestReferencePoint(
    const double s) const {
    if (smoothed_points_.empty()) {
        AERROR << "Reference line is empty.";
        return ReferenceLinePoint();
    }

    if (s <= smoothed_points_.front().s) {
        return smoothed_points_.front();
    }
    if (s >= smoothed_points_.back().s) {
        return smoothed_points_.back();
    }

    auto it_upper =
        std::lower_bound(smoothed_points_.begin(), smoothed_points_.end(), s,
                         [](const ReferenceLinePoint& pt, const double value) {
                             return pt.s < value;
                         });

    auto it_lower = std::prev(it_upper);

    const auto& curr_pt = *it_lower;
    const auto& next_pt = *it_upper;

    const double s0 = curr_pt.s;
    const double s1 = next_pt.s;

    ReferenceLinePoint interpolated_point;

    interpolated_point.s = s;
    interpolated_point.x = math::lerp(curr_pt.x, s0, next_pt.x, s1, s);
    interpolated_point.y = math::lerp(curr_pt.y, s0, next_pt.y, s1, s);

    double heading = math::slerp(curr_pt.heading, s0, next_pt.heading, s1, s);
    interpolated_point.heading = heading;
    interpolated_point.curvature =
        math::lerp(curr_pt.curvature, s0, next_pt.curvature, s1, s);

    interpolated_point.dcurvature =
        math::lerp(curr_pt.dcurvature, s0, next_pt.dcurvature, s1, s);
    return interpolated_point;
}

bool ReferenceLine::GetRoadWidth(const double s, double& curr_road_left_width,
                                 double& curr_road_right_width) const {
    // 该s值为target_linei起点为0
    if (s < 0.0 || s > smoothed_points_.back().s) {
        return false;
    }

    size_t matched_index = 0;
    for (size_t i = 1; i < smoothed_points_.size(); i++) {
        const auto& last_smoothed_pt = smoothed_points_[i - 1];
        const auto& curr_smoothed_pt = smoothed_points_[i];
        if (curr_smoothed_pt.s > s && last_smoothed_pt.s < s) {
            matched_index = i - 1;
        }
    }
    size_t next_index = matched_index + 1;

    ReferenceLinePoint curr_pt = smoothed_points_[matched_index];
    ReferenceLinePoint next_pt = smoothed_points_[next_index];

    const double s0 = curr_pt.s;
    const double s1 = next_pt.s;

    curr_road_left_width = math::lerp(curr_pt.distance_to_left_boundary, s0,
                                      next_pt.distance_to_left_boundary, s1, s);
    curr_road_right_width =
        math::lerp(curr_pt.distance_to_right_boundary, s0,
                   next_pt.distance_to_right_boundary, s1, s);

    return true;
}

bool ReferenceLine::GetLaneWidth(const double s, double& curr_lane_left_width,
                                 double& curr_lane_right_width) const {
    // 该s值为target_linei起点为0
    if (s < 0.0 || s > smoothed_points_.back().s) {
        return false;
    }

    size_t matched_index = 0;
    for (size_t i = 1; i < smoothed_points_.size(); i++) {
        const auto& last_smoothed_pt = smoothed_points_[i - 1];
        const auto& curr_smoothed_pt = smoothed_points_[i];
        if (curr_smoothed_pt.s > s && last_smoothed_pt.s < s) {
            matched_index = i - 1;
        }
    }
    size_t next_index = matched_index + 1;

    ReferenceLinePoint curr_pt = smoothed_points_[matched_index];
    ReferenceLinePoint next_pt = smoothed_points_[next_index];

    const double s0 = curr_pt.s;
    const double s1 = next_pt.s;

    curr_lane_left_width = math::lerp(curr_pt.distance_to_left_divider, s0,
                                      next_pt.distance_to_left_divider, s1, s);
    curr_lane_right_width =
        math::lerp(curr_pt.distance_to_right_divider, s0,
                   next_pt.distance_to_right_divider, s1, s);

    return true;
}

size_t ReferenceLine::find_matched_index(const double s) const {
    if (s <= 0.0) {
        return 0;
    }

    if (raw_points_.size() <= 0) {
        return raw_points_.size();
    }

    if (s >= raw_length_) {
        return static_cast<int>(raw_points_.size()) - 1;
    }

    for (size_t i = 1; i < raw_points_.size(); ++i) {
        const auto& last_raw_pt = raw_points_[i - 1];
        const auto& curr_raw_pt = raw_points_[i];
        if (curr_raw_pt.s > s && last_raw_pt.s < s) {
            return i - 1;
        }
    }

    return raw_points_.size();
}

bool ReferenceLine::SampleRawPoints() {
    int num_of_sampled_points =
        std::max(2, static_cast<int>(raw_length_ / kSampledInterval + 0.5));
    std::vector<double> sample_s;
    common::uniform_slice(0.0, raw_length_, num_of_sampled_points - 1,
                          &sample_s);

    constexpr double kMinRawReferenceLineLength = 120.0;
    ReferenceLinePoint prev_sample_point;
    bool is_first_point = true;
    for (const double s : sample_s) {
        if (s > raw_length_) {
            break;
        }

        size_t matched_index = find_matched_index(s);
        size_t next_index = matched_index + 1;
        if (next_index >= raw_points_.size()) {
            break;
        }

        ArcAdsNavReferenceLinePoint curr_raw_pt = raw_points_[matched_index];
        ArcAdsNavReferenceLinePoint next_raw_pt = raw_points_[next_index];
        const float s0 = curr_raw_pt.s;
        const float s1 = next_raw_pt.s;

        ReferenceLinePoint sample_point;
        sample_point.s = s;
        float interpolated_s = static_cast<float>(s);
        sample_point.x = static_cast<double>(math::lerp_float(
            curr_raw_pt.x, s0, next_raw_pt.x, s1, interpolated_s));
        sample_point.y = static_cast<double>(math::lerp_float(
            curr_raw_pt.y, s0, next_raw_pt.y, s1, interpolated_s));
        sample_point.heading = static_cast<double>(math::lerp_float(
            curr_raw_pt.yaw, s0, next_raw_pt.yaw, s1, interpolated_s));

        // 如果不是第一个点，计算dheading
        if (!is_first_point) {
            double ds = sample_point.s - prev_sample_point.s;
            double dheading =
                std::abs(sample_point.heading - prev_sample_point.heading) / ds;

            // 如果dheading大于阈值，则break
            if (dheading > 0.2 && sample_point.s > kMinRawReferenceLineLength) {
                break;
            }
        }

        sample_point.distance_to_left_divider =
            static_cast<double>(math::lerp_float(
                curr_raw_pt.distance_to_left_divider, s0,
                next_raw_pt.distance_to_left_divider, s1, interpolated_s));
        sample_point.distance_to_right_divider =
            static_cast<double>(math::lerp_float(
                curr_raw_pt.distance_to_right_divider, s0,
                next_raw_pt.distance_to_right_divider, s1, interpolated_s));

        sample_point.distance_to_left_boundary =
            static_cast<double>(math::lerp_float(
                curr_raw_pt.distance_to_left_boundary, s0,
                next_raw_pt.distance_to_left_boundary, s1, interpolated_s));
        sample_point.distance_to_right_boundary =
            static_cast<double>(math::lerp_float(
                curr_raw_pt.distance_to_right_boundary, s0,
                next_raw_pt.distance_to_right_boundary, s1, interpolated_s));

        sample_point.left_divider_id = curr_raw_pt.left_divider_id;
        sample_point.right_divider_id = curr_raw_pt.right_divider_id;
        sample_point.left_divider_type =
            static_cast<LaneDividerType>(curr_raw_pt.left_divider_type);
        sample_point.right_divider_type =
            static_cast<LaneDividerType>(curr_raw_pt.right_divider_type);

        sample_point.left_boundary_id = curr_raw_pt.left_boundary_id;
        sample_point.right_boundary_id = curr_raw_pt.right_boundary_id;
        sample_point.left_boundary_type =
            static_cast<RoadBoundaryType>(curr_raw_pt.left_boundary_type);
        sample_point.right_boundary_type =
            static_cast<RoadBoundaryType>(curr_raw_pt.right_boundary_type);

        // 将当前点存入容器
        sampled_raw_points_.emplace_back(sample_point);

        // 更新上一个点
        prev_sample_point = sample_point;
        is_first_point = false;
    }

    return true;
}

}  // namespace planning
}  // namespace arcsoft
