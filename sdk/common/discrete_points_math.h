#pragma once

#include <numeric>
#include <utility>
#include <vector>

#include "common/basic_types.h"
#include "common/math/linear_interpolation.h"

namespace arcsoft {
namespace planning {

class DiscretePointsMath {
public:
    DiscretePointsMath() = delete;

    static bool ComputePathProfile(std::vector<ReferenceLinePoint>& xy_points);
    static bool ComputePathProfile(PathData& xy_points);
    static bool ComputeTrajectoryKappa(TrajectoryPoints& xy_points);
    static double ComputeTrajectoryMaxFabsKappa(TrajectoryPoints& xy_points);
    static double ComputeTrajectoryMeanFabsKappa(TrajectoryPoints& xy_points);
    static bool ComputeInterpolateTrajectory(
        TrajectoryPoints& xy_points, const double interpolation_interval = 0.5);
};

}  // namespace planning
}  // namespace arcsoft
