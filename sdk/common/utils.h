/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __UTILS_H__
#define __UTILS_H__

#include <chrono>
#include <ctime>
#include <stdexcept>
#include <vector>

namespace arcsoft {
namespace common {

inline double get_system_time() {
    auto now = std::chrono::system_clock::now();

    // 转换为从epoch开始的秒数（double类型）
    return std::chrono::duration<double>(now.time_since_epoch()).count();
}

inline uint64_t get_uint_current_time() {
    // 获取系统启动以来的时间（steady_clock 是 monotonic 时钟）
    auto now = std::chrono::steady_clock::now();

    // 转换为毫秒
    auto duration = now.time_since_epoch();
    uint64_t millis =
        std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();

    return millis;
}

/**
 * uniformly slice a segment [start, end] to num + 1 pieces
 * the result sliced will contain the n + 1 points that slices the provided
 * segment. `start` and `end` will be the first and last element in `sliced`.
 */
template <typename T>
void uniform_slice(const T start, const T end, uint32_t num,
                   std::vector<T>* sliced) {
    if (!sliced || num == 0) {
        return;
    }
    const T delta = (end - start) / num;
    sliced->resize(num + 1);
    T s = start;
    for (uint32_t i = 0; i < num; ++i, s += delta) {
        sliced->at(i) = s;
    }
    sliced->at(num) = end;
}

}  // namespace common
}  // namespace arcsoft

#endif /* __UTILS_H__ */