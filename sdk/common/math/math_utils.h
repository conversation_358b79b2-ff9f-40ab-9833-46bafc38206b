/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __MATH_UTILS_H__
#define __MATH_UTILS_H__

#include <algorithm>  // std::min / std::max

#include "common/vec2d.h"

using Vec2d = arcsoft::common::Vec2d;

namespace arcsoft {
namespace math {

template <class T>
static const T clamp(const T &v, const T &low, const T &high) {
    return std::min(std::max(v, low), high);
}

template <typename T>
static inline T Square(const T value) {
    return value * value;
}

static double CrossProd(const Vec2d &start_point, const Vec2d &end_point_1,
                        const Vec2d &end_point_2) {
    return (end_point_1 - start_point).CrossProd(end_point_2 - start_point);
}

static double WrapAngle(const double angle) {
    const double new_angle = std::fmod(angle, M_PI * 2.0);
    return new_angle < 0 ? new_angle + M_PI * 2.0 : new_angle;
}

static double NormalizeAngle(const double angle) {
    double a = std::fmod(angle + M_PI, 2.0 * M_PI);
    if (a < 0.0) {
        a += 2.0 * M_PI;
    }
    return a - M_PI;
}

inline bool equals_with_epsilon(
    double a, double b,
    double epsilon = std::numeric_limits<double>::epsilon() * 100) {
    // 处理NaN情况
    if (std::isnan(a) || std::isnan(b)) {
        return false;
    }

    // 处理无穷大情况
    if (std::isinf(a) || std::isinf(b)) {
        return a == b;
    }

    // 计算绝对误差
    double diff = std::fabs(a - b);

    // 如果两个数非常接近0，直接比较绝对值
    if (std::fabs(a) < 1e-10 && std::fabs(b) < 1e-10) {
        return diff < epsilon;
    }

    // 否则比较相对误差和绝对误差
    double magnitude = std::max(std::fabs(a), std::fabs(b));
    return diff <= epsilon || diff <= magnitude * epsilon;
}

inline double fast_atan2(const double dy_dx) {
    // 分段近似
    if ((dy_dx <= 5e-2) && (dy_dx >= -5e-2)) {
        return dy_dx;
    } else if ((dy_dx <= 8e-2) && (dy_dx >= -8e-2)) {
        return 0.9978 * dy_dx;
    } else if ((dy_dx <= 0.12) && (dy_dx >= -0.12)) {
        return 0.9952 * dy_dx;
    } else if ((dy_dx <= 0.15) && (dy_dx >= -0.15)) {
        return 0.9925 * dy_dx;
    } else if ((dy_dx <= 0.17) && (dy_dx >= -0.17)) {
        return 0.9905 * dy_dx;
    } else if ((dy_dx <= 0.19) && (dy_dx >= -0.19)) {
        return 0.9882 * dy_dx;
    } else if ((dy_dx <= 0.20) && (dy_dx >= -0.20)) {
        return 0.9870 * dy_dx;
    } else if ((dy_dx <= 0.22) && (dy_dx >= -0.22)) {
        return 0.9843 * dy_dx;
    } else {
        return std::atan2(dy_dx, 1.0);
    }
}

}  // namespace math
}  // namespace arcsoft

#endif /* __MATH_UTILS_H__ */
