#include "linear_interpolation.h"

namespace arcsoft {
namespace math {

double slerp(const double a0, const double t0, const double a1, const double t1,
             const double t) {
    if (std::abs(t1 - t0) <= arcsoft::common::kMathEpsilon) {
        AINFO << "input time difference is too small";
        return NormalizeAngle(a0);
    }
    const double a0_n = NormalizeAngle(a0);
    const double a1_n = NormalizeAngle(a1);
    double d = a1_n - a0_n;
    if (d > M_PI) {
        d = d - 2 * M_PI;
    } else if (d < -M_PI) {
        d = d + 2 * M_PI;
    }

    const double r = (t - t0) / (t1 - t0);
    const double a = a0_n + d * r;
    return NormalizeAngle(a);
}

ReferenceLinePoint InterpolateUsingLinearApproximation(
    const ReferenceLinePoint &p0, const ReferenceLinePoint &p1,
    const double s) {
    double s0 = p0.s;
    double s1 = p1.s;

    ReferenceLinePoint path_point;
    double weight = (s - s0) / (s1 - s0);
    double x = (1 - weight) * p0.x + weight * p1.x;
    double y = (1 - weight) * p0.y + weight * p1.y;
    double heading = slerp(p0.heading, p0.s, p1.heading, p1.s, s);
    double curvature = (1 - weight) * p0.curvature + weight * p1.curvature;
    double dcurvature = (1 - weight) * p0.dcurvature + weight * p1.dcurvature;

    //    AINFO << "p0.s: " << p0.s;
    //    AINFO << "p1.s: " << p1.s;
    //    AINFO << "weight: " << weight;
    //    AINFO << "x: " << x;
    //    AINFO << "y: " << y;

    path_point.x = x;
    path_point.y = y;
    path_point.heading = heading;
    path_point.s = s;
    path_point.curvature = curvature;
    path_point.dcurvature = dcurvature;

    return path_point;
}

PathPoint InterpolateUsingLinearApproximation(const PathPoint &p0,
                                              const PathPoint &p1,
                                              const double s) {
    double s0 = p0.s;
    double s1 = p1.s;

    PathPoint path_point;
    double weight = (s - s0) / (s1 - s0);
    double x = (1 - weight) * p0.x + weight * p1.x;
    double y = (1 - weight) * p0.y + weight * p1.y;
    double heading = slerp(p0.heading, p0.s, p1.heading, p1.s, s);
    double curvature = (1 - weight) * p0.curvature + weight * p1.curvature;
    double dcurvature = (1 - weight) * p0.dcurvature + weight * p1.dcurvature;
    double ddcurvature =
        (1 - weight) * p0.ddcurvature + weight * p1.ddcurvature;

    path_point.x = x;
    path_point.y = y;
    path_point.heading = heading;
    path_point.curvature = curvature;
    path_point.dcurvature = dcurvature;
    path_point.ddcurvature = ddcurvature;
    path_point.s = s;
    return path_point;
}

double x = 0.0;
double y = 0.0;
double heading = 0.0;
double curvature = 0.0;
double t = 0.0;
double v = 0.0;
double a = 0.0;
double jerk = 0.0;

TrajectoryPoint InterpolateUsingLinearApproximation(const TrajectoryPoint &p0,
                                                    const TrajectoryPoint &p1,
                                                    const double s) {
    double s0 = p0.s;
    double s1 = p1.s;

    TrajectoryPoint trajectory_point;
    double weight = (s - s0) / (s1 - s0);
    double x = (1 - weight) * p0.x + weight * p1.x;
    double y = (1 - weight) * p0.y + weight * p1.y;
    double heading = slerp(p0.heading, p0.s, p1.heading, p1.s, s);
    double curvature = (1 - weight) * p0.curvature + weight * p1.curvature;
    double t = (1 - weight) * p0.t + weight * p1.t;
    double v = (1 - weight) * p0.v + weight * p1.v;
    double a = (1 - weight) * p0.a + weight * p1.a;
    double jerk = (1 - weight) * p0.jerk + weight * p1.jerk;

    trajectory_point.x = x;
    trajectory_point.y = y;
    trajectory_point.heading = heading;
    trajectory_point.curvature = curvature;
    trajectory_point.t = t;
    trajectory_point.v = v;
    trajectory_point.s = s;
    trajectory_point.a = a;
    trajectory_point.jerk = jerk;
    return trajectory_point;
}

TrajectoryPoints InterpolateTrajectory(const TrajectoryPoints &raw_path,
                                       double interpolation_interval) {
    TrajectoryPoints interpolated_path;
    if (raw_path.size() < 2 || interpolation_interval <= 0.0) {
        return raw_path;
    }

    for (size_t i = 0; i < raw_path.size() - 1; ++i) {
        const TrajectoryPoint &p0 = raw_path[i];
        const TrajectoryPoint &p1 = raw_path[i + 1];

        double s0 = p0.s;
        double s1 = p1.s;
        double delta_s = s1 - s0;

        if (delta_s < 1e-6) continue;

        int steps = static_cast<int>(delta_s / interpolation_interval);

        for (int j = 0; j < steps; ++j) {
            double s_interp = s0 + j * interpolation_interval;
            interpolated_path.push_back(
                InterpolateUsingLinearApproximation(p0, p1, s_interp));
        }
    }

    // 保证末尾点一定被包含
    interpolated_path.push_back(raw_path.back());
    return interpolated_path;
}

}  // namespace math
}  // namespace arcsoft
