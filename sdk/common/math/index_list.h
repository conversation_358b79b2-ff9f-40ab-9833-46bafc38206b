/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __INDEX_LIST_H__
#define __INDEX_LIST_H__

#include <iostream>
#include <memory>
#include <unordered_map>
#include <vector>

namespace arcsoft {
namespace math {

template <typename I, typename T>
class IndexedList {
public:
    /**
     * @brief copy object into the container. If the id is already exist,
     * overwrite the object in the container.
     * @param id the id of the object
     * @param object the const reference of the objected to be copied to the
     * container.
     * @return The pointer to the object in the container.
     */
    std::shared_ptr<T> Add(const I id, const T& object) {
        auto obs = Find(id);
        if (obs) {
            *obs = object;
            return obs;
        } else {
            auto obj_sp = std::make_shared<T>(object);
            object_dict_.insert({id, obj_sp});
            object_list_.push_back(obj_sp);
            return obj_sp;
        }
    }

    /**
     * @brief Find object by id in the container
     * @param id the id of the object
     * @return the raw pointer to the object if found.
     * @return nullptr if the object is not found.
     */
    std::shared_ptr<T> Find(const I id) const {
        auto it = object_dict_.find(id);
        return (it != object_dict_.end()) ? it->second : nullptr;
    }

    /**
     * @brief Get read-only access to all items
     */
    const std::vector<std::shared_ptr<T>>& Items() const {
        return object_list_;
    }

    /**
     * @brief Get mutable access to all items
     */
    std::vector<std::shared_ptr<T>>& MutableItems() { return object_list_; }

    /**
     * @brief Get read-only dictionary view
     */
    const std::unordered_map<I, std::shared_ptr<T>>& Dict() const {
        return object_dict_;
    }

    /**
     * @brief Get mutable dictionary view
     */
    std::unordered_map<I, std::shared_ptr<T>>& MutableDict() {
        return object_dict_;
    }

    /**
     * @brief Copy the container with objects.
     */
    IndexedList& operator=(const IndexedList& other) {
        if (&other == this) {
            return *this;
        }
        object_list_.clear();
        object_dict_.clear();
        for (const auto& item : other.Dict()) {
            Add(item.first, *item.second);
        }
        return *this;
    }

    /**
     * @brief Clear all objects from the container
     */
    void Clear() {
        object_dict_.clear();
        object_list_.clear();
    }

    /**
     * @brief Get number of elements
     */
    size_t Size() const { return object_list_.size(); }

private:
    std::vector<std::shared_ptr<T>> object_list_;
    std::unordered_map<I, std::shared_ptr<T>> object_dict_;
};

}  // namespace math
}  // namespace arcsoft

#endif /* __INDEX_LIST_H__ */