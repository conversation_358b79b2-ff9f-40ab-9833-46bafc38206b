#pragma once

#include "common/basic_types.h"
#include "linear_interpolation.h"

namespace arcsoft {
namespace math {

using namespace arcsoft::planning;

class PathMatcher {
public:
    PathMatcher() = delete;

    static ReferenceLinePoint MatchToPath(
        const std::vector<ReferenceLinePoint>& reference_line, const double x,
        const double y, const bool use_projection = true);

    static std::pair<double, double> GetPathFrenetCoordinate(
        const std::vector<ReferenceLinePoint>& reference_line, const double x,
        const double y);

    static ReferenceLinePoint MatchToPath(
        const std::vector<ReferenceLinePoint>& reference_line, const double s);

private:
    static ReferenceLinePoint FindProjectionPoint(const ReferenceLinePoint& p0,
                                                  const ReferenceLinePoint& p1,
                                                  const double x,
                                                  const double y);
};

}  // namespace math
}  // namespace arcsoft
