/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "frenet_coordinate_system.h"

namespace arcsoft {
namespace planning {

using namespace arcsoft::math;

bool FrenetCoordinateSystem::CartCoord2FrenetCoord(const Point2D& cart,
                                                   SLPoint& frenet) const {
    if (reference_line_point_.size() < 2) {
        return false;
    }

    // // temp
    // for (const auto& pt : reference_line_point_) {
    //     AINFO << "reference point x: " << pt.x << " y: " << pt.y
    //           << " s: " << pt.s << " heading: " << pt.heading
    //           << " curvature: " << pt.curvature
    //           << " dcurvature: " << pt.dcurvature;
    // }

    const ReferenceLinePoint& projection_point =
        PathMatcher::MatchToPath(reference_line_point_, cart.x, cart.y);
    const double dx = cart.x - projection_point.x;
    const double dy = cart.y - projection_point.y;

    // AINFO << "projection_point x: " << projection_point.x
    //       << " y: " << projection_point.y << " s: " << projection_point.s
    //       << " heading: " << projection_point.heading
    //       << " curvature: " << projection_point.curvature
    //       << " dcurvature: " << projection_point.dcurvature;

    //    size_t index = 50;
    //    const ReferenceLinePoint& projection_point =
    //    PathMatcher::MatchToPath(reference_line_point_,
    //    reference_line_point_[index].x, reference_line_point_[index].y); const
    //    double dx = reference_line_point_[index].x - projection_point.x; const
    //    double dy = reference_line_point_[index].y - projection_point.y;

    const double cos_theta_r = std::cos(projection_point.heading);
    const double sin_theta_r = std::sin(projection_point.heading);
    const double cross_rd_nd = cos_theta_r * dy - sin_theta_r * dx;

    frenet.s = projection_point.s;
    frenet.l = std::copysign(std::sqrt(dx * dx + dy * dy), cross_rd_nd);

    // AINFO << "frenet.s: " << frenet.s;
    // AINFO << "frenet.d: " << frenet.l;

    return true;
}

bool FrenetCoordinateSystem::FrenetCoord2CartCoord(const SLPoint& frenet,
                                                   Point2D& cart) const {
    // 投影点，此坐标属性信息为笛卡尔坐标系
    const ReferenceLinePoint& projection_point =
        PathMatcher::MatchToPath(reference_line_point_, frenet.s);

    const double cos_theta_r = std::cos(projection_point.heading);
    const double sin_theta_r = std::sin(projection_point.heading);

    cart.x = projection_point.x - sin_theta_r * frenet.l;
    cart.y = projection_point.y + cos_theta_r * frenet.l;

    return true;
}

bool FrenetCoordinateSystem::CartCoord2FrenetCoord(
    const double x, const double y, const double v, const double a,
    const double theta, const double kappa,
    std::array<double, 3>* const ptr_s_condition,
    std::array<double, 3>* const ptr_d_condition) const {
    const ReferenceLinePoint& projection_point =
        PathMatcher::MatchToPath(reference_line_point_, x, y);
    double rx = projection_point.x;
    double ry = projection_point.y;
    double rtheta = projection_point.heading;
    double rs = projection_point.s;
    double rkappa = projection_point.curvature;
    double rdkappa = projection_point.dcurvature;

    const double dx = x - rx;
    const double dy = y - ry;

    const double cos_theta_r = std::cos(rtheta);
    const double sin_theta_r = std::sin(rtheta);

    const double cross_rd_nd = cos_theta_r * dy - sin_theta_r * dx;
    ptr_d_condition->at(0) =
        std::copysign(std::sqrt(dx * dx + dy * dy), cross_rd_nd);

    const double delta_theta = theta - rtheta;
    const double tan_delta_theta = std::tan(delta_theta);
    const double cos_delta_theta = std::cos(delta_theta);

    const double one_minus_kappa_r_d = 1 - rkappa * ptr_d_condition->at(0);
    ptr_d_condition->at(1) = one_minus_kappa_r_d * tan_delta_theta;

    const double kappa_r_d_prime =
        rdkappa * ptr_d_condition->at(0) + rkappa * ptr_d_condition->at(1);

    ptr_d_condition->at(2) =
        -kappa_r_d_prime * tan_delta_theta +
        one_minus_kappa_r_d / cos_delta_theta / cos_delta_theta *
            (kappa * one_minus_kappa_r_d / cos_delta_theta - rkappa);

    ptr_s_condition->at(0) = rs;

    ptr_s_condition->at(1) = v * cos_delta_theta / one_minus_kappa_r_d;

    const double delta_theta_prime =
        one_minus_kappa_r_d / cos_delta_theta * kappa - rkappa;
    ptr_s_condition->at(2) =
        (a * cos_delta_theta -
         ptr_s_condition->at(1) * ptr_s_condition->at(1) *
             (ptr_d_condition->at(1) * delta_theta_prime - kappa_r_d_prime)) /
        one_minus_kappa_r_d;

    return true;
}

bool FrenetCoordinateSystem::FrenetCoord2CartCoord(
    const SLPoint& frenet, const std::array<double, 3>& s_condition,
    const std::array<double, 3>& d_condition, double* const ptr_x,
    double* const ptr_y, double* const ptr_theta, double* const ptr_kappa,
    double* const ptr_v, double* const ptr_a) {
    // 投影点，此坐标属性信息为笛卡尔坐标系
    const ReferenceLinePoint& projection_point =
        PathMatcher::MatchToPath(reference_line_point_, frenet.s);
    double rx = projection_point.x;
    double ry = projection_point.y;
    double rtheta = projection_point.heading;
    double rs = projection_point.s;
    double rkappa = projection_point.curvature;
    double rdkappa = projection_point.dcurvature;

    if (std::abs(rs - s_condition[0]) < 1.0e-6) {
        AINFO << "The reference point s and s_condition[0] don't match";
    }

    const double cos_theta_r = std::cos(rtheta);
    const double sin_theta_r = std::sin(rtheta);

    *ptr_x = rx - sin_theta_r * d_condition[0];
    *ptr_y = ry + cos_theta_r * d_condition[0];

    const double one_minus_kappa_r_d = 1 - rkappa * d_condition[0];

    const double tan_delta_theta = d_condition[1] / one_minus_kappa_r_d;
    const double delta_theta = std::atan2(d_condition[1], one_minus_kappa_r_d);
    const double cos_delta_theta = std::cos(delta_theta);

    *ptr_theta = NormalizeAngle(delta_theta + rtheta);

    const double kappa_r_d_prime =
        rdkappa * d_condition[0] + rkappa * d_condition[1];
    *ptr_kappa = (((d_condition[2] + kappa_r_d_prime * tan_delta_theta) *
                   cos_delta_theta * cos_delta_theta) /
                      (one_minus_kappa_r_d) +
                  rkappa) *
                 cos_delta_theta / (one_minus_kappa_r_d);

    const double d_dot = d_condition[1] * s_condition[1];
    *ptr_v = std::sqrt(one_minus_kappa_r_d * one_minus_kappa_r_d *
                           s_condition[1] * s_condition[1] +
                       d_dot * d_dot);

    const double delta_theta_prime =
        one_minus_kappa_r_d / cos_delta_theta * (*ptr_kappa) - rkappa;

    *ptr_a = s_condition[2] * one_minus_kappa_r_d / cos_delta_theta +
             s_condition[1] * s_condition[1] / cos_delta_theta *
                 (d_condition[1] * delta_theta_prime - kappa_r_d_prime);

    return true;
}

// 获取参考路径在s值处的航向角
double FrenetCoordinateSystem::GetRefCurveHeading(double s) const {
    if (s < 0.0 || s > total_path_length_) {
        throw std::out_of_range("s value is out of range");
    }

    size_t segment_index = FindSegmentIndex(s);
    const Point2D& p1 = {reference_line_point_[segment_index].x,
                         reference_line_point_[segment_index].y};
    const auto& p_temp = reference_line_point_[std::min(
        segment_index + 1, reference_line_point_.size() - 1)];
    const Point2D& p2 = {p_temp.x, p_temp.y};

    double dx = p2.x - p1.x;
    double dy = p2.y - p1.y;

    if (dx == 0.0 && dy == 0.0) {
        return 0.0;  // 或抛出异常
    }

    return std::atan2(dy, dx);
}

// 根据s值找到对应的路径段索引
size_t FrenetCoordinateSystem::FindSegmentIndex(double s) const {
    // 使用二分查找
    size_t left = 0;
    size_t right = path_lengths_.size() - 1;

    while (left < right) {
        size_t mid = left + (right - left) / 2;
        if (path_lengths_[mid] < s) {
            left = mid + 1;
        } else {
            right = mid;
        }
    }

    // 检查是否超出范围
    if (left >= path_lengths_.size()) {
        return path_lengths_.size() - 1;
    }

    // 检查前一个点
    if (left > 0 && s < path_lengths_[left] - 1e-6) {
        return left - 1;
    }

    return left;
}

// 预计算路径累积长度
void FrenetCoordinateSystem::PrecomputePathLengths() {
    path_lengths_.clear();
    double cumulative_length = 0.0;

    for (size_t i = 1; i < reference_line_point_.size(); ++i) {
        const Point2D& p1 = {reference_line_point_[i - 1].x,
                             reference_line_point_[i - 1].y};
        const Point2D& p2 = {reference_line_point_[i].x,
                             reference_line_point_[i].y};
        cumulative_length += Distance(p1, p2);
        path_lengths_.push_back(cumulative_length);
    }

    // 最后一个点的累积长度即为总路径长度
    total_path_length_ = cumulative_length;
}

bool FrenetCoordinateSystem::GetLateralOffsetAndHeading(
    const double& x, const double& y, const double& heading,
    double& lateral_offset, double& heading_offset) const {
    // 使用 PathMatcher 找到离 (x, y) 最近的参考线点
    const ReferenceLinePoint& projection_point =
        PathMatcher::MatchToPath(reference_line_point_, x, y);

    // 检查 s 是否在有效范围内
    if (projection_point.s < 0.0 || projection_point.s > total_path_length_) {
        AERROR << "Projection point s value out of range.";
        return false;
    }

    // 提取投影点信息
    const double rx = projection_point.x;
    const double ry = projection_point.y;
    const double rtheta = projection_point.heading;

    // 计算向量差
    const double dx = x - rx;
    const double dy = y - ry;

    // 计算横向偏移的符号（通过向量叉积）
    const double cos_rtheta = std::cos(rtheta);
    const double sin_rtheta = std::sin(rtheta);
    const double cross = cos_rtheta * dy - sin_rtheta * dx;

    // 计算横向偏移的大小并保留符号
    const double distance = std::sqrt(dx * dx + dy * dy);
    lateral_offset = std::copysign(distance, cross);

    // 获取投影点的航向角
    heading_offset = std::abs(rtheta - heading);

    return true;
}

double FrenetCoordinateSystem::CalculateTheta(const double rtheta,
                                              const double rkappa,
                                              const double l, const double dl) {
    return NormalizeAngle(rtheta + std::atan2(dl, 1 - l * rkappa));
}

double FrenetCoordinateSystem::CalculateKappa(const double rkappa,
                                              const double rdkappa,
                                              const double l, const double dl,
                                              const double ddl) {
    double denominator = (dl * dl + (1 - l * rkappa) * (1 - l * rkappa));
    if (std::fabs(denominator) < 1e-8) {
        return 0.0;
    }
    denominator = std::pow(denominator, 1.5);
    const double numerator = rkappa + ddl - 2 * l * rkappa * rkappa -
                             l * ddl * rkappa +
                             l * l * rkappa * rkappa * rkappa +
                             l * dl * rdkappa + 2 * dl * dl * rkappa;
    return numerator / denominator;
}

}  // namespace planning
}  // namespace arcsoft
