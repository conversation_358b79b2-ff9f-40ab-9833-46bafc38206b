/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __FORWARD_SIMULATOR_H__
#define __FORWARD_SIMULATOR_H__

#include "common/basic_types.h"
#include "common/frenet_coordinate_system.h"
#include "common/local_view.h"
#include "common/log.h"
#include "common/math/math_utils.h"
#include "discrete_points_math.h"

namespace arcsoft {
namespace planning {

constexpr double kSimTimeStep = 0.2;
constexpr double kInverseSimTimeStep = 1.0 / kSimTimeStep;

class LateralModel {
public:
    static constexpr size_t STATE_NUM = 5;
    static constexpr size_t CHANGED_DOT_NUM_IN_RK4 = 3;
    enum StateIdx { X = 0, Y = 1, THETA = 2, DELTA = 3, V = 4 };

public:
    LateralModel() = default;
    ~LateralModel() = default;

    static void CalNextState(const AgentParam &params, const Control &control,
                             const double dt, State *state, const double &v) {
        if (state == nullptr) {
            return;
        }
        const double x = state->x;
        const double y = state->y;
        const double theta = state->heading;
        const double delta = state->steer;
        const double L = params.wheel_base;
        const double gamma = control.steer_rate;
        const double a = control.acc;
        const auto x_t =
            CalRK4Kinematic(x, y, theta, delta, v, gamma, a, dt, L);
        auto ret = ToState(x_t);

        state->x = x_t[X];
        state->y = x_t[Y];
        state->heading = x_t[THETA];
        state->steer = x_t[DELTA];
        state->v = x_t[V];
        state->curvature = std::tan(ret.steer) / L;
        state->acc = a;
        state->jerk = (a - state->acc) / dt;
    }
    static inline State ToState(const std::array<double, STATE_NUM> &x_t) {
        return {x_t[X], x_t[Y], x_t[THETA], 0.0, x_t[DELTA],
                0.0,    x_t[V], 0.0,        0.0};
    }

    static std::array<double, STATE_NUM> CalRK4Kinematic(
        const double x, const double y, const double theta, const double delta,
        const double v, const double gamma, const double a, const double dt,
        const double L) {
        std::array<double, CHANGED_DOT_NUM_IN_RK4> k1{};
        std::array<double, CHANGED_DOT_NUM_IN_RK4> k2{};
        std::array<double, CHANGED_DOT_NUM_IN_RK4> k3{};
        std::array<double, CHANGED_DOT_NUM_IN_RK4> k4{};
        k1.fill(0.0), k2.fill(0.0), k3.fill(0.0), k4.fill(0.0);
        k1[X] = v * std::cos(theta);
        k1[Y] = v * std::sin(theta);
        k1[THETA] = v * std::tan(delta) / L;

        const double v1 = v + dt * a * 0.5;
        const double theta1 = theta + dt * 0.5 * k1[THETA];
        const double delta1 = delta + dt * 0.5 * gamma;
        k2[X] = v1 * std::cos(theta1);
        k2[Y] = v1 * std::sin(theta1);
        k2[THETA] = v1 * std::tan(delta1) / L;

        const double v2 = v1;
        const double theta2 = theta + dt * 0.5 * k2[THETA];
        k3[X] = v2 * std::cos(theta2);
        k3[Y] = v2 * std::sin(theta2);
        k3[THETA] = k2[THETA];

        const double v3 = v + dt * a;
        const double theta3 = theta + dt * k3[THETA];
        const double delta3 = delta + dt * gamma;
        k4[X] = v3 * std::cos(theta3);
        k4[Y] = v3 * std::sin(theta3);
        k4[THETA] = v3 * std::tan(delta3) / L;

        std::array<double, STATE_NUM> x_t{x, y, theta, delta3, v3};
        x_t[X] += (k1[X] + 2.0 * k2[X] + 2.0 * k3[X] + k4[X]) * dt / 6.0;
        x_t[Y] += (k1[Y] + 2.0 * k2[Y] + 2.0 * k3[Y] + k4[Y]) * dt / 6.0;
        x_t[THETA] +=
            (k1[THETA] + 2.0 * k2[THETA] + 2.0 * k3[THETA] + k4[THETA]) * dt /
            6.0;
        return x_t;
    }

private:
};

class ForwardSimulation {
public:
    static double CalPreviewControlSteer(const double angle_diff,
                                         const double look_ahead_dist,
                                         const double wheelbase_len) {
        return std::atan2(2.0 * wheelbase_len * std::sin(angle_diff),
                          look_ahead_dist);
    }

    static double CalDesireL(const double &look_ahead_dist,
                             const SimTrajPoint &traj_point,
                             const TrajectoryPoints &traj) {
        auto cmp = [](const double b, const TrajectoryPoint &a) {
            return b <= a.s;
        };
        auto it =
            std::upper_bound(traj.begin(), traj.end(),
                             traj_point.frenet_point.s + look_ahead_dist, cmp);
        int index = static_cast<int>(std::distance(traj.begin(), it));
        if (it == traj.end()) {
            index = static_cast<int>(traj.size()) - 1;
        }
        if (index < 0) {
            return 0.0;
        }
        double s_begin = traj_point.frenet_point.s;
        double l_begin = traj_point.frenet_point.l;
        double s_end = traj.at(static_cast<unsigned long>(index)).s;
        double l_end = traj[static_cast<unsigned long>(index)].l;

        double t = 1.0;
        if (std::abs(s_end - s_begin) > 0.001) {
            t = look_ahead_dist / (s_end - s_begin);
        }
        if (s_end < s_begin) {
            t = 0.0;
        }
        return l_begin + (l_end - l_begin) * t;
    }

    static bool CalcualateSteer(const FrenetCoordinateSystem *frenet_coord,
                                const AgentParam &params,
                                const SimTrajPoint &traj_point,
                                const TrajectoryPoints &target_frenet,
                                double *steer, const double target_l0 = 0.0,
                                const bool is_lane_change = false) {
        if (steer == nullptr) {
            return false;
        }
        const auto &state = traj_point.state;
        double look_ahead_dist = math::clamp(params.preview_time * state.v,
                                             params.look_ahead_distance_min,
                                             params.look_ahead_distance_max);

        SLPoint desire_sl{};
        Point2D desire_cart{};
        // AINFO << "[steve add] traj_point.frenet_point.s: "
        //       << traj_point.frenet_point.s
        //       << " look_ahead_dist:  " << look_ahead_dist
        //       << " preview_time: " << params.preview_time << " v: " <<
        //       state.v
        //       << " params.look_ahead_distance_min: "
        //       << params.look_ahead_distance_min
        //       << " params.look_ahead_distance_max: "
        //       << params.look_ahead_distance_max;
        desire_sl.s = traj_point.frenet_point.s + look_ahead_dist;
        desire_sl.l = is_lane_change ? CalDesireL(look_ahead_dist, traj_point,
                                                  target_frenet)
                                     : target_l0;
        // AINFO << "[steve add] desire_sl.s: " << desire_sl.x
        //       << " , l: " << desire_sl.y;
        if (frenet_coord->FrenetCoord2CartCoord(desire_sl, desire_cart) ==
            false) {
            return false;
        }

        // AINFO << "[steve add] desire_cart.x: " << desire_cart.x
        //       << " , y: " << desire_cart.y;

        const double dx = desire_cart.x - state.x;
        const double dy = desire_cart.y - state.y;
        look_ahead_dist = std::hypot(dx, dy);
        double angle_diff =
            math::NormalizeAngle(std::atan2(dy, dx) - state.heading);

        *steer = CalPreviewControlSteer(angle_diff, look_ahead_dist,
                                        params.wheel_base);

        // AINFO << "[steve add] steer: " << *steer;

        return true;
    }

    static bool Simulation(const FrenetCoordinateSystem *frenet_coord,
                           const AgentPose &ego_pose,
                           const double total_sim_time,
                           const std::vector<double> &v_array,
                           const TrajectoryPoints &target_frenet,
                           TrajectoryPoints &sim_trajectory) {
        sim_trajectory.clear();
        const std::size_t total_size =
            static_cast<std::size_t>(total_sim_time * kInverseSimTimeStep);
        const double min_vel = 1.0;
        const std::size_t min_acceptable_size = 20;
        sim_trajectory.reserve(total_size);
        if (target_frenet.empty()) {
            return false;
        }
        SimTrajPoint traj_point{};
        SimTrajPoint last_traj_point{};
        ToTrajPt(ego_pose.state, &traj_point);

        SLPoint ego_sl{};
        TrajectoryPoint traj_pt{};
        if (frenet_coord->CartCoord2FrenetCoord(
                {traj_point.state.x, traj_point.state.y}, ego_sl) == false) {
            return false;
        }
        // AINFO << "[steve add] ego_sl.s: " << ego_sl.x << " , l: " <<
        // ego_sl.y;
        traj_point.frenet_point.s = ego_sl.s;
        traj_point.frenet_point.l = ego_sl.l;
        last_traj_point = traj_point;
        traj_pt.x = traj_point.state.x;
        traj_pt.y = traj_point.state.y;
        traj_pt.frenet_valid = true;
        traj_pt.s = traj_point.frenet_point.s;
        traj_pt.l = traj_point.frenet_point.l;
        traj_pt.heading = traj_point.state.heading;
        traj_pt.t = 0.0;
        sim_trajectory.emplace_back(traj_pt);

        double steer{};
        Control control{};
        for (std::size_t i = 1; (i < total_size) && (i < v_array.size()); ++i) {
            if (!CalcualateSteer(frenet_coord, ego_pose.param, traj_point,
                                 target_frenet, &steer, 0.0, false)) {
                break;
            }

            auto *state = &traj_point.state;
            control.steer_rate = (steer - state->steer) * kInverseSimTimeStep;
            LateralModel::CalNextState(ego_pose.param, control, kSimTimeStep,
                                       state, std::fmax(min_vel, v_array[i]));
            const auto &last_state = last_traj_point.state;
            const auto &last_frenet = last_traj_point.frenet_point;

            SLPoint next_sl{};
            Point2D next_cart{state->x, state->y};
            if (frenet_coord->CartCoord2FrenetCoord(next_cart, next_sl) ==
                false) {
                break;
            }
            traj_point.frenet_point.s = next_sl.s;
            traj_point.frenet_point.l = next_sl.l;
            last_traj_point = traj_point;

            traj_pt.x = traj_point.state.x;
            traj_pt.y = traj_point.state.y;
            traj_pt.frenet_valid = true;
            traj_pt.heading = traj_point.state.heading;
            traj_pt.s = traj_point.frenet_point.s;
            traj_pt.l = traj_point.frenet_point.l;
            traj_pt.t += kSimTimeStep;
            sim_trajectory.emplace_back(traj_pt);
        }
        // 新增轨迹的曲率
        DiscretePointsMath::ComputeTrajectoryKappa(sim_trajectory);
        return sim_trajectory.size() >= min_acceptable_size;
    }

    static inline void ToTrajPt(const State &ego_state,
                                SimTrajPoint *traj_point) {
        if (traj_point == nullptr) {
            return;
        }
        traj_point->state.x = ego_state.x;
        traj_point->state.y = ego_state.y;
        traj_point->state.heading = ego_state.heading;
        traj_point->state.curvature = ego_state.curvature;
        traj_point->state.v = ego_state.v;
        traj_point->state.acc = ego_state.acc;
        traj_point->state.jerk = ego_state.jerk;
        traj_point->state.steer = ego_state.steer;
    }
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __FORWARD_SIMULATOR_H__ */
