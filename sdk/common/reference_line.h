/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __REFERENCE_LINE_H__
#define __REFERENCE_LINE_H__

#include <memory>
#include <unordered_map>

#include "frenet_coordinate_system.h"
#include "log.h"

namespace arcsoft {
namespace planning {

class ReferenceLine {
public:
    ReferenceLine() = default;

    const std::vector<ArcAdsNavReferenceLinePoint>& raw_points() const {
        return raw_points_;
    }
    void set_raw_points(
        const std::vector<ArcAdsNavReferenceLinePoint>& raw_points);

    const std::vector<ReferenceLinePoint>& smoothed_points() const {
        return smoothed_points_;
    }
    void set_smoothed_points(
        const std::vector<ReferenceLinePoint>& smoothed_points);

    const bool is_valid() const { return is_valid_; }
    void set_is_valid(const bool is_valid) { is_valid_ = is_valid; }

    const uint32_t id() const { return id_; }
    void set_id(const uint32_t id) { id_ = id; }

    const uint32_t left_lane_id() const { return left_lane_id_; }
    void set_left_lane_id(const uint32_t left_lane_id) {
        left_lane_id_ = left_lane_id;
    }

    const uint32_t right_lane_id() const { return right_lane_id_; }
    void set_right_lane_id(const uint32_t right_lane_id) {
        right_lane_id_ = right_lane_id;
    }

    const FrenetCoordinateSystem* frenet_coord() const {
        return frenet_coord_.get();
    }

    FrenetCoordinateSystem* mutable_frenet_coord() {
        return frenet_coord_.get();
    }

    void add_frenet_obstacles(const FrenetObstacle& frenet_obstacles) {
        frenet_obstacles_.emplace_back(frenet_obstacles);
    }

    const std::vector<FrenetObstacle>& frenet_obstacles() const {
        return frenet_obstacles_;
    }

    void set_is_on_route(const bool is_on_route) { is_on_route_ = is_on_route; }

    const bool is_on_route() const { return is_on_route_; }

    bool GetSLBoundary(const std::vector<Point2D>& box,
                       double warm_start_s = -1.0);

    const SLBoundary adc_sl_boundary() const { return adc_sl_boundary_; }

    ReferenceLinePoint GetNearestReferencePoint(const double s) const;

    double LengthToEgo() const {
        if (smoothed_points_.empty()) {
            AERROR << "Reference line is empty.";
            return 0.0;
        }
        return smoothed_points_.back().s - frenet_coord_->ego_frenet_sl().s;
    }

    double Length() const {
        if (smoothed_points_.empty()) {
            return 0.0;
        }
        return smoothed_points_.back().s;
    }

    bool GetLaneWidth(const double s, double& curr_lane_left_width,
                      double& curr_lane_right_width) const;
    bool GetRoadWidth(const double s, double& curr_road_left_width,
                      double& curr_road_right_width) const;
    double GetRawLength() const { return raw_length_; }

    bool SampleRawPoints();

    double CalculateHausdorffDistance(
        const std::vector<ReferenceLinePoint>& last_current_lane_points);

    const std::vector<ReferenceLinePoint>& sampled_raw_points() const {
        return sampled_raw_points_;
    }

    void set_total_cost(const double total_cost) { total_cost_ = total_cost; }
    const double total_cost() const { return total_cost_; }

    const double hausdorff_dis() const { return hausdorff_dis_; }

    const bool FindNearestReferenceLine(
        const std::vector<std::shared_ptr<ReferenceLine>>& candidate_lanes,
        uint32_t& nearest_lane_id) const;

    const bool FindSideNearestReferenceLine(
        const std::vector<std::shared_ptr<ReferenceLine>>& candidate_lanes,
        const bool is_left_side, uint32_t& nearest_lane_id) const;

private:
    size_t find_matched_index(const double s) const;

    /**
     * @brief 找一个同当前reference_line最相似的reference_line
     *
     * @param candidate_lanes 候选的reference_line
     * @param nearest_lane_id 最相似的reference_line的id
     * @return const bool 是否找到最相似的reference_line
     */
    const bool EvaluateReferenceLineRawDistance(
        const std::vector<std::shared_ptr<ReferenceLine>>& candidate_lanes,
        std::unordered_map<uint32_t, double>& distance_map) const;

private:
    uint32_t id_ = 0;
    uint32_t left_lane_id_ = 0;
    uint32_t right_lane_id_ = 0;
    bool is_valid_ = false;
    std::shared_ptr<FrenetCoordinateSystem> frenet_coord_;
    std::vector<ArcAdsNavReferenceLinePoint> raw_points_;
    std::vector<ReferenceLinePoint> sampled_raw_points_;
    std::vector<ReferenceLinePoint> smoothed_points_;
    double raw_length_ = 0.0;
    double hausdorff_distance_ = 0.0;

    // 该条参考线上绑定的障碍物
    std::vector<FrenetObstacle> frenet_obstacles_;

    // 是否在导航线路上 [TODO] 后续考虑更多因素用on_route_dis替代
    bool is_on_route_ = false;

    //
    double hausdorff_dis_ = std::numeric_limits<double>::max();
    double total_cost_ = std::numeric_limits<double>::max();

    SLBoundary adc_sl_boundary_;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __REFERENCE_LINE_H__ */
