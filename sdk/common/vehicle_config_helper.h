/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __VEHICLE_CONFIG_HELPER_H__
#define __VEHICLE_CONFIG_HELPER_H__

#include "vehicle_param_config.h"

namespace arcsoft {
namespace common {

class VehicleConfigHelper {
public:
    // 获取单例实例（线程安全）
    static VehicleConfigHelper& Instance() {
        static VehicleConfigHelper instance;
        return instance;
    }

    // 初始化方法
    bool Init();

    // 获取配置
    const VehicleParamConfig& GetVehicleParam();

private:
    // 私有构造函数防止外部实例化
    VehicleConfigHelper() = default;

    // 删除拷贝构造函数和赋值运算符
    VehicleConfigHelper(const VehicleConfigHelper&) = delete;
    VehicleConfigHelper& operator=(const VehicleConfigHelper&) = delete;

    VehicleParamConfig vehicle_param_config_;
    bool is_init_ = false;
};

}  // namespace common
}  // namespace arcsoft

#endif /* __VEHICLE_CONFIG_HELPER_H__ */