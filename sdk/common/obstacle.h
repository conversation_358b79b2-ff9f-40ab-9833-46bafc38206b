/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __OBSTACLE_H__
#define __OBSTACLE_H__

#include "basic_types.h"

namespace arcsoft {

namespace planning {

class Obstacle {
public:
    Obstacle(int id, bool is_static, double x, double y, double heading,
             double speed, double acceleration, double length, double width,
             const TrajectoryPoints& trajectory, const Polygon2d& polygon)
        : id_(id)
        , is_static_(is_static)
        , center_x_(x)
        , center_y_(y)
        , heading_(heading)
        , speed_(speed)
        , acceleration_(acceleration)
        , length_(length)
        , width_(width)
        , trajectory_(trajectory)
        , polygon_(polygon) {}

    int id() const { return id_; }
    bool is_static() const { return is_static_; }
    double center_x() const { return center_x_; }
    double center_y() const { return center_y_; }
    double heading() const { return heading_; }
    double speed() const { return speed_; }
    double acceleration() const { return acceleration_; }
    double length() const { return length_; }
    double width() const { return width_; }
    const TrajectoryPoints& trajectory() const { return trajectory_; }
    const Polygon2d& polygon() const { return polygon_; }
    const STBoundary& path_st_boundary() const { return path_st_boundary_; }

    void set_id(int id) { id_ = id; }
    void set_is_static(bool is_static) { is_static_ = is_static; }
    void set_center_x(double x) { center_x_ = x; }
    void set_center_y(double y) { center_y_ = y; }
    void set_heading(double heading) { heading_ = heading; }
    void set_speed(double speed) { speed_ = speed; }
    void set_acceleration(double acceleration) { acceleration_ = acceleration; }
    void set_length(double length) { length_ = length; }
    void set_width(double width) { width_ = width; }
    void set_trajectory(const TrajectoryPoints& trajectory) {
        trajectory_ = trajectory;
    }
    void set_polygon(const Polygon2d& polygon) { polygon_ = polygon; }
    void set_path_st_boundary(const STBoundary& boundary) {
        path_st_boundary_ = boundary;
    }

    common::Polygon2d GetObstacleTrajectoryPolygon(
        const TrajectoryPoint& point) const;

    TrajectoryPoint GetObstacleTrajectoryPointByTime(double time) const;

private:
    int id_ = -1;
    bool is_static_ = false;
    double center_x_ = -1000.0;
    double center_y_ = -1000.0;
    double heading_ = 0.0;
    double speed_ = 0.0;
    double acceleration_ = 0.0;
    double length_ = 0.0;
    double width_ = 0.0;
    TrajectoryPoints trajectory_;
    Polygon2d polygon_;
    STBoundary path_st_boundary_;
};

}  // namespace planning

}  // namespace arcsoft

#endif /* __OBSTACLE_H__ */