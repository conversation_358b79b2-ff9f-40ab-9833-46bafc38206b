/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#pragma once

#ifndef __LOG_H__
#define __LOG_H__

// compile-time logging level
#define MLOG_ACTIVE_LEVEL MLOG_LEVEL_DEBUG

#define MLOG_MODULE arcads_planning
#include <mlog.h>
#define DLOG_MODULE arcads_planning
#include <time.h>

#include <chrono>
#include <cstring>
#include <dlog.hpp>
#include <iomanip>
#include <sstream>
#include <string>

inline const char* GetBaseFileName(const char* path) {
    const char* basename = strrchr(path, '/');
    return basename ? basename + 1 : path;
}

// 获取微秒级时间戳 (HH:MM:SS.uuuuuu)
inline std::string GetPreciseTime() {
    auto now = std::chrono::system_clock::now();
    auto us = std::chrono::duration_cast<std::chrono::microseconds>(
                  now.time_since_epoch()) %
              1000000;
    time_t t = std::chrono::system_clock::to_time_t(now);
    struct tm tm_time;
#ifdef _MSC_VER
    localtime_s(&tm_time, &t);
#else
    localtime_r(&t, &tm_time);
#endif

    std::ostringstream oss;
    oss << std::setfill('0') << std::setw(2) << tm_time.tm_hour << ":"
        << std::setw(2) << tm_time.tm_min << ":" << std::setw(2)
        << tm_time.tm_sec << "." << std::setw(6) << us.count();  // 6位微秒
    return oss.str();
}

// [新添加的函数] 将 MLogLevel 枚举转换为字符串
inline const char* GetSeverityString(MLogLevel level) {
    switch (level) {
        case MLOG_INFO:
            return "[INFO] ";
        case MLOG_WARN:
            return "[WARN] ";
        case MLOG_ERROR:
            return "[ERROR]";
        case MLOG_FATAL:
            return "[FATAL]";
        case MLOG_DEBUG:
            return "[DEBUG]";
        case MLOG_TRACE:
            return "[TRACE]";
        default:
            return "[UNKWN]";
    }
}

class MlogMessageWrapper {
public:
    MlogMessageWrapper(MLogLevel severity, const char* file, int line)
        : severity_(severity), file_(file), line_(line) {
        // [修改之处] 在时间和文件名之间，插入日志级别字符串
        buffer_ << GetPreciseTime() << " " << GetSeverityString(severity) << "["
                << GetBaseFileName(file) << ":" << line << "] ";
    }

    ~MlogMessageWrapper() {
        mlog_write(severity_, MLOG_TAG, file_, line_, buffer_.str().c_str());

        if (severity_ == MLOG_FATAL) {
            abort();
        }
    }

    // 重载 << 操作符，使其能接收所有可流式输出的类型
    template <typename T>
    MlogMessageWrapper& operator<<(const T& value) {
        buffer_ << value;
        return *this;
    }

private:
    std::ostringstream buffer_;
    const MLogLevel severity_;
    const char* file_;
    const int line_;
};

#define LOG_STREAM(severity) MlogMessageWrapper(severity, __FILE__, __LINE__)

// 定义标准日志宏，使用 mlog 的日志级别枚举
#define AINFO LOG_STREAM(MLOG_INFO)
#define AWARN LOG_STREAM(MLOG_WARN)
#define AERROR LOG_STREAM(MLOG_ERROR)
#define AFATAL LOG_STREAM(MLOG_FATAL)  // MLOG_FATAL 将触发 abort()

#endif  // __LOG_H__