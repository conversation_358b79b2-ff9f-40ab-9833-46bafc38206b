/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __BASIC_TYPES_H__
#define __BASIC_TYPES_H__

#include <algorithm>
#include <array>
#include <chrono>
#include <cmath>
#include <iostream>
#include <limits>
#include <vector>

#include "common/speed/st_boundary.h"
#include "local_view.h"
#include "polygon2d.h"

namespace arcsoft {
namespace planning {

using Polygon2d = common::Polygon2d;

struct SLPoint {
    double s = 0.0;
    double l = 0.0;
    SLPoint() = default;
    SLPoint(double _s, double _l) : s(_s), l(_l) {}
    void set_s(double _s) { s = _s; }
    void set_l(double _l) { l = _l; }
};

struct Point2D {
    double x = 0.0;
    double y = 0.0;
    Point2D() = default;
    Point2D(double _x, double _y) : x(_x), y(_y) {}
    bool operator==(const Point2D& o) const {
        return (std::fabs(x - o.x) < std::numeric_limits<double>::epsilon()) &&
               (std::fabs(y - o.y) < std::numeric_limits<double>::epsilon());
    }
    void set_x(double _x) { x = _x; }
    void set_y(double _y) { y = _y; }
};

enum class PerceptionObstacleType {
    UNKNOWN = 0,
    UNKNOWN_MOVABLE = 1,
    UNKNOWN_UNMOVABLE = 2,
    PEDESTRIAN = 3,  // Pedestrian, usually determined by moving behavior.
    BICYCLE = 4,     // bike, motor bike
    VEHICLE = 5,     // Passenger car or truck.
};

enum class ScenarioType {
    UNKNOWN = 0,
    SINGLE_LANE = 1,
    TWO_LANE_ROAD = 2,
    THREE_LANE_ROAD = 3
};

enum class LaneDividerType {
    UNKNOWN = 0,
    SOLID = 1,
    DASHED = 2,
    SOLID_DASHED = 3,
    DASHED_SOLID = 4,
    SOLID_SOLID = 5,
    DASHED_DASHED = 6
};

enum class RoadBoundaryType {
    UNKNOWN = 0,
    CURB = 1,
    BARRIER = 2,
    GUARDRAIL = 3,
    POLES = 4,
    VIRTUAL = 5
};

struct ReferenceLinePoint {
    double x = 0.0;
    double y = 0.0;
    double heading = 0.0;
    double s = 0.0;
    double curvature = 0.0;
    double dcurvature = 0.0;

    // 左右车道线信息
    uint32_t left_divider_id;            // 左车道线 ID，0 表示不存在
    uint32_t right_divider_id;           // 右车道线 ID，0 表示不存在
    LaneDividerType left_divider_type;   // 左车道线类型
    LaneDividerType right_divider_type;  // 右车道线类型
    double distance_to_left_divider = 0.0;  // 到左车道线的距离，单位：m
    double distance_to_right_divider = 0.0;  // 到右车道线的距离，单位：m

    // 道路边界线信息
    uint32_t left_boundary_id;   // 左道路边界线 ID，0 表示不存在
    uint32_t right_boundary_id;  // 右道路边界线 ID，0 表示不存在
    RoadBoundaryType left_boundary_type;   // 左道路边界线类型
    RoadBoundaryType right_boundary_type;  // 右道路边界线类型
    double distance_to_left_boundary = 0.0;  // 到左道路边界线的距离，单位：m
    double distance_to_right_boundary = 0.0;  // 到右道路边界线的距离，单位：m
};

struct PathPoint {
    double x = 0.0;
    double y = 0.0;
    double heading = 0.0;
    double curvature = 0.0;
    double dcurvature = 0.0;
    double ddcurvature = 0.0;
    double s = 0.0;
};

struct FrenetFramePoint {
    double s = 0.0;
    double l = 0.0;
    double dl = 0.0;
    double ddl = 0.0;
};

using FrenetFramePath = std::vector<FrenetFramePoint>;
using PathData = std::vector<PathPoint>;

struct TrajectoryPoint {
    // enu
    double x = 0.0;
    double y = 0.0;
    double heading = 0.0;
    double curvature = 0.0;
    double t = 0.0;
    double v = 0.0;
    double a = 0.0;
    double jerk = 0.0;

    // frenet
    double s = 0.0;
    double l = 0.0;
    bool frenet_valid = false;
};

using TrajectoryPoints = std::vector<TrajectoryPoint>;

struct STArray {
    std::vector<double> s{};
    std::vector<double> v{};
    std::vector<double> a{};
    std::vector<double> j{};
    void clear_all() {
        s.clear();
        v.clear();
        a.clear();
        j.clear();
    }
};

struct BehaviorSTPoint {
    double s = 0.0;
    double v = 0.0;
    double a = 0.0;
    double j = 0.0;
    double t = 0.0;
    BehaviorSTPoint() = default;
    BehaviorSTPoint(double _s, double _v, double _a, double _j, double _t)
        : s(_s), v(_v), a(_a), j(_j), t(_t){};
};

enum PlanningStatus {
    SUCCESS = 0,
    SCENARIO_STATE_MACHINE_FAILED = 1,
    BEHAVIOR_GENERATOR_FAILED = 2,
    PATH_OPTIMIZER_FAILED = 3,
    SPEED_PLANNING_FAILED = 4,
    SPEED_OPTIMIZER_FAILED = 5,
    TRAJECTORY_EVALUATOR_FAILED = 6,

    NOT_INITIALIZED = 10
};

enum ScenarioStateEnum {
    CRUISE_KEEP = 0,
    CRUISE_WAIT = 1,
    CRUISE_CHANGE = 2,
    CRUISE_BACK = 3
};

enum RequestSource {
    NO_REQUEST = 0,
    MANUAL_REQUEST = 1,
    NAVI_REQUEST = 2,
    OVERTAKE_REQUEST = 3
};

enum RequestType { NO_CHANGE = 0, LEFT_CHANGE = 1, RIGHT_CHANGE = 2 };

struct ManualRequestInfo {
    RequestType request_type = RequestType::NO_CHANGE;
    int call_cnt = 0;
};

struct LaneChangeDecision {
    bool is_valid = false;
    RequestSource request_source = RequestSource::NO_REQUEST;
    RequestType request_type = RequestType::NO_CHANGE;
    int call_cnt = 0;
};

struct StateTransitionContext {
    explicit StateTransitionContext(ScenarioStateEnum _source_state,
                                    ScenarioStateEnum _target_state,
                                    RequestType _request_type)
        : source_state(_source_state)
        , target_state(_target_state)
        , request_type(_request_type) {}

    ScenarioStateEnum source_state;
    ScenarioStateEnum target_state;
    int source_lane_id;
    int target_lane_id;

    RequestType request_type;
};
using StateTransitionContexts = std::vector<StateTransitionContext>;

struct Control {
    double steer_rate = 0.0;
    double acc = 0.0;
    Control() = default;
    Control(double _steer_rate, double _acc)
        : steer_rate(_steer_rate), acc(_acc) {}
};

struct State {
    double x = 0.0;
    double y = 0.0;
    double heading = 0.0;
    double curvature = 0.0;
    double steer = 0.0;
    double steer_rate = 0.0;
    double v = 0.0;
    double acc = 0.0;
    double jerk = 0.0;

    State() = default;
    State(double _x, double _y, double _heading, double _curvature,
          double _steer, double _steer_rate, double _v, double _acc,
          double _jerk)
        : x(_x)
        , y(_y)
        , heading(_heading)
        , curvature(_curvature)
        , steer(_steer)
        , steer_rate(_steer_rate)
        , v(_v)
        , acc(_acc)
        , jerk(_jerk) {}
};

struct FrenetPoint {
    double s = 0.0;
    double l = 0.0;
    double dl = 0.0;
    double ddl = 0.0;
    FrenetPoint() = default;
    FrenetPoint(double _s, double _l, double _dl, double _ddl)
        : s(_s), l(_l), dl(_dl), ddl(_ddl) {}
};

struct SimTrajPoint {
    uint64_t timestamp = 0;
    State state;
    FrenetPoint frenet_point;
    SimTrajPoint() = default;
    SimTrajPoint(uint64_t _timestamp, const State& _state,
                 const FrenetPoint& _frenet_point)
        : timestamp(_timestamp), state(_state), frenet_point(_frenet_point) {}
};

struct AgentParam {
    double preview_time = 2.0;
    double look_ahead_distance_min = 1.0;
    double look_ahead_distance_max = 20.0;
    double wheel_base = 2.87;
};

struct AgentPose {
    State state;
    AgentParam param;
};

struct LaneChangeInfo {
    double lc_time_pass = 0.0;
    double lc_time_remain = 7.0;
    double lc_time_total = 7.0;
    double lc_dist_pass = 0.0;
    double lc_dist_remain = 300.0;
    double lc_dist_total = 300.0;
    double lc_time_wait = 0.0;
};

struct BehaviorPoint {
    double x = -100.0;
    double y = 0.0;
    double heading = 0.0;
    double vel = 0.0;
    double a = 0.0;
    double heading_cos = 2.0;
    double heading_sin = 2.0;
    double s = 0.0;
    double l = 0.0;
    bool frenet_valid = false;
    double dx = 0.0;
    double dy = 0.0;
    double ds = 0.0;
    double dl = 0.0;
    double dheading = 0.0;
    double dx_local = 0.0;
    double dy_local = 0.0;
    double ratio = 1.0;
    double frenet_ratio = 1.0;
    double curvature = 0.0;
    std::vector<double> c_poly{0.0, 0.0, 0.0, 0.0};
};

struct BehaviorCandidate {
    int behavior_index = -1;
    bool valid = false;
    STArray st_samples;
    std::vector<BehaviorPoint> target_points{};
    std::vector<BehaviorPoint> trajectory;
    LaneChangeInfo lane_change_info{};
    double target_s = 0.0;
    double target_l = 0.0;
    double target_speed = 0.0;
};

struct SLBoundary {
    double start_s = 0.0;
    double end_s = 0.0;
    double start_l = 0.0;
    double end_l = 0.0;

    std::vector<SLPoint> boundary_point;

    void set_start_s(double s) { start_s = s; }
    void set_end_s(double s) { end_s = s; }
    void set_start_l(double l) { start_l = l; }
    void set_end_l(double l) { end_l = l; }
};

struct FrenetObstacle {
    int id = -1;
    bool is_static = false;

    double s = 0.0;
    double l = 0.0;
    SLBoundary sl_boundary;
    PerceptionObstacleType type = PerceptionObstacleType::UNKNOWN;
};

struct ScenarioStateInfo {
    ScenarioStateEnum target_scenario_state = ScenarioStateEnum::CRUISE_KEEP;
    RequestType turn_signal = NO_CHANGE;
    double turn_signal_on_time = 0.0;
};

enum LaneChangeStatus { UNINITIALIZED = 0, IN_PROCESS = 1, FINISHED = 2 };

struct SpeedPoint {
    double s = 0.0;
    double t = 0.0;
    double v = 0.0;
    double a = 0.0;
    double da = 0.0;
    SpeedPoint() = default;
    SpeedPoint(double _s, double _t, double _v, double _a, double _da)
        : s(_s), t(_t), v(_v), a(_a), da(_da) {}
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __BASIC_TYPES_H__ */