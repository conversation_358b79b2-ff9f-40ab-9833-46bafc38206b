/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "vehicle_config_helper.h"

#include <fstream>
#include <iostream>
#include <iterator>
#include <string>

#include "json/json.hpp"

namespace arcsoft {
namespace common {

using json = nlohmann::json;

bool VehicleConfigHelper::Init() {
    // // 1. 读取文件内容
    // std::ifstream file(VEHICLE_PARAM_CONFIG_PATH);
    // if (!file.is_open()) {
    //     A<PERSON>ROR << "[ERROR] Failed to open config file: "
    //            << VEHICLE_PARAM_CONFIG_PATH;
    //     return false;
    // }

    // std::string text_proto((std::istreambuf_iterator<char>(file)),
    //                        std::istreambuf_iterator<char>());

    // // 显式关闭文件
    // file.close();

    // // 2. 解析文本格式的Protobuf
    // if (!google::protobuf::TextFormat::ParseFromString(
    //         text_proto, &vehicle_param_config_)) {
    //     AERROR << "[ERROR] Invalid text proto format in file: "
    //            << VEHICLE_PARAM_CONFIG_PATH;
    //     return false;
    // }

    // 1. 打开并解析JSON文件
    vehicle_param_config_ = VehicleParamConfig();

    std::ifstream input_file(VEHICLE_PARAM_CONFIG_PATH);
    if (input_file.is_open()) {
        json json_data;
        input_file >> json_data;

        // 2. 创建VehicleParamConfig对象并赋值

        // 检查JSON中是否存在vehicle_param_config字段
        if (json_data.contains("vehicle_param_config")) {
            const auto& vehicle_param = json_data["vehicle_param_config"];

            // 为结构体成员赋值
            if (vehicle_param.contains("vehicle_id"))
                vehicle_param_config_.vehicle_id = vehicle_param["vehicle_id"];
            if (vehicle_param.contains("vehicle_name"))
                vehicle_param_config_.vehicle_name =
                    vehicle_param["vehicle_name"];
            if (vehicle_param.contains("front_edge_to_center"))
                vehicle_param_config_.front_edge_to_center =
                    vehicle_param["front_edge_to_center"];
            if (vehicle_param.contains("back_edge_to_center"))
                vehicle_param_config_.back_edge_to_center =
                    vehicle_param["back_edge_to_center"];
            if (vehicle_param.contains("left_edge_to_center"))
                vehicle_param_config_.left_edge_to_center =
                    vehicle_param["left_edge_to_center"];
            if (vehicle_param.contains("right_edge_to_center"))
                vehicle_param_config_.right_edge_to_center =
                    vehicle_param["right_edge_to_center"];
            if (vehicle_param.contains("length"))
                vehicle_param_config_.length = vehicle_param["length"];
            if (vehicle_param.contains("width"))
                vehicle_param_config_.width = vehicle_param["width"];
            if (vehicle_param.contains("height"))
                vehicle_param_config_.height = vehicle_param["height"];
            if (vehicle_param.contains("min_turn_radius"))
                vehicle_param_config_.min_turn_radius =
                    vehicle_param["min_turn_radius"];
            if (vehicle_param.contains("max_acceleration"))
                vehicle_param_config_.max_acceleration =
                    vehicle_param["max_acceleration"];
            if (vehicle_param.contains("max_deceleration"))
                vehicle_param_config_.max_deceleration =
                    vehicle_param["max_deceleration"];
            if (vehicle_param.contains("max_steer_angle"))
                vehicle_param_config_.max_steer_angle =
                    vehicle_param["max_steer_angle"];
            if (vehicle_param.contains("max_steer_angle_rate"))
                vehicle_param_config_.max_steer_angle_rate =
                    vehicle_param["max_steer_angle_rate"];
            if (vehicle_param.contains("min_steer_angle_rate"))
                vehicle_param_config_.min_steer_angle_rate =
                    vehicle_param["min_steer_angle_rate"];
            if (vehicle_param.contains("steer_ratio"))
                vehicle_param_config_.steer_ratio =
                    vehicle_param["steer_ratio"];
            if (vehicle_param.contains("wheel_base"))
                vehicle_param_config_.wheel_base = vehicle_param["wheel_base"];
        }
    }

    is_init_ = true;

    return true;
}

const VehicleParamConfig& VehicleConfigHelper::GetVehicleParam() {
    if (!is_init_) {
        Init();
    }
    return vehicle_param_config_;
}

}  // namespace common

}  // namespace arcsoft