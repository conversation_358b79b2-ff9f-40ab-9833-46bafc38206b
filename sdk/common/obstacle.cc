/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "obstacle.h"

namespace arcsoft {
namespace planning {

common::Polygon2d Obstacle::GetObstacleTrajectoryPolygon(
    const TrajectoryPoint& point) const {
    double delta_heading = point.heading - heading_;
    double cos_delta_heading = cos(delta_heading);
    double sin_delta_heading = sin(delta_heading);
    std::vector<common::Vec2d> polygon_point;
    polygon_point.reserve(polygon_.points().size());

    for (auto& iter : polygon_.points()) {
        double relative_x = iter.x() - center_x_;
        double relative_y = iter.y() - center_y_;
        double x = relative_x * cos_delta_heading -
                   relative_y * sin_delta_heading + point.x;
        double y = relative_x * sin_delta_heading +
                   relative_y * cos_delta_heading + point.y;
        polygon_point.emplace_back(x, y);
    }

    common::Polygon2d trajectory_point_polygon(polygon_point);
    return trajectory_point_polygon;
}

TrajectoryPoint Obstacle::GetObstacleTrajectoryPointByTime(double time) const {
    TrajectoryPoint result;

    if (is_static_ || trajectory_.empty()) {
        result.x = center_x_;
        result.y = center_y_;
        result.heading = heading_;
        result.v = 0.0;
        result.a = 0.0;
        result.t = time;
        return result;
    }

    if (time <= trajectory_.front().t) {
        return trajectory_.front();
    }

    if (time >= trajectory_.back().t) {
        return trajectory_.back();
    }

    for (size_t i = 0; i < trajectory_.size() - 1; ++i) {
        const auto& p0 = trajectory_[i];
        const auto& p1 = trajectory_[i + 1];

        if (time >= p0.t && time <= p1.t) {
            double alpha = (time - p0.t) / (p1.t - p0.t);
            result.x = p0.x + alpha * (p1.x - p0.x);
            result.y = p0.y + alpha * (p1.y - p0.y);
            result.heading = p0.heading + alpha * (p1.heading - p0.heading);
            result.v = p0.v + alpha * (p1.v - p0.v);
            result.a = p0.a + alpha * (p1.a - p0.a);
            result.t = time;
            return result;
        }
    }

    result.x = center_x_;
    result.y = center_y_;
    result.heading = heading_;
    result.v = speed_;
    result.a = acceleration_;
    result.t = time;
    return result;
}

}  // namespace planning

}  // namespace arcsoft
