/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __AUTO_DRIVE_MODE_STATE_MACHINE_H__
#define __AUTO_DRIVE_MODE_STATE_MACHINE_H__

#include <functional>
#include <memory>
#include <string>

#include "common/basic_types.h"
#include "framework/session.h"

namespace arcsoft {
namespace planning {

using Session = arcsoft::framework::Session;

inline std::string state_to_string(common::AutoDriveModeState state) {
    switch (state) {
        case common::AutoDriveModeState::OFF:
            return "OFF";
        case common::AutoDriveModeState::ACC:
            return "ACC";
        case common::AutoDriveModeState::LCC:
            return "LCC";
        case common::AutoDriveModeState::NOA:
            return "NOA";
        default:
            return "UNKNOWN";
    }
}

class AutoDriveModeStateMachine {
public:
    AutoDriveModeStateMachine(Session* session)
        : session_(session), current_state_(common::AutoDriveModeState::OFF) {}

    void Process();

    void SetStateChangeCallback(std::function<void(common::AutoDriveModeState,
                                                   common::AutoDriveModeState)>
                                    callback) {
        on_state_change = callback;
    }

    common::AutoDriveModeState GetCurrentState() const {
        return current_state_;
    }

private:
    bool check_system_succeed();
    bool check_acc_activated();
    bool check_acc_deactivated();

    bool check_lcc_activated();
    bool check_lcc_deactivated();

    bool check_noa_activated();
    bool check_noa_deactivated();

    common::AutoDriveModeState get_next_state();

private:
    Session* session_;
    common::AutoDriveModeState current_state_ = common::AutoDriveModeState::OFF;
    std::function<void(common::AutoDriveModeState, common::AutoDriveModeState)>
        on_state_change;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __AUTO_DRIVE_MODE_STATE_MACHINE_H__ */