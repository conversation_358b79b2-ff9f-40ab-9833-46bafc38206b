/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "auto_drive_mode_state_machine.h"

#include <cmath>  // for std::abs

#include "common/log.h"

namespace arcsoft {
namespace planning {

bool AutoDriveModeStateMachine::check_system_succeed() { return true; }

bool AutoDriveModeStateMachine::check_acc_activated() {
    const auto& local_view = session_->local_view();

    // [TODO] 底盘信号接入判断
    const bool is_acc_activation_triggered = false;

    const bool perception_obstacles_status =
        local_view.perception_obstacles_status;

    return is_acc_activation_triggered && perception_obstacles_status;
}

bool AutoDriveModeStateMachine::check_acc_deactivated() {
    const auto& local_view = session_->local_view();

    // [TODO] 底盘信号接入判断：是否用户踩下刹车
    const bool is_manual_brake_triggered = false;

    //[TODO] 底盘信号接入判断
    const bool is_acc_deactivation_triggered = false;

    const PlanningStatus& planning_status = session_->planning_status();
    const bool planning_failed = planning_status != PlanningStatus::SUCCESS;

    return is_manual_brake_triggered || is_acc_deactivation_triggered ||
           planning_failed;
}

bool AutoDriveModeStateMachine::check_lcc_activated() {
    const auto& local_view = session_->local_view();

    const bool perception_obstacles_status =
        local_view.perception_obstacles_status;

    const bool nav_model_status = local_view.nav_model_status;

    const bool prediction_status = local_view.prediction_status;

    // [TODO] 底盘信号接入判断
    const bool is_lcc_activation_triggered = false;

    const PlanningStatus& planning_status = session_->planning_status();
    const bool planning_succeed = planning_status == PlanningStatus::SUCCESS;

    return perception_obstacles_status && nav_model_status &&
           prediction_status && is_lcc_activation_triggered && planning_status;
}

bool AutoDriveModeStateMachine::check_lcc_deactivated() {
    // [TODO] 底盘信号接入判断：是否用户踩下刹车
    const bool is_manual_brake_triggered = false;
    const bool is_manual_steer_triggered = false;

    //[TODO] 底盘信号接入判断
    const bool is_lcc_deactivation_triggered = false;

    const PlanningStatus& planning_status = session_->planning_status();
    const bool planning_failed = planning_status != PlanningStatus::SUCCESS;

    return is_manual_brake_triggered || is_manual_steer_triggered ||
           is_lcc_deactivation_triggered || planning_failed;
}

bool AutoDriveModeStateMachine::check_noa_activated() {
    const auto& local_view = session_->local_view();

    const bool perception_obstacles_status =
        local_view.perception_obstacles_status;

    const bool nav_model_status = local_view.nav_model_status;

    const bool prediction_status = local_view.prediction_status;

    // [TODO] 底盘信号接入判断
    const bool is_noa_activation_triggered = false;

    const PlanningStatus& planning_status = session_->planning_status();
    const bool planning_succeed = planning_status == PlanningStatus::SUCCESS;

    return perception_obstacles_status && nav_model_status &&
           prediction_status && is_noa_activation_triggered && planning_status;
}

bool AutoDriveModeStateMachine::check_noa_deactivated() {
    //
    // [TODO] 做详细判断，切换至LCC、ACC、还是OFF
    return false;
}

common::AutoDriveModeState AutoDriveModeStateMachine::get_next_state() {
    switch (current_state_) {
        case common::AutoDriveModeState::OFF:
            if (check_noa_activated()) {
                return common::AutoDriveModeState::NOA;
            } else if (check_lcc_activated()) {
                return common::AutoDriveModeState::LCC;
            } else if (check_acc_activated()) {
                return common::AutoDriveModeState::ACC;
            }
            break;

        case common::AutoDriveModeState::ACC:
            if (check_acc_deactivated()) {
                return common::AutoDriveModeState::OFF;
            } else if (check_lcc_activated()) {
                return common::AutoDriveModeState::LCC;
            }
            break;

        case common::AutoDriveModeState::LCC:
            if (check_lcc_deactivated()) {
                return common::AutoDriveModeState::ACC;
            } else if (check_noa_activated()) {
                return common::AutoDriveModeState::NOA;
            }
            break;

        case common::AutoDriveModeState::NOA:
            if (check_noa_deactivated()) {
                return common::AutoDriveModeState::LCC;
            }
            break;

        default:
            break;
    }

    if (check_system_succeed() == false) {
        return common::AutoDriveModeState::OFF;
    }

    // 无状态变化
    return current_state_;
}

void AutoDriveModeStateMachine::Process() {
    common::AutoDriveModeState next_state = get_next_state();
    if (next_state != current_state_) {
        AINFO << "Auto Drive Mode State changed: "
              << state_to_string(current_state_) << " → "
              << state_to_string(next_state);

        if (on_state_change) {
            on_state_change(current_state_, next_state);
        }
        current_state_ = next_state;
    }

    session_->set_auto_drive_mode_state(current_state_);

    AINFO << "Current Auto Drive Mode State: "
          << state_to_string(current_state_);
}

}  // namespace planning
}  // namespace arcsoft