#pragma once

#include <string>
#include <utility>
#include <vector>

#include "common/basic_types.h"
#include "common/local_view.h"
#include "common/log.h"
#include "publishable_trajectory.h"

namespace arcsoft {
namespace planning {

const double replan_lateral_distance_threshold = 1.0;
const double replan_longitudinal_distance_threshold = 2.5;
const double replan_speed_diff_threshold = 2;
const double replan_accel_diff_threshold = 20;
const double replan_time_threshold = 7.0;
const double enable_trajectory_stitcher = true;

class TrajectoryStitcher {
public:
    TrajectoryStitcher() = delete;

    static std::vector<TrajectoryPoint> ComputeStitchingTrajectory(
        const common::VehicleChassis& vehicle_chassis,
        const common::VehicleState& vehicle_state,
        const double current_timestamp, const double planning_cycle_time,
        const size_t preserved_points_num, const bool replan_by_offset,
        const PublishableTrajectory* prev_trajectory,
        const bool is_simulation_mode, std::string* replan_reason);

    static std::vector<TrajectoryPoint> ComputeReinitStitchingTrajectoryByAll(
        const double planning_cycle_time,
        const common::VehicleChassis& vehicle_chassis,
        const common::VehicleState& vehicle_state);
    static std::vector<TrajectoryPoint>
    ComputeReinitStitchingTrajectoryByLatReplan(
        const double planning_cycle_time,
        const common::VehicleState& vehicle_state,
        const TrajectoryPoint& time_matched_point);
    static std::vector<TrajectoryPoint>
    ComputeReinitStitchingTrajectoryByLonReplan(
        const double planning_cycle_time,
        const common::VehicleChassis& vehicle_chassis,
        const common::VehicleState& vehicle_state,
        const std::vector<TrajectoryPoint>& stitching_trajectory);

    static bool need_replan_by_necessary_check(
        const common::VehicleState& vehicle_state,
        const double current_timestamp,
        const PublishableTrajectory* prev_trajectory,
        const bool is_simulation_mode, std::string* replan_reason,
        size_t* time_matched_index);

    static bool need_replan_by_control_interactive(
        const double current_timestamp, std::string* replan_reason);

private:
    static std::pair<double, double> ComputePositionProjection(
        const double x, const double y,
        const TrajectoryPoint& matched_trajectory_point);

    static TrajectoryPoint ComputeTrajectoryPointFromVehicleState(
        const double planning_cycle_time,
        const common::VehicleState& vehicle_state);
    static void RearCenteredKinematicBicycleModel(
        const double predicted_time_horizon,
        const common::VehicleState& cur_vehicle_state,
        common::VehicleState& predicted_vehicle_state);
};

}  // namespace planning
}  // namespace arcsoft
