#include "publishable_trajectory.h"

#include "common/log.h"

namespace arcsoft {
namespace planning {

PublishableTrajectory::PublishableTrajectory(
    const double header_time,
    const DiscretizedTrajectory& discretized_trajectory)
    : DiscretizedTrajectory(discretized_trajectory)
    , header_time_(header_time) {}

double PublishableTrajectory::header_time() const { return header_time_; }

}  // namespace planning
}  // namespace arcsoft
