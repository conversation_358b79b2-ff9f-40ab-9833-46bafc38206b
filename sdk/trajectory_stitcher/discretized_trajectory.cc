#include "discretized_trajectory.h"

#include <limits>

#include "common/log.h"
#include "common/math/linear_interpolation.h"

namespace arcsoft {
namespace planning {

DiscretizedTrajectory::DiscretizedTrajectory(
    const std::vector<TrajectoryPoint>& trajectory_points)
    : std::vector<TrajectoryPoint>(trajectory_points) {
    if (trajectory_points.empty())
        AINFO << "trajectory_points should NOT be empty()";
}

// TrajectoryPoint DiscretizedTrajectory::Evaluate(
//     const double relative_time) const {
//     auto comp = [](const TrajectoryPoint& p, const double relative_time) {
//         return p.t < relative_time;
//     };

//     auto it_lower = std::lower_bound(begin(), end(), relative_time, comp);

//     if (it_lower == begin()) {
//         return front();
//     } else if (it_lower == end()) {
//         AWARN << "When evaluate trajectory, relative_time(" << relative_time
//               << ") is too large";
//         return back();
//     }
//     return math::InterpolateUsingLinearApproximation(*(it_lower - 1),
//     *it_lower,
//                                                      relative_time);
// }

size_t DiscretizedTrajectory::QueryLowerBoundPoint(const double relative_time,
                                                   const double epsilon) const {
    AINFO << "QueryLowerBoundPoint relative_time: " << relative_time
          << ", epsilon: " << epsilon << ", back().t: " << back().t;
    if (relative_time >= back().t) {
        return size() - 1;
    }
    auto func = [&epsilon](const TrajectoryPoint& tp,
                           const double relative_time) {
        return tp.t + epsilon < relative_time;
    };
    auto it_lower = std::lower_bound(begin(), end(), relative_time, func);
    return std::distance(begin(), it_lower);
}

size_t DiscretizedTrajectory::QueryNearestPoint(
    const common::Vec2d& position) const {
    double dist_sqr_min = std::numeric_limits<double>::max();
    size_t index_min = 0;
    for (size_t i = 0; i < size(); ++i) {
        const common::Vec2d curr_point(data()[i].x, data()[i].y);

        const double dist_sqr = curr_point.DistanceSquareTo(position);
        if (dist_sqr < dist_sqr_min) {
            dist_sqr_min = dist_sqr;
            index_min = i;
        }
    }
    return index_min;
}

size_t DiscretizedTrajectory::QueryNearestPointWithBuffer(
    const common::Vec2d& position, const double buffer) const {
    double dist_sqr_min = std::numeric_limits<double>::max();
    size_t index_min = 0;
    for (size_t i = 0; i < size(); ++i) {
        const common::Vec2d curr_point(data()[i].x, data()[i].y);

        const double dist_sqr = curr_point.DistanceSquareTo(position);
        if (dist_sqr < dist_sqr_min + buffer) {
            dist_sqr_min = dist_sqr;
            index_min = i;
        }
    }
    return index_min;
}

void DiscretizedTrajectory::AppendTrajectoryPoint(
    const TrajectoryPoint& trajectory_point) {
    if (!empty()) {
        // CHECK_GT(trajectory_point.t, back().t);
    }
    push_back(trajectory_point);
}

const TrajectoryPoint& DiscretizedTrajectory::TrajectoryPointAt(
    const size_t index) const {
    // CHECK_LT(index, NumOfPoints());
    return data()[index];
}

TrajectoryPoint DiscretizedTrajectory::StartPoint() const {
    // ACHECK(!empty());
    return front();
}

double DiscretizedTrajectory::GetTemporalLength() const {
    if (empty()) {
        return 0.0;
    }
    return back().t - front().t;
}

double DiscretizedTrajectory::GetSpatialLength() const {
    if (empty()) {
        return 0.0;
    }
    return back().s - front().s;
}

}  // namespace planning
}  // namespace arcsoft
