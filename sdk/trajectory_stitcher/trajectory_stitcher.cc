#include "trajectory_stitcher.h"

#include <algorithm>

namespace arcsoft {
namespace planning {

TrajectoryPoint TrajectoryStitcher::ComputeTrajectoryPointFromVehicleState(
    const double planning_cycle_time,
    const common::VehicleState& vehicle_state) {
    TrajectoryPoint point;
    point.s = 0.0;
    point.x = vehicle_state.x;
    point.y = vehicle_state.y;
    point.heading = vehicle_state.heading;
    point.curvature = vehicle_state.kappa;
    point.v = vehicle_state.linear_velocity;
    point.a = vehicle_state.linear_acceleration;
    point.t = planning_cycle_time;

    return point;
}

std::vector<TrajectoryPoint>
TrajectoryStitcher::ComputeReinitStitchingTrajectoryByAll(
    const double planning_cycle_time,
    const common::VehicleChassis& vehicle_chassis,
    const common::VehicleState& vehicle_state) {
    TrajectoryPoint reinit_point;
    static constexpr double kEpsilon_v = 0.1;
    static constexpr double kEpsilon_a = 0.4;
    if (std::abs(vehicle_state.linear_velocity) < kEpsilon_v &&
        std::abs(vehicle_state.linear_acceleration) < kEpsilon_a) {
        reinit_point = ComputeTrajectoryPointFromVehicleState(
            planning_cycle_time, vehicle_state);
    } else {
        common::VehicleState predicted_vehicle_state;
        RearCenteredKinematicBicycleModel(planning_cycle_time, vehicle_state,
                                          predicted_vehicle_state);
        reinit_point = ComputeTrajectoryPointFromVehicleState(
            planning_cycle_time, predicted_vehicle_state);
    }
    reinit_point.v = vehicle_chassis.lon_velocity;
    reinit_point.a = vehicle_chassis.lon_acceleration;

    return std::vector<TrajectoryPoint>(1, reinit_point);
}

std::vector<TrajectoryPoint>
TrajectoryStitcher::ComputeReinitStitchingTrajectoryByLatReplan(
    const double planning_cycle_time, const common::VehicleState& vehicle_state,
    const TrajectoryPoint& time_matched_point) {
    TrajectoryPoint reinit_point;
    std::vector<TrajectoryPoint> stitching_trajectory_replan;
    static constexpr double kEpsilon_v = 0.1;
    static constexpr double kEpsilon_a = 0.4;
    if (std::abs(vehicle_state.linear_velocity) < kEpsilon_v &&
        std::abs(vehicle_state.linear_acceleration) < kEpsilon_a) {
        reinit_point = ComputeTrajectoryPointFromVehicleState(
            planning_cycle_time, vehicle_state);
    } else {
        common::VehicleState predicted_vehicle_state;
        RearCenteredKinematicBicycleModel(planning_cycle_time, vehicle_state,
                                          predicted_vehicle_state);
        reinit_point = ComputeTrajectoryPointFromVehicleState(
            planning_cycle_time, predicted_vehicle_state);
    }
    reinit_point.v = time_matched_point.v;
    reinit_point.a = time_matched_point.a;
    stitching_trajectory_replan.emplace_back(reinit_point);
    return stitching_trajectory_replan;
}
std::vector<TrajectoryPoint>
TrajectoryStitcher::ComputeReinitStitchingTrajectoryByLonReplan(
    const double planning_cycle_time,
    const common::VehicleChassis& vehicle_chassis,
    const common::VehicleState& vehicle_state,
    const std::vector<TrajectoryPoint>& stitching_trajectory) {
    std::vector<TrajectoryPoint> stitching_trajectory_replan =
        stitching_trajectory;
    stitching_trajectory_replan.back().v = vehicle_chassis.lon_velocity;
    stitching_trajectory_replan.back().a = vehicle_chassis.lon_acceleration;
    return stitching_trajectory_replan;
}

/* Planning from current vehicle state if:
   1. the auto-driving mode is off
   (or) 2. we don't have the trajectory from last planning cycle
   (or) 3. the position deviation from actual and target is too high
*/
std::vector<TrajectoryPoint> TrajectoryStitcher::ComputeStitchingTrajectory(
    const common::VehicleChassis& vehicle_chassis,
    const common::VehicleState& vehicle_state, const double current_timestamp,
    const double planning_cycle_time, const size_t preserved_points_num,
    const bool replan_by_offset, const PublishableTrajectory* prev_trajectory,
    const bool is_simulation_mode, std::string* replan_reason) {
    bool lat_replan_flag = false;
    bool lon_replan_flag = false;
    // 1.check replan not by offset
    size_t time_matched_index = 0;
    const double veh_rel_time =
        current_timestamp - prev_trajectory->header_time();
    const bool is_vehicle_manual_mode =
        vehicle_state.driving_mode == common::AutoDriveModeState::OFF &&
        !is_simulation_mode;
    if (need_replan_by_necessary_check(vehicle_state, current_timestamp,
                                       prev_trajectory, is_simulation_mode,
                                       replan_reason, &time_matched_index) ||
        is_vehicle_manual_mode) {
        return ComputeReinitStitchingTrajectoryByAll(
            planning_cycle_time, vehicle_chassis, vehicle_state);
    }

    // 2. check replan by GEAR switch
    // static common::VehicleGear gear_pos = common::VehicleGear::GEAR_N;
    // if (gear_pos == common::VehicleGear::GEAR_N &&
    //     vehicle_chassis.vehicle_gear == common::VehicleGear::GEAR_D) {
    //     gear_pos = vehicle_chassis.vehicle_gear;
    //     const std::string msg =
    //         "gear change from n to d, replan to avoid large station
    //         error";
    //     AERROR << msg;
    //     *replan_reason = msg;
    //     return ComputeReinitStitchingTrajectoryByAll(planning_cycle_time,
    //                                             vehicle_state);
    // }
    // gear_pos = vehicle_chassis.vehicle_gear;

    auto time_matched_point = prev_trajectory->TrajectoryPointAt(
        static_cast<uint32_t>(time_matched_index));

    AINFO << "[Trajectory Stitcher] Time matched index: " << time_matched_index
          << ", veh_rel_time: " << veh_rel_time;

    AINFO << " time_matched_point.t: " << time_matched_point.t
          << " time_matched_point.v: " << time_matched_point.v
          << " time_matched_point.a: " << time_matched_point.a;

    size_t position_matched_index =
        prev_trajectory->QueryNearestPointWithBuffer(
            {vehicle_state.x, vehicle_state.y}, 1.0e-6);

    auto frenet_sd = ComputePositionProjection(
        vehicle_state.x, vehicle_state.y,
        prev_trajectory->TrajectoryPointAt(
            static_cast<uint32_t>(position_matched_index)));
    AINFO << "Frenet s: " << frenet_sd.first << ", l: " << frenet_sd.second;

    // 4.check replan by offset
    if (replan_by_offset) {
        auto lon_diff = time_matched_point.s - frenet_sd.first;
        auto lat_diff = frenet_sd.second;
        double time_diff = time_matched_point.t -
                           prev_trajectory
                               ->TrajectoryPointAt(static_cast<uint32_t>(
                                   position_matched_index))
                               .t;

        AINFO << "Control lateral diff: " << lat_diff
              << ", longitudinal diff: " << lon_diff
              << ", time diff: " << time_diff;

        if (std::fabs(lat_diff) > replan_lateral_distance_threshold) {
            const std::string msg =
                "the distance between matched point and actual position is "
                "too "
                "large. Replan is triggered. lat_diff = " +
                std::to_string(lat_diff);

            AERROR << msg;
            *replan_reason = msg;
            lat_replan_flag = true;
        }

        // 速度/加速度误差重规划
        auto speed_diff =
            std::fabs(time_matched_point.v - vehicle_chassis.lon_velocity);
        auto accel_diff =
            std::fabs(time_matched_point.a - vehicle_chassis.lon_acceleration);

        if (speed_diff > replan_speed_diff_threshold) {
            const std::string msg =
                "the speed diff is too large. Replan is triggered. "
                "speed_diff "
                "= " +
                std::to_string(speed_diff);
            AERROR << msg;
            *replan_reason = msg;
            lon_replan_flag = true;
        }

        if (accel_diff > replan_accel_diff_threshold) {
            const std::string msg =
                "the accel diff is too large. Replan is triggered. "
                "accel_diff "
                "= " +
                std::to_string(accel_diff);
            AERROR << msg;
            *replan_reason = msg;
            lon_replan_flag = true;
        }
    } else {
        AINFO << "replan according to certain amount of "
              << "lat、lon and time offset is disabled";
    }

    // 4.stitching_trajectory
    double forward_rel_time = current_timestamp -
                              prev_trajectory->header_time() +
                              planning_cycle_time;

    size_t forward_time_index =
        prev_trajectory->QueryLowerBoundPoint(forward_rel_time);

    // AINFO << "Position matched index:\t" << position_matched_index;
    // AINFO << "Time matched index:\t" << time_matched_index;

    // auto matched_index = std::min(time_matched_index,
    // position_matched_index);
    auto matched_index = position_matched_index;
    AINFO << "matched_index: " << matched_index;

    std::vector<TrajectoryPoint> stitching_trajectory(
        prev_trajectory->begin() +
            std::max(0, static_cast<int>(matched_index - preserved_points_num)),
        prev_trajectory->begin() +
            std::max(0, static_cast<int>(matched_index)) + 1);
    AINFO << "stitching_trajectory size: " << stitching_trajectory.size();

    if (stitching_trajectory.size() == 0) {
        *replan_reason = "replan for previous trajectory missed path point";
        return ComputeReinitStitchingTrajectoryByAll(
            planning_cycle_time, vehicle_chassis, vehicle_state);
    }
    const double zero_s = stitching_trajectory.back().s;
    for (auto& tp : stitching_trajectory) {
        tp.t = tp.t + prev_trajectory->header_time() - current_timestamp;
        tp.s = tp.s - zero_s;
    }
    stitching_trajectory.back().v = time_matched_point.v;
    stitching_trajectory.back().a = time_matched_point.a;

    if (lat_replan_flag && lon_replan_flag) {
        return ComputeReinitStitchingTrajectoryByAll(
            planning_cycle_time, vehicle_chassis, vehicle_state);
    } else if (lat_replan_flag) {
        return ComputeReinitStitchingTrajectoryByLatReplan(
            planning_cycle_time, vehicle_state, time_matched_point);
    } else if (lon_replan_flag) {
        return ComputeReinitStitchingTrajectoryByLonReplan(
            planning_cycle_time, vehicle_chassis, vehicle_state,
            stitching_trajectory);
    } else {
        return stitching_trajectory;
    }
}

std::pair<double, double> TrajectoryStitcher::ComputePositionProjection(
    const double x, const double y, const TrajectoryPoint& p) {
    Vec2d v(x - p.x, y - p.y);
    Vec2d n(std::cos(p.heading), std::sin(p.heading));

    std::pair<double, double> frenet_sd;
    frenet_sd.first = v.InnerProd(n) + p.s;
    frenet_sd.second = v.CrossProd(n);
    return frenet_sd;
}

bool TrajectoryStitcher::need_replan_by_necessary_check(
    const common::VehicleState& vehicle_state, const double current_timestamp,
    const PublishableTrajectory* prev_trajectory, const bool is_simulation_mode,
    std::string* replan_reason, size_t* time_matched_index) {
    if (!enable_trajectory_stitcher) {
        *replan_reason = "stitch is disabled by gflag.";
        return true;
    }
    if (!prev_trajectory) {
        *replan_reason = "replan for no previous trajectory.";
        return true;
    }

    size_t prev_trajectory_size = prev_trajectory->NumOfPoints();
    AINFO << "Previous trajectory size: " << prev_trajectory_size;
    if (prev_trajectory_size <= 0) {
        AINFO << "Projected trajectory at time ["
              << prev_trajectory->header_time()
              << "] size is zero! Previous planning not exist or failed. Use "
                 "origin car status instead.";
        *replan_reason = "replan for empty previous trajectory.";
        return true;
    }

    double veh_rel_time = 0.0;
    if (is_simulation_mode) {
        veh_rel_time = 0.1;
    } else {
        veh_rel_time = current_timestamp - prev_trajectory->header_time();
    }

    *time_matched_index = prev_trajectory->QueryLowerBoundPoint(veh_rel_time);
    // AINFO << "Time matched index: " << *time_matched_index
    //       << ", veh_rel_time: " << veh_rel_time
    //       << ", current_timestamp: " << current_timestamp
    //       << ", prev_trajectory->header_time(): "
    //       << prev_trajectory->header_time();

    if (*time_matched_index == 0 &&
        veh_rel_time < prev_trajectory->StartPoint().t) {
        AWARN << "current time smaller than previous trajectory's first time ";
        *replan_reason =
            "current time smaller than previous trajectory's first time";
        return true;
    }

    if (*time_matched_index + 1 >= prev_trajectory_size) {
        AWARN << "current time beyond the previous trajectory's last time";
        *replan_reason =
            "current time beyond the previous trajectory's last time";
        return true;
    }

    // auto time_matched_point = prev_trajectory->TrajectoryPointAt(
    //     static_cast<uint32_t>(*time_matched_index));

    // if (!time_matched_point.has_path_point()) {
    //     *replan_reason = "replan for previous trajectory missed path point";
    //     return true;
    // }
    return false;
}

void TrajectoryStitcher::RearCenteredKinematicBicycleModel(
    const double predicted_time_horizon,
    const common::VehicleState& cur_vehicle_state,
    common::VehicleState& predicted_vehicle_state) {
    // Kinematic bicycle model centered at rear axis center by Euler forward
    // discretization
    // Assume constant control command and constant z axis position
    // CHECK_GT(predicted_time_horizon, 0.0);
    double dt = predicted_time_horizon;
    double cur_x = cur_vehicle_state.x;
    double cur_y = cur_vehicle_state.y;
    double cur_phi = cur_vehicle_state.heading;
    double cur_v = cur_vehicle_state.linear_velocity;
    double cur_a = cur_vehicle_state.linear_acceleration;
    double next_x = cur_x;
    double next_y = cur_y;
    double next_phi = cur_phi;
    double next_v = cur_v;
    double countdown_time = predicted_time_horizon;
    bool finish_flag = false;
    static constexpr double kepsilon = 1e-8;
    while (countdown_time > kepsilon && !finish_flag) {
        countdown_time -= dt;
        if (countdown_time < kepsilon) {
            dt = countdown_time + dt;
            finish_flag = true;
        }
        double intermidiate_phi =
            cur_phi + 0.5 * dt * cur_v * cur_vehicle_state.kappa;
        next_phi =
            cur_phi + dt * (cur_v + 0.5 * dt * cur_a) * cur_vehicle_state.kappa;
        next_x = cur_x +
                 dt * (cur_v + 0.5 * dt * cur_a) * std::cos(intermidiate_phi);
        next_y = cur_y +
                 dt * (cur_v + 0.5 * dt * cur_a) * std::sin(intermidiate_phi);

        next_v = cur_v + dt * cur_a;
        cur_x = next_x;
        cur_y = next_y;
        cur_phi = next_phi;
        cur_v = next_v;
    }
    predicted_vehicle_state.x = next_x;
    predicted_vehicle_state.y = next_y;
    predicted_vehicle_state.heading = next_phi;
    predicted_vehicle_state.kappa = cur_vehicle_state.kappa;
    predicted_vehicle_state.linear_velocity = next_v;
    predicted_vehicle_state.linear_acceleration =
        cur_vehicle_state.linear_acceleration;
}

}  // namespace planning
}  // namespace arcsoft
