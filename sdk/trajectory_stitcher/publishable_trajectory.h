#pragma once

#include "discretized_trajectory.h"

namespace arcsoft {
namespace planning {

class PublishableTrajectory : public DiscretizedTrajectory {
public:
    PublishableTrajectory() = default;

    PublishableTrajectory(const double header_time,
                          const DiscretizedTrajectory& discretized_trajectory);
    double header_time() const;

private:
    double header_time_ = 0.0;
};

}  // namespace planning
}  // namespace arcsoft
