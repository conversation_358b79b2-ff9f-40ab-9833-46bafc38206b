/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __LANE_CHANGE_DECIDER_H__
#define __LANE_CHANGE_DECIDER_H__

#include "triggers/base_lane_change_trigger.h"

namespace arcsoft {
namespace planning {

class LaneChangeDecider {
public:
    explicit LaneChangeDecider(framework::Session* session)
        : session_(session) {
        register_triggers();
        register_suppressors();
    }
    ~LaneChangeDecider() = default;

    bool Process();

private:
    void register_triggers();
    void register_suppressors();
    void sort_lane_change_decisions(
        const std::vector<LaneChangeDecision>& candidate_trigger_decisions,
        LaneChangeDecision& best_decision) const;

private:
    framework::Session* session_;
    std::vector<std::unique_ptr<BaseLaneChangeTrigger>> lane_change_triggers_;

    LaneChangeDecision last_trigger_decision_;

    int call_cnt_ = 0;
    int last_lane_change_cnt_ = -10000;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __LANE_CHANGE_DECIDER_H__ */