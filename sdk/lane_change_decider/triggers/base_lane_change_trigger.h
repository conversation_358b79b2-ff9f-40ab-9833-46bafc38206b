/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __BASE_LANE_CHANGE_TRIGGER_H__
#define __BASE_LANE_CHANGE_TRIGGER_H__

#include "common/basic_types.h"
#include "framework/session.h"

namespace arcsoft {
namespace planning {

class BaseLaneChangeTrigger {
public:
    explicit BaseLaneChangeTrigger(framework::Session* session)
        : session_(session) {}
    virtual ~BaseLaneChangeTrigger() = default;

    virtual RequestType Trigger() = 0;

    virtual RequestSource request_source() const = 0;
    virtual std::string name() const = 0;
    virtual size_t same_direction_freeze_cnt() const { return 0; }
    virtual size_t opposite_direction_freeze_cnt() const { return 0; }

protected:
    framework::Session* session_ = nullptr;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __BASE_LANE_CHANGE_TRIGGER_H__ */