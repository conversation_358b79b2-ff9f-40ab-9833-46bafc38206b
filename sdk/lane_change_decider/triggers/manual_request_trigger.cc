#include "manual_request_trigger.h"

namespace arcsoft {
namespace planning {

RequestType ManualRequestTrigger::Trigger() {
    // [TODO]
    // return RequestType::NO_CHANGE;

    auto& manual_request_info = session_->mutable_manual_request_info();

    const auto& curr_turn_signal =
        session_->local_view().vehicle_chassis.vehicle_light_flag;
    const auto& lane_change_status = session_->lane_change_status();

    // 当前帧无拨杆信号或与上一帧相反
    if ((curr_turn_signal == common::VehicleLightFLAG::OFF) ||
        lane_change_status == LaneChangeStatus::FINISHED) {
        manual_request_info.request_type = RequestType::NO_CHANGE;
        manual_request_info.call_cnt = 0;
    } else if ((curr_turn_signal != last_turn_signal_) &&
               (curr_turn_signal == common::VehicleLightFLAG::LEFT_TURN)) {
        manual_request_info.request_type = RequestType::LEFT_CHANGE;
        manual_request_info.call_cnt++;
    } else if ((curr_turn_signal != last_turn_signal_) &&
               (curr_turn_signal == common::VehicleLightFLAG::RIGHT_TURN)) {
        manual_request_info.request_type = RequestType::RIGHT_CHANGE;
        manual_request_info.call_cnt++;
    } else {
        // TODO
    }

    AINFO << "curr_turn_signal: " << curr_turn_signal;

    last_turn_signal_ = curr_turn_signal;

    return manual_request_info.request_type;
}

}  // namespace planning
}  // namespace arcsoft