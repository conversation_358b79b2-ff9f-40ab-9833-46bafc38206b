/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __NAVI_REQUEST_TRIGGER_H__
#define __NAVI_REQUEST_TRIGGER_H__

#include "base_lane_change_trigger.h"

namespace arcsoft {
namespace planning {

class NaviRequestTrigger : public BaseLaneChangeTrigger {
public:
    explicit NaviRequestTrigger(framework::Session* session)
        : BaseLaneChangeTrigger(session) {}
    virtual ~NaviRequestTrigger() = default;

    RequestType Trigger() override;

    RequestSource request_source() const override {
        return RequestSource::NAVI_REQUEST;
    }

    std::string name() const override { return "NaviRequestTrigger"; }

private:
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __NAVI_REQUEST_TRIGGER_H__ */