/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __MANUAL_REQUEST_TRIGGER_H__
#define __MANUAL_REQUEST_TRIGGER_H__

#include "base_lane_change_trigger.h"

namespace arcsoft {
namespace planning {

static constexpr size_t kSameDirFreezeCnt = 50U;
static constexpr size_t kOppositeDirFreezeCnt = 50U;

class ManualRequestTrigger : public BaseLaneChangeTrigger {
public:
    explicit ManualRequestTrigger(framework::Session* session)
        : BaseLaneChangeTrigger(session) {}
    virtual ~ManualRequestTrigger() = default;

    RequestType Trigger() override;

    RequestSource request_source() const override {
        return RequestSource::MANUAL_REQUEST;
    }

    std::string name() const override { return "ManualRequestTrigger"; }

    size_t same_direction_freeze_cnt() const override {
        return kSameDirFreezeCnt;
    }

    size_t opposite_direction_freeze_cnt() const override {
        return kOppositeDirFreezeCnt;
    }

private:
    common::VehicleLightFLAG last_turn_signal_ = common::VehicleLightFLAG::OFF;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __MANUAL_REQUEST_TRIGGER_H__ */
