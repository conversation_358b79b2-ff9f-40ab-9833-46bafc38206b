/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "navi_request_trigger.h"

namespace arcsoft {
namespace planning {

RequestType NaviRequestTrigger::Trigger() {
    // Disable Lane change for now
    return RequestType::NO_CHANGE;

    // 判断是否存在导航推荐车道
    const auto& lane_change_status = session_->lane_change_status();
    if (lane_change_status == LaneChangeStatus::FINISHED) {
        return RequestType::NO_CHANGE;
    }

    // // For Lane Change Debug
    // return RequestType::RIGHT_CHANGE;

    // 获取当前车道
    const auto& current_lane = session_->current_lane();

    // 1. 如果当前车道在规划路径上，直接返回 NO_CHANGE
    if (current_lane->is_on_route()) {
        return RequestType::NO_CHANGE;
    }

    auto& reference_lines = session_->mutable_reference_lines();

    // 2. 检查右侧是否有 on_route 的车道
    uint32_t right_lane_id = current_lane->right_lane_id();
    while (right_lane_id != 0) {
        auto right_lane = reference_lines.Find(right_lane_id);
        if (right_lane && right_lane->is_on_route()) {
            return RequestType::RIGHT_CHANGE;
        }
        right_lane_id = right_lane ? right_lane->right_lane_id() : 0;
    }

    // 3. 检查左侧是否有 on_route 的车道
    uint32_t left_lane_id = current_lane->left_lane_id();
    while (left_lane_id != 0) {
        auto left_lane = reference_lines.Find(left_lane_id);
        if (left_lane && left_lane->is_on_route()) {
            return RequestType::LEFT_CHANGE;
        }
        left_lane_id = left_lane ? left_lane->left_lane_id() : 0;
    }

    // 4. 如果左右都没有 on_route 的车道，返回 NO_CHANGE
    return RequestType::NO_CHANGE;
}

}  // namespace planning

}  // namespace arcsoft
