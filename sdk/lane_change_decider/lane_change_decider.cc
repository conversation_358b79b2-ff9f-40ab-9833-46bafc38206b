#include "lane_change_decider.h"

#include "triggers/manual_request_trigger.h"
#include "triggers/navi_request_trigger.h"

namespace arcsoft {
namespace planning {

void LaneChangeDecider::register_triggers() {
    // 拨杆触发
    lane_change_triggers_.emplace_back(
        std::make_unique<ManualRequestTrigger>(session_));

    // 导航触发
    lane_change_triggers_.emplace_back(
        std::make_unique<NaviRequestTrigger>(session_));

    return;
}

void LaneChangeDecider::register_suppressors() {}

bool LaneChangeDecider::Process() {
    // 计数器
    call_cnt_++;
    AINFO << "call cnt: " << call_cnt_;

    // 变道正在进行
    const auto& last_scenario_state =
        session_->scenario_state_info().target_scenario_state;
    // 能否由keep状态触发变道
    if (last_scenario_state == ScenarioStateEnum::CRUISE_KEEP) {
        AINFO << "last scenario state is CRUISE_KEEP, check candidate trigger "
                 "decisions";
        std::vector<LaneChangeDecision> candidate_trigger_decisions;

        for (const auto& trigger : lane_change_triggers_) {
            AINFO << "trigger name: " << trigger->name();
            RequestType triggered_dir = trigger->Trigger();
            RequestSource triggered_source = trigger->request_source();
            AINFO << "trigger direction: " << static_cast<int>(triggered_dir);
            if (triggered_dir == RequestType::NO_CHANGE) {
                continue;
            }

            // 变道冷却时间
            if (last_trigger_decision_.request_type != RequestType::NO_CHANGE) {
                const bool enable_freeze =
                    last_lane_change_cnt_ >= last_trigger_decision_.call_cnt;
                AINFO << "enable_freeze: " << enable_freeze;
                // 成功触发过才允许freeze
                if (enable_freeze) {
                    AINFO << "after last trigger : "
                          << call_cnt_ - last_trigger_decision_.call_cnt;
                    AINFO << "after last lane change finish: "
                          << call_cnt_ - last_lane_change_cnt_;
                    AINFO << "same direction freeze cnt: "
                          << trigger->same_direction_freeze_cnt();
                    AINFO << "opposite direction freeze cnt: "
                          << trigger->opposite_direction_freeze_cnt();

                    // if ((last_trigger_decision_.request_type ==
                    //      triggered_dir) &&
                    //     (last_lane_change_cnt_ +
                    //          static_cast<int>(
                    //              trigger->same_direction_freeze_cnt()) >=
                    //      call_cnt_)) {
                    //     AINFO << "Freeze LC for Same Direction! ";
                    //     continue;
                    // }
                    if ((last_trigger_decision_.request_type !=
                         triggered_dir) &&
                        (last_lane_change_cnt_ +
                             static_cast<int>(
                                 trigger->opposite_direction_freeze_cnt()) >=
                         call_cnt_)) {
                        AINFO << "Freeze LC for Opposite Direction! ";
                        continue;
                    }
                }
            }

            candidate_trigger_decisions.emplace_back(LaneChangeDecision{
                true, triggered_source, triggered_dir, call_cnt_});
        }

        //
        LaneChangeDecision best_trigger_decision;
        sort_lane_change_decisions(candidate_trigger_decisions,
                                   best_trigger_decision);

        // if (best_trigger_decision.is_valid &&
        //     (best_trigger_decision.request_type != RequestType::NO_CHANGE)) {
        //     session_->set_best_trigger_decision(best_trigger_decision);
        //     last_trigger_decision_ = best_trigger_decision;
        // }
        session_->set_best_trigger_decision(best_trigger_decision);
        last_trigger_decision_ = best_trigger_decision;
    } else {
        last_lane_change_cnt_ = call_cnt_;
        // 能否由change跳出变道
        if (last_scenario_state == ScenarioStateEnum::CRUISE_CHANGE) {
            AINFO << "last scenario state is CRUISE_CHANGE, check candidate "
                     "trigger decisions";
            for (const auto& trigger : lane_change_triggers_) {
                AINFO << "trigger name: " << trigger->name();
                RequestType triggered_dir = trigger->Trigger();
                RequestSource triggered_source = trigger->request_source();
                AINFO << "trigger direction: "
                      << static_cast<int>(triggered_dir);
                if (triggered_dir != RequestType::NO_CHANGE) {
                    LaneChangeDecision best_trigger_decision{
                        true, triggered_source, triggered_dir, call_cnt_};
                    session_->set_best_trigger_decision(best_trigger_decision);
                    last_trigger_decision_ = best_trigger_decision;
                    return true;
                }
            }
            LaneChangeDecision best_trigger_decision{
                true, RequestSource::NO_REQUEST, RequestType::NO_CHANGE,
                call_cnt_};
            session_->set_best_trigger_decision(best_trigger_decision);
            last_trigger_decision_ = best_trigger_decision;
        }
    }
    return true;
}

void LaneChangeDecider::sort_lane_change_decisions(
    const std::vector<LaneChangeDecision>& candidate_trigger_decisions,
    LaneChangeDecision& best_decision) const {
    best_decision.is_valid = false;

    if (candidate_trigger_decisions.empty()) {
        AINFO << "candidate_trigger_decisions is empty.";
        return;
    }

    // 过滤掉无效的请求(NO_REQUEST)
    std::vector<LaneChangeDecision> valid_decisions;
    for (const auto& decision : candidate_trigger_decisions) {
        if (decision.request_source != RequestSource::NO_REQUEST) {
            valid_decisions.push_back(decision);
        }
    }

    if (valid_decisions.empty()) {
        AINFO << "No valid lane change decisions found (all are NO_REQUEST).";
        return;
    }

    // 按照优先级排序: MANUAL > NAVI > OVERTAKE
    std::sort(valid_decisions.begin(), valid_decisions.end(),
              [](const LaneChangeDecision& a, const LaneChangeDecision& b) {
                  return a.request_source < b.request_source;
              });

    best_decision = valid_decisions.front();
    best_decision.is_valid = true;
}

}  // namespace planning

}  // namespace arcsoft