/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __SCENARIO_STATE_H__
#define __SCENARIO_STATE_H__

#include <memory>
#include <string>

#include "common/basic_types.h"
#include "framework/session.h"
#include "framework/task_runner.h"
#include "hfsm/machine_single.hpp"
#include "lane_change_decider/lane_change_decider.h"
#include "lane_change_transition_evaluator.h"

namespace arcsoft {
namespace planning {

class ScenarioStateMachine;
class LaneChangeTransitionEvaluator;

using Session = arcsoft::framework::Session;

struct FSMContext {
    std::string name;
    int state = 0;
    RequestType direction = NO_CHANGE;
    bool external = false;
    bool initialized = false;
    std::shared_ptr<ScenarioStateMachine> state_machine;
    std::shared_ptr<LaneChangeTransitionEvaluator> lc_transition_evaluator;
    Session *session;
    std::shared_ptr<arcsoft::framework::TaskRunner> task_runner;

    //
    int cnt = 0;
};

using M = hfsm::Machine<FSMContext>;

template <typename T>
struct type2int {};

template <typename T>
struct type2name {};

struct StateBase : M::Base {
    virtual ~StateBase() = default;

    void transition(Control &control, FSMContext &context) {
        if (context.external == true) {
            return;
        }

        process(control, context);
    }

    void process(Control &control, FSMContext &context);

    template <typename T>
    static void change_state(Control &control, FSMContext &context) {
        if (context.state == type2int<T>::value) {
            return;
        }

        AINFO << "Change state from [" << context.name << "] to ["
              << type2name<T>::name << "]. ";

        (void)control.changeTo<T>();
        context.state = type2int<T>::value;
        context.name = type2name<T>::name;
    }

    void change_state(ScenarioStateEnum next_state, Control &control,
                      FSMContext &context);

    virtual bool is_leaf() { return false; }

    virtual void get_state_transition_candidates(
        FSMContext &context, StateTransitionContexts &transition_contexts) {}
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __SCENARIO_STATE_H__ */