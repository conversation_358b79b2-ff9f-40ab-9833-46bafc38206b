/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __LANE_CHANGE_TRANSITION_EVALUATOR_H__
#define __LANE_CHANGE_TRANSITION_EVALUATOR_H__

#include "scenario_state.h"

namespace arcsoft {
namespace planning {

struct FSMContext;

struct ClosestObstacles {
    const FrenetObstacle* rear_obs = nullptr;
    const FrenetObstacle* front_obs = nullptr;
};

class LaneChangeTransitionEvaluator {
public:
    LaneChangeTransitionEvaluator(framework::Session* session)
        : session_(session) {}
    ~LaneChangeTransitionEvaluator() = default;

    bool Process(const FSMContext& context);

    const bool can_transition_from_keep(const RequestSource source,
                                        const ScenarioStateEnum target_state);
    const bool can_transition_from_wait(const RequestSource source,
                                        const ScenarioStateEnum target_state);
    const bool can_transition_from_change(const RequestSource source,
                                          const RequestType request_type,
                                          const ScenarioStateEnum target_state);
    const bool can_transition_from_back(const RequestSource source,
                                        const ScenarioStateEnum target_state);

private:
    framework::Session* session_;
    double ego_speed_ = 0.0;
    RequestSource request_source_ = RequestSource::NO_REQUEST;
    RequestType request_type_ = RequestType::NO_CHANGE;
    RequestType last_request_type_ = RequestType::NO_CHANGE;
    bool same_direction_ = true;
    bool is_gap_available_ = false;
    bool is_turn_signal_on_enough_ = false;

    // 由keep->wait检查
    bool check_keep2wait_conditions();
    // 计算可用虚线长度
    double calculate_crossable_length_from_ego_until_solid(
        const uint32_t& lane_id);

    // 前后方车辆是否安全
    bool IsRearObstacleSafe(const FrenetObstacle& frenet_obstacle, double ego_s,
                            const ReferenceLine& target_ref_line) const;
    bool IsFrontObstacleSafe(const FrenetObstacle& frenet_obstacle,
                             double ego_s, double ego_speed) const;

    bool is_divider_crossable(LaneDividerType divider_type) const;

    bool check_target_lane_collision_risk() const;
    void log_target_lane_obstacles(const ReferenceLine& target_ref_lane);

    ClosestObstacles find_closest_obstacles(
        const ReferenceLine& target_ref_lane) const;

    bool is_lane_change_finish(const RequestSource request_source,
                               const RequestType request_type);

    // 常量定义
    static constexpr double kMinDottedLineLength = 50.0;  // 最小虚线长度(米)
    static constexpr double kMinRearTTC = 3.0;   // 最小后方TTC(秒)
    static constexpr double kMinFrontTHW = 1.2;  // 最小后方TTC(秒)
    static constexpr double kMinRearGap = 7;     // 最小后方Gap（米）
    static constexpr double kMinFrontGap = 5;    // 最小前方Gap (米)

    // 硬编码的车辆参数
    const double front_edge_to_center = 3.2;  // 参考点到车头距离
    const double back_edge_to_center = 1.5;   // 参考点到车尾距离
    const double length = 4.8;                // 车辆总长
    const double width = 1.9;                 // 车辆总宽

    uint32_t last_target_lane_id = 0;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __LANE_CHANGE_TRANSITION_EVALUATOR_H__ */
