/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "scenario_state.h"

#include "scenario_state_machine.h"

namespace arcsoft {
namespace planning {

void StateBase::process(Control &control, FSMContext &context) {
    auto state_machine = context.state_machine;

    if (!is_leaf()) {
        return;
    }
    auto start_time = std::chrono::system_clock::now();
    // 清空历史数据
    StateTransitionContexts &transition_contexts =
        context.session->mutable_transition_contexts();
    transition_contexts.clear();
    context.session->set_min_trajectory_cost(
        std::numeric_limits<double>::max());

    // 计算状态转移所依赖的一些变量
    const auto &lc_transition_evaluator = context.lc_transition_evaluator;
    lc_transition_evaluator->Process(context);

    // 获取候选状态
    get_state_transition_candidates(context, transition_contexts);

    // [TODO] 待通过transition_contexts计算得到
    // context.session->set_target_lane_id(1);

    auto end_time = std::chrono::system_clock::now();
    std::chrono::duration<double> diff = end_time - start_time;
    AINFO << "Scenario State Machine used time: " << diff.count() * 1000
          << " ms.";

    // 执行Task Pipeline
    if (!context.task_runner->Run()) {
        return;
    }

    const int best_transition_context_id =
        context.session->best_transition_context_id();
    AINFO << "[Final] Best transition context id: "
          << best_transition_context_id;

    auto &lane_change_status = context.session->mutable_lane_change_status();

    // 进行状态转移
    if (best_transition_context_id >=
            static_cast<int>(transition_contexts.size()) ||
        best_transition_context_id < 0) {
        lane_change_status = LaneChangeStatus::UNINITIALIZED;
        AERROR << " best_transition_context_id is invalid! ";
        return;
    }

    const auto &best_transition_context =
        transition_contexts[best_transition_context_id];
    auto next_state = best_transition_context.target_state;

    // AINFO << "best_transition_context.target_state: "
    //       << best_transition_context.source_state << " "
    //       << best_transition_context.target_state << " "
    //       << best_transition_context.request_type;
    // AINFO << "context.state: " << context.state << " " << context.direction;
    if (best_transition_context.target_state != context.state) {
        if (best_transition_context.target_state ==
            ScenarioStateEnum::CRUISE_CHANGE) {
            lane_change_status = LaneChangeStatus::IN_PROCESS;
        } else if (best_transition_context.target_state ==
                       ScenarioStateEnum::CRUISE_KEEP &&
                   context.state == ScenarioStateEnum::CRUISE_CHANGE) {
            lane_change_status = LaneChangeStatus::FINISHED;
        } else {
            lane_change_status = LaneChangeStatus::UNINITIALIZED;
        }
        change_state(next_state, control, context);
        context.state = next_state;
    } else {
        if (best_transition_context.target_state ==
            ScenarioStateEnum::CRUISE_KEEP) {
            lane_change_status = LaneChangeStatus::UNINITIALIZED;
        }
    }
    AINFO << "lane_change_status: " << lane_change_status;
    DLOG_DICT_ITEM("lane_change_status", lane_change_status);
    DLOG_DICT_ITEM("scenario_state", context.state);

    // if (best_transition_context.target_state == context.state &&
    //     best_transition_context.target_state ==
    //         ScenarioStateEnum::CRUISE_KEEP) {
    //     lane_change_status = LaneChangeStatus::UNINITIALIZED;
    // }

    //
}

void StateBase::change_state(ScenarioStateEnum next_state, Control &control,
                             FSMContext &context) {
    switch (next_state) {
        case CRUISE_KEEP:
            change_state<CruiseState::Keep>(control, context);
            break;
        case CRUISE_WAIT:
            change_state<CruiseState::Wait>(control, context);
            break;
        case CRUISE_CHANGE:
            change_state<CruiseState::Change>(control, context);
            break;
        case CRUISE_BACK:
            change_state<CruiseState::Back>(control, context);
            break;
        default:
            change_state<CruiseState::Keep>(control, context);
    }
}

}  // namespace planning
}  // namespace arcsoft