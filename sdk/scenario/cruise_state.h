/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __CRUISE_STATE_H__
#define __CRUISE_STATE_H__

#include "scenario_state.h"

namespace arcsoft {
namespace planning {

struct CruiseBase : StateBase {
    bool is_leaf() { return true; }
    virtual void get_state_transition_candidates(
        FSMContext &context, StateTransitionContexts &transition_contexts) {}
};

struct CruiseState : StateBase {
    struct Keep : CruiseBase {
        void get_state_transition_candidates(
            FSMContext &context,
            StateTransitionContexts &transition_contexts) override;
    };

    struct Wait : CruiseBase {
        void get_state_transition_candidates(
            FSMContext &context,
            StateTransitionContexts &transition_contexts) override;
    };

    struct Change : CruiseBase {
        void get_state_transition_candidates(
            FSMContext &context,
            StateTransitionContexts &transition_contexts) override;
    };

    struct Back : CruiseBase {
        void get_state_transition_candidates(
            FSMContext &context,
            StateTransitionContexts &transition_contexts) override;
    };
};

template <>
struct type2int<CruiseState::Keep> {
    enum { value = CRUISE_KEEP };
};

template <>
struct type2int<CruiseState::Wait> {
    enum { value = CRUISE_WAIT };
};

template <>
struct type2int<CruiseState::Change> {
    enum { value = CRUISE_CHANGE };
};

template <>
struct type2int<CruiseState::Back> {
    enum { value = CRUISE_BACK };
};

template <>
struct type2name<CruiseState::Keep> {
    static constexpr auto name = "KEEP";
};

template <>
struct type2name<CruiseState::Wait> {
    static constexpr auto name = "WAIT";
};

template <>
struct type2name<CruiseState::Change> {
    static constexpr auto name = "CHANGE";
};

template <>
struct type2name<CruiseState::Back> {
    static constexpr auto name = "BACK";
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __CRUISE_STATE_H__ */