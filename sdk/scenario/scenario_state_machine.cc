/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "scenario_state_machine.h"

namespace arcsoft {
namespace planning {

using json = nlohmann::json;

const std::vector<std::string> LoadTaskList() {
    std::vector<std::string> pipeline_tasks;

    pipeline_tasks.emplace_back("PathOptimizer");
    pipeline_tasks.emplace_back("STGraphGenerator");
    pipeline_tasks.emplace_back("SpeedPlanner");
    pipeline_tasks.emplace_back("SpeedOptimizer");
    pipeline_tasks.emplace_back("TrajectoryEvaluator");

    return pipeline_tasks;
}

void ScenarioStateMachine::Init() {
    fsm_context_.state_machine = shared_from_this();
    fsm_context_.state = CRUISE_KEEP;
    fsm_context_.external = false;
    fsm_context_.name = "KEEP";
    fsm_context_.session = session_;

    std::shared_ptr<arcsoft::framework::TaskRunner> task_runner =
        std::make_shared<arcsoft::framework::TaskRunner>(session_);
    task_runner->Init(LoadTaskList());
    fsm_context_.task_runner = task_runner;

    std::shared_ptr<LaneChangeTransitionEvaluator> lc_transition_evaluator =
        std::make_shared<LaneChangeTransitionEvaluator>(session_);

    fsm_context_.lc_transition_evaluator = lc_transition_evaluator;

    scenario_fsm_.changeTo<CruiseState::Keep>();
}

void ScenarioStateMachine::reset_state_machine() {
    change_state_external<CruiseState::Keep>();
}

void ScenarioStateMachine::update_state_machine() {
    if ((fsm_context_.state >= CRUISE_KEEP) &&
        (fsm_context_.state <= CRUISE_BACK)) {
        scenario_fsm_.update();
    } else {
        AINFO << "[ERROR] State Not Implemented!";
        change_state_external<CruiseState::Keep>();
    }
}

bool ScenarioStateMachine::calculate_turn_signal() {
    //
    auto& scenario_state_info = session_->mutable_scenario_state_info();
    scenario_state_info.turn_signal =
        session_->best_trigger_decision().request_type;

    if (turn_signal_ == NO_CHANGE &&
        scenario_state_info.turn_signal != NO_CHANGE) {
        turn_signal_on_time_ = common::get_system_time();
    }

    turn_signal_ = scenario_state_info.turn_signal;

    return true;
}

bool ScenarioStateMachine::Process() {
    if (!fsm_context_.initialized) {
        reset_state_machine();
        fsm_context_.initialized = true;
    }

    if (session_->cnt_ > 5) {
        AINFO << "sz test session cnt > 5! ";
    }

    calculate_turn_signal();
    update_state_machine();

    auto& scenario_state_info = session_->mutable_scenario_state_info();
    scenario_state_info.target_scenario_state =
        static_cast<ScenarioStateEnum>(fsm_context_.state);

    return true;
}

}  // namespace planning
}  // namespace arcsoft
