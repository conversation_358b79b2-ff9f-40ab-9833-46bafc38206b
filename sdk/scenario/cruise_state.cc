/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "cruise_state.h"

namespace arcsoft {
namespace planning {

void CruiseState::Keep::get_state_transition_candidates(
    FSMContext &context, StateTransitionContexts &transition_contexts) {
    transition_contexts.clear();

    std::vector<ScenarioStateEnum> candidate_states;
    std::vector<RequestType> request_types;
    auto lc_transition_evaluator = context.lc_transition_evaluator;

    // 判断是否可状态跳转
    const auto &request_source =
        context.session->best_trigger_decision().request_source;
    if (lc_transition_evaluator->can_transition_from_keep(request_source,
                                                          CRUISE_WAIT)) {
        candidate_states.emplace_back(CRUISE_WAIT);
        request_types.emplace_back(NO_CHANGE);
    } else {
        candidate_states.emplace_back(CRUISE_KEEP);
        request_types.emplace_back(NO_CHANGE);
    }
    for (size_t i = 0; i < candidate_states.size(); ++i) {
        transition_contexts.emplace_back(StateTransitionContext{
            CRUISE_KEEP, candidate_states[i], request_types[i]});
    }
}

void CruiseState::Wait::get_state_transition_candidates(
    FSMContext &context, StateTransitionContexts &transition_contexts) {
    auto lc_transition_evaluator = context.lc_transition_evaluator;
    std::vector<ScenarioStateEnum> candidate_states;
    std::vector<RequestType> request_types;
    const auto &request_source =
        context.session->best_trigger_decision().request_source;

    if (lc_transition_evaluator->can_transition_from_wait(request_source,
                                                          CRUISE_CHANGE)) {
        // [TODO] 目前仅考虑单状态
        candidate_states.emplace_back(CRUISE_CHANGE);
        request_types.emplace_back(
            context.session->best_trigger_decision().request_type);
        // candidate_states.emplace_back(CRUISE_WAIT);
    } else if (lc_transition_evaluator->can_transition_from_wait(request_source,
                                                                 CRUISE_WAIT)) {
        candidate_states.emplace_back(CRUISE_WAIT);
        request_types.emplace_back(NO_CHANGE);
    } else {
        candidate_states.emplace_back(CRUISE_KEEP);
        request_types.emplace_back(NO_CHANGE);
    }

    for (size_t i = 0; i < candidate_states.size(); ++i) {
        transition_contexts.emplace_back(StateTransitionContext{
            CRUISE_WAIT, candidate_states[i], request_types[i]});
    }
}

void CruiseState::Change::get_state_transition_candidates(
    FSMContext &context, StateTransitionContexts &transition_contexts) {
    auto lc_transition_evaluator = context.lc_transition_evaluator;
    std::vector<ScenarioStateEnum> candidate_states;
    std::vector<RequestType> request_types;
    const auto &request_source =
        context.session->best_trigger_decision().request_source;
    const auto &request_type =
        context.session->best_trigger_decision().request_type;

    if (lc_transition_evaluator->can_transition_from_change(request_source,
                                                            request_type,
                                                            CRUISE_KEEP)) {
        candidate_states.emplace_back(CRUISE_KEEP);
        request_types.emplace_back(NO_CHANGE);
    } else {
        // [TODO] 暂未实现back状态
        candidate_states.emplace_back(CRUISE_CHANGE);
        request_types.emplace_back(request_type);
    }

    // else if (lc_transition_evaluator->can_transition_from_change(
    //              request_source, CRUISE_CHANGE)) {
    //     candidate_states.emplace_back(CRUISE_CHANGE);
    //     candidate_states.emplace_back(CRUISE_BACK);
    // }
    // else {
    //     candidate_states.emplace_back(CRUISE_BACK);
    // }

    for (size_t i = 0; i < candidate_states.size(); ++i) {
        transition_contexts.emplace_back(StateTransitionContext{
            CRUISE_CHANGE, candidate_states[i], request_types[i]});
    }
}

void CruiseState::Back::get_state_transition_candidates(
    FSMContext &context, StateTransitionContexts &transition_contexts) {
    auto lc_transition_evaluator = context.lc_transition_evaluator;
    std::vector<ScenarioStateEnum> candidate_states;
    std::vector<RequestType> request_types;
    const auto &request_source =
        context.session->best_trigger_decision().request_source;
    const auto &request_type =
        context.session->best_trigger_decision().request_type;

    if (lc_transition_evaluator->can_transition_from_back(request_source,
                                                          CRUISE_WAIT)) {
        candidate_states.emplace_back(CRUISE_WAIT);
        request_types.emplace_back(NO_CHANGE);
    } else if (lc_transition_evaluator->can_transition_from_back(
                   request_source, CRUISE_CHANGE)) {
        candidate_states.emplace_back(CRUISE_CHANGE);
        request_types.emplace_back(request_type);
        if (lc_transition_evaluator->can_transition_from_back(request_source,
                                                              CRUISE_BACK)) {
            candidate_states.emplace_back(CRUISE_BACK);
            request_types.emplace_back(NO_CHANGE);
        }
    } else if (lc_transition_evaluator->can_transition_from_back(request_source,
                                                                 CRUISE_BACK)) {
        candidate_states.emplace_back(CRUISE_BACK);
        request_types.emplace_back(NO_CHANGE);
    } else {
        candidate_states.emplace_back(CRUISE_KEEP);
        request_types.emplace_back(NO_CHANGE);
    }

    for (size_t i = 0; i < candidate_states.size(); ++i) {
        transition_contexts.emplace_back(StateTransitionContext{
            CRUISE_BACK, candidate_states[i], request_types[i]});
    }
}

}  // namespace planning
}  // namespace arcsoft
