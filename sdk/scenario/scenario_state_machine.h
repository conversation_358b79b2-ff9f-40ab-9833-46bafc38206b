/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __SCENARIO_STATE_MACHINE_H__
#define __SCENARIO_STATE_MACHINE_H__

#include "common/utils.h"
#include "cruise_state.h"

namespace arcsoft {
namespace planning {

using ScenarioFSM =
    M::PeerRoot<M::Composite<CruiseState, CruiseState::Keep, CruiseState::Wait,
                             CruiseState::Change, CruiseState::Back>>;

class ScenarioStateMachine
    : public std::enable_shared_from_this<ScenarioStateMachine> {
public:
    explicit ScenarioStateMachine(framework::Session* session)
        : scenario_fsm_(fsm_context_), session_(session) {}

    ~ScenarioStateMachine() = default;

    void Init();

    bool Process();

    const RequestType& turn_signal() const { return turn_signal_; }
    const double turn_signal_on_time() const { return turn_signal_on_time_; }

private:
    void update_state_machine();

    bool calculate_turn_signal();

    void reset_state_machine();
    template <typename T>
    void change_state_external() {
        if (fsm_context_.state == type2int<T>::value) {
            return;
        }

        AINFO << "Change state external from [" << fsm_context_.name << "] to ["
              << type2name<T>::name << "].";

        fsm_context_.external = true;
        (void)scenario_fsm_.changeTo<T>();
        scenario_fsm_.update();

        fsm_context_.external = false;
        fsm_context_.state = type2int<T>::value;
        fsm_context_.name = type2name<T>::name;
    }

    FSMContext fsm_context_;
    ScenarioFSM scenario_fsm_;
    framework::Session* session_;
    RequestType turn_signal_ = NO_CHANGE;
    double turn_signal_on_time_ = 0.0;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __SCENARIO_STATE_MACHINE_H__ */
