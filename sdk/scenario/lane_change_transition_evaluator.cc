/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "lane_change_transition_evaluator.h"

#include "scenario_state_machine.h"

namespace arcsoft {
namespace planning {

constexpr double kManualRequestLowSpeedThreshold = 5.0;

bool LaneChangeTransitionEvaluator::Process(const FSMContext& context) {
    // 1. 更新基础信息
    ego_speed_ = session_->planning_start_point().v;
    request_source_ = session_->best_trigger_decision().request_source;
    request_type_ = session_->best_trigger_decision().request_type;
    same_direction_ =
        (request_type_ != NO_CHANGE) && (request_type_ == last_request_type_);

    AINFO << " request_source_" << request_source_ << " last_request_type_"
          << last_request_type_ << " request_type_" << request_type_
          << " same_direction_" << same_direction_;

    uint32_t target_lane_id = 0;
    if (request_type_ != RequestType::NO_CHANGE) {
        if (last_request_type_ == RequestType::NO_CHANGE) {
            const auto& current_lane = session_->current_lane();
            if (current_lane == nullptr) {
                return false;
            }
            if (request_type_ == RequestType::LEFT_CHANGE) {
                if (current_lane->FindSideNearestReferenceLine(
                        session_->reference_lines().Items(), true,
                        target_lane_id)) {
                    AINFO
                        << "find left side nearest reference line success, new "
                        << "target lane id: " << target_lane_id;
                } else {
                    target_lane_id = current_lane->left_lane_id();
                }
            } else if (request_type_ == RequestType::RIGHT_CHANGE) {
                if (current_lane->FindSideNearestReferenceLine(
                        session_->reference_lines().Items(), false,
                        target_lane_id)) {
                    AINFO << "find right side nearest reference line success, "
                          << "new target lane id: " << target_lane_id;
                } else {
                    target_lane_id = current_lane->right_lane_id();
                }
            }
            AINFO << "[First Enter] target_lane_id: " << target_lane_id;
        } else {
            if (session_->target_lane() != nullptr) {
                /// 第一帧目标车道取最近的左右侧车道，后续目标车道与上一帧的目标车道作匹配
                if (session_->target_lane()->FindNearestReferenceLine(
                        session_->reference_lines().Items(), target_lane_id)) {
                    AINFO
                        << "last target lane id: "
                        << session_->target_lane()->id()
                        << ", find nearest reference line success, new target "
                        << "lane id: " << target_lane_id;
                } else {
                    target_lane_id = session_->target_lane()->id();
                    AINFO << "last target lane id: "
                          << session_->target_lane()->id()
                          << ", find nearest reference line failed, keep last "
                          << "target lane id: " << target_lane_id;
                }
            } else {
                AINFO << "session_->target_lane() is nullptr";
            }
        }
    }
    /// 判断是否到达目标车道，投影自车box角点到目标车道参考线，判断是否在车道内
    if (session_->target_lane() != nullptr) {
        auto target_lane_frenet_coord = session_->target_lane()->frenet_coord();
        bool is_reached = false;
        for (const auto& pt :
             session_->local_view().vehicle_state.box.GetAllCorners()) {
            std::array<double, 3> s_condition;
            std::array<double, 3> l_condition;
            if (!target_lane_frenet_coord->CartCoord2FrenetCoord(
                    pt.x(), pt.y(),
                    session_->local_view().vehicle_state.linear_velocity,
                    session_->local_view().vehicle_state.linear_acceleration,
                    session_->local_view().vehicle_state.heading,
                    session_->local_view().vehicle_state.kappa, &s_condition,
                    &l_condition)) {
                continue;
            }
            if (s_condition[0] < 0 ||
                s_condition[0] > session_->target_lane()->Length()) {
                continue;
            }
            double target_lane_left_width = 0.0;
            double target_lane_right_width = 0.0;
            if (session_->target_lane()->GetLaneWidth(
                    s_condition[0], target_lane_left_width,
                    target_lane_right_width)) {
                AINFO << "zcq debug box corner point target lane info: sl("
                      << std::fixed << std::setprecision(2) << s_condition[0]
                      << ", " << l_condition[0] << "), target lane width("
                      << target_lane_left_width << ", "
                      << target_lane_right_width << ")";
                if ((l_condition[0] < 0 &&
                     std::fabs(l_condition[0]) < target_lane_right_width) ||
                    (l_condition[0] > 0 &&
                     std::fabs(l_condition[0]) < target_lane_left_width)) {
                    is_reached = true;
                    session_->set_has_reached_target_lane(is_reached);
                    break;
                }
            }
        }
        if (!is_reached) {
            session_->set_has_reached_target_lane(false);
        }
        AINFO << "zcq debug has_reached_target_lane: "
              << session_->has_reached_target_lane();
    }

    last_request_type_ = request_type_;
    if (target_lane_id == 0) {
        if (session_->has_reached_target_lane() &&
            session_->scenario_state_info().target_scenario_state !=
                ScenarioStateEnum::CRUISE_KEEP) {
            if (session_->target_lane() != nullptr) {
                if (session_->target_lane()->FindNearestReferenceLine(
                        session_->reference_lines().Items(), target_lane_id)) {
                    session_->set_target_lane(
                        session_->reference_lines().Find(target_lane_id));
                    AINFO
                        << "last target lane id: "
                        << session_->target_lane()->id()
                        << ", find nearest reference line success, new target "
                        << "lane id: " << target_lane_id;
                } else {
                    target_lane_id = session_->target_lane()->id();
                    session_->set_target_lane(
                        session_->reference_lines().Find(target_lane_id));
                    AINFO << "last target lane id: "
                          << session_->target_lane()->id()
                          << ", find nearest reference line failed, keep last "
                          << "target lane id: " << target_lane_id;
                }
            } else {
                return false;
            }
        } else {
            session_->set_target_lane(nullptr);
            return false;
        }
    } else {
        const auto& curr_target_lane =
            session_->reference_lines().Find(target_lane_id);
        if (curr_target_lane != nullptr &&
            curr_target_lane->is_valid() == false) {
            session_->set_target_lane(nullptr);
            return false;
        }
        session_->set_target_lane(curr_target_lane);
    }

    // to do:判断两条参考线是否为一致
    // if (last_target_lane != nullptr && curr_target_lane != nullptr) {
    //     // 计算hausdorff distance
    // }

    // 5. 更新转向灯状态
    auto state_machine = context.state_machine;
    if (state_machine->turn_signal() != NO_CHANGE) {
        const double turn_signal_on_duration =
            common::get_system_time() - state_machine->turn_signal_on_time();
        constexpr double kTurnSignalOnTimeThreshold = 1.0;
        is_turn_signal_on_enough_ =
            turn_signal_on_duration > kTurnSignalOnTimeThreshold;
    } else {
        is_turn_signal_on_enough_ = false;
    }
    return true;
}

bool LaneChangeTransitionEvaluator::check_keep2wait_conditions() {
    if (!same_direction_) {
        AWARN << "[CheckAvailability] <== Flip-flop lane change request "
                 "detected. "
              << "Current: " << static_cast<int>(request_type_)
              << ", Last: " << static_cast<int>(last_request_type_)
              << ". Result: false.";
        return false;
    }

    const auto& current_line = session_->current_lane();
    if (current_line == nullptr) {
        AERROR << "[CheckAvailability] <== Critical error: Current lane is "
                  "null. Result: false.";
        return false;
    }

    // 条件2: 虚线长度检查
    double dotted_length = calculate_crossable_length_from_ego_until_solid(
        session_->current_lane()->id());
    AINFO << "Transition condition Dotted line length (" << dotted_length
          << "m)";
    if (dotted_length < kMinDottedLineLength) {
        AINFO << "Transition condition failed: Dotted line length ("
              << dotted_length << "m) is less than the minimum required ("
              << kMinDottedLineLength << "m).";
        return false;
    }

    // 条件3: 目标车道是否存在碰撞风险
    if (!check_target_lane_collision_risk()) {
        // 具体的失败日志在子函数中打印
        return false;
    }

    // 所有条件均满足
    AINFO << "All keep2wait transition conditions met.";
    return true;
}

bool LaneChangeTransitionEvaluator::check_target_lane_collision_risk() const {
    // 1. 获取目标车道并立即检查
    const auto& target_lane = session_->target_lane();
    if (!target_lane) {
        AINFO << "Cannot check collision risk: target lane in session is null.";
        return false;
    }

    // 2. 调用新函数并使用结构化绑定解包
    const auto& [closest_rear_obs, closest_front_obs] =
        find_closest_obstacles(*target_lane);

    // 3. 获取目标参考线，并优先使用 const 访问器
    const auto& target_lane_id = target_lane->id();
    const auto& target_ref_line =
        session_->reference_lines().Find(target_lane_id);

    if (target_ref_line == nullptr) {
        AERROR << "Target reference line for lane " << target_lane_id
               << " not found in reference line set.";
        return false;
    }

    if (target_ref_line->frenet_coord() == nullptr) {
        return false;
    }

    // [TODO] 待调试前后方障碍物的碰撞阈值
    return true;

    // 4. 获取自车状态
    const double ego_s = target_ref_line->frenet_coord()->ego_frenet_sl().s;
    const double ego_speed = session_->planning_start_point().v;

    // 5. 检查后方障碍物
    if (closest_rear_obs &&
        !IsRearObstacleSafe(*closest_rear_obs, ego_s, *target_lane)) {
        AINFO << "Transition blocked by closest rear obstacle: "
              << closest_rear_obs->id;
        return false;
    }

    // 6. 检查前方障碍物
    if (closest_front_obs &&
        !IsFrontObstacleSafe(*closest_front_obs, ego_s, ego_speed)) {
        AINFO << "Transition blocked by closest front obstacle: "
              << closest_front_obs->id;
        return false;
    }

    // 7. 所有检查通过
    return true;
}

void LaneChangeTransitionEvaluator::log_target_lane_obstacles(
    const ReferenceLine& target_ref_lane) {
    std::stringstream ss;
    ss << "[EvaluateTransitions] Obstacles on target lane: [";

    for (const auto& frenet_obstacle : target_ref_lane.frenet_obstacles()) {
        ss << "ID " << frenet_obstacle.id << " (s=" << frenet_obstacle.s
           << "m), ";
    }

    // 清理字符串格式
    std::string log_str = ss.str();
    if (log_str.length() > 2) {
        log_str.pop_back();  // 移除空格
        log_str.pop_back();  // 移除逗号
    }
    log_str += "]";

    AINFO << log_str;
}

ClosestObstacles LaneChangeTransitionEvaluator::find_closest_obstacles(
    const ReferenceLine& target_ref_lane) const {  // 匹配新的函数签名

    ClosestObstacles result;  // 创建一个本地的 result 对象

    double max_rear_s = -std::numeric_limits<double>::max();
    double min_front_s = std::numeric_limits<double>::max();
    constexpr double kMinLatDistance = 1.6;

    for (const auto& frenet_obstacle : target_ref_lane.frenet_obstacles()) {
        // 距离target_lane横向距离过远的，进行过滤
        if (std::fabs(frenet_obstacle.l) > kMinLatDistance) {
            continue;
        }

        if (frenet_obstacle.s < 0) {  // 后车
            if (frenet_obstacle.s > max_rear_s) {
                max_rear_s = frenet_obstacle.s;
                result.rear_obs = &frenet_obstacle;  // 修改本地 result 对象
            }
        } else {  // 前车 (s >= 0)
            if (frenet_obstacle.s < min_front_s) {
                min_front_s = frenet_obstacle.s;
                result.front_obs = &frenet_obstacle;  // 修改本地 result 对象
            }
        }
    }

    return result;  // 通过值返回 result 对象
}

bool LaneChangeTransitionEvaluator::IsRearObstacleSafe(
    const FrenetObstacle& frenet_obstacle, double ego_s,
    const ReferenceLine& target_ref_line) const {
    AINFO << "[IsRearObstacleSafe] ==> Evaluating rear obstacle ID: "
          << frenet_obstacle.id << " at s=" << frenet_obstacle.s
          << "m. Ego s=" << ego_s << "m.";

    // --- 步骤 1: 静态距离检查 (Gap Check) ---
    // Frenet坐标系下，后车s为负值，因此其绝对值代表了与自车的纵向距离
    const double rear_gap = std::abs(frenet_obstacle.s);
    AINFO << "[IsRearObstacleSafe] Step 1: Static Gap Check. Rear gap is "
          << rear_gap << "m.";

    // 检查静态距离是否小于最小要求
    if (rear_gap < kMinRearGap) {
        // 注意：您的原始代码中这里可能错误地与 kMinFrontGap 比较，已修正为
        // kMinRearGap
        AINFO << "[IsRearObstacleSafe] <== Unsafe: Rear obstacle "
              << frenet_obstacle.id << " is too close. Gap " << rear_gap
              << "m < kMinRearGap " << kMinRearGap << "m.";
        return false;
    }

    // --- 步骤 2: 获取障碍物轨迹并构建自车多边形 ---
    AINFO << "[IsRearObstacleSafe] Step 2: Preparing for dynamic collision "
             "check.";
    const auto& obstacle = session_->obstacles().Find(frenet_obstacle.id);
    if (!obstacle || obstacle->trajectory().empty()) {
        AINFO << "[IsRearObstacleSafe] <== Safe: Obstacle "
              << frenet_obstacle.id
              << " not found or has no predicted trajectory. Assuming safe by "
                 "default.";
        // 如果找不到障碍物或其轨迹，保守起见可以返回false，但更常见的做法是认为其安全（因为无法预测威胁）
        return true;
    }

    // 获取自车在目标车道参考线上的投影点姿态
    const auto& ego_projection_pose =
        target_ref_line.GetNearestReferencePoint(ego_s);

    // 计算几何中心相对于参考点(后轴中心)的纵向偏移
    const double ego_center_longitudinal_offset =
        (front_edge_to_center - back_edge_to_center) * 0.5;

    // 创建偏移向量 (参考点在横向中心线上，横向偏移为0)
    Vec2d ego_center_map_frame(ego_center_longitudinal_offset, 0.0);

    // //
    // 将偏移向量旋转到世界坐标系（自旋操作，将局部坐标下的偏移转换到全局坐标）
    // ego_center_map_frame.SelfRotate(ego_projection_pose.heading);

    ego_center_map_frame.set_x(ego_center_map_frame.x() +
                               ego_projection_pose.x);

    ego_center_map_frame.set_y(ego_center_map_frame.y() +
                               ego_projection_pose.y);

    // 构建自车包围盒，加入横向安全冗余
    const double lateral_buffer = 0.1;  // 10cm的横向安全buffer
    common::Box2d adc_box(ego_center_map_frame, ego_projection_pose.heading,
                          length, width + 2 * lateral_buffer);

    // 创建自车的静态多边形
    Polygon2d adc_polygon(adc_box);
    // AINFO << "[IsRearObstacleSafe] ADC polygon for collision check has been "
    //          "constructed at ("
    //       << ego_center_projected.x() << ", " << ego_center_projected.y()
    //       << ") with heading " << ego_projection_pose.heading << ".";

    // --- 步骤 3: 遍历后车轨迹，进行碰撞检查 ---
    AINFO
        << "[IsRearObstacleSafe] Step 3: Checking for collision along obstacle "
        << frenet_obstacle.id << "'s predicted trajectory ("
        << obstacle->trajectory().size() << " points).";

    double first_collision_time = std::numeric_limits<double>::max();
    bool collision_found = false;

    for (const auto& obs_traj_point : obstacle->trajectory()) {
        // 构建该时刻的障碍物多边形
        Vec2d obs_center(obs_traj_point.x, obs_traj_point.y);
        common::Box2d obs_box(obs_center, obs_traj_point.heading,
                              obstacle->length(), obstacle->width());
        Polygon2d obs_polygon(obs_box);

        // 执行碰撞检查
        if (obs_polygon.HasOverlap(adc_polygon)) {
            first_collision_time = obs_traj_point.t;
            collision_found = true;
            AINFO << "[IsRearObstacleSafe] Collision detected with obstacle "
                  << frenet_obstacle.id << " at time t=" << first_collision_time
                  << "s.";
            // 找到首次碰撞后立即退出循环
            break;
        }
    }

    if (!collision_found) {
        AINFO << "[IsRearObstacleSafe] <== Safe: No collision predicted with "
                 "rear obstacle "
              << frenet_obstacle.id;
        return true;
    }

    // --- 步骤 4: 根据首次碰撞时间做出最终判断 ---
    AINFO << "[IsRearObstacleSafe] Step 4: Final decision based on "
             "Time-To-Collision (TTC).";
    if (first_collision_time < kMinRearTTC) {
        AINFO << "[IsRearObstacleSafe] <== Unsafe: Predicted rear collision is "
                 "too soon. TTC "
              << first_collision_time << "s < kMinRearTTC " << kMinRearTTC
              << "s.";
        return false;
    }

    AINFO << "[IsRearObstacleSafe] <== Safe: Predicted rear collision is far "
             "enough in the future (TTC="
          << first_collision_time << "s >= kMinRearTTC " << kMinRearTTC
          << "s).";
    return true;
}

bool LaneChangeTransitionEvaluator::IsFrontObstacleSafe(
    const FrenetObstacle& frenet_obstacle, double ego_s_initial,
    double ego_speed) const {
    // --- Log function entry and inputs ---
    AINFO << "[IsFrontObstacleSafe] Evaluating front obstacle: "
          << frenet_obstacle.id << " | Ego S: " << ego_s_initial
          << "m, Ego Speed: " << ego_speed << "m/s";

    // --- 步骤 1: 获取必要的数据 ---
    const auto& obstacle = session_->obstacles().Find(frenet_obstacle.id);
    if (!obstacle) {
        // 找不到障碍物信息，保守认为不安全
        AINFO << "[IsFrontObstacleSafe] Failed to find obstacle with ID: "
              << frenet_obstacle.id;
        return false;
    }

    // --- 步骤 2: 静态距离检查 (Gap Check) ---
    // 计算自车前端到障碍物后端的物理距离
    const double current_gap =
        frenet_obstacle.s - obstacle->length() / 2.0 - front_edge_to_center;

    AINFO << "[IsFrontObstacleSafe] Obstacle " << frenet_obstacle.id
          << ": s=" << frenet_obstacle.s << "m, length=" << obstacle->length()
          << "m."
          << " Calculated current_gap: " << current_gap << "m";

    // 检查静态距离是否小于最小要求
    if (current_gap < kMinFrontGap) {
        AINFO << "[IsFrontObstacleSafe] Unsafe: Front obstacle "
              << frenet_obstacle.id << " is too close. Gap " << current_gap
              << "m < kMinFrontGap " << kMinFrontGap << "m.";
        return false;
    }

    // --- 步骤 3: 动态时间检查 (THW Check) ---
    // 计算车头时距 (Time Headway)
    const double ego_current_speed = session_->planning_start_point().v;
    // 防止除以零
    if (ego_current_speed < 1e-3) {
        AINFO << "[IsFrontObstacleSafe] Ego speed is nearly zero ("
              << ego_current_speed << "m/s), skipping THW check.";
        // 如果自车静止，仅基于静态距离判断是安全的
        AINFO << "[IsFrontObstacleSafe] Safe: Front obstacle "
              << frenet_obstacle.id
              << " passed gap check, and ego is stationary.";
        return true;
    }

    const double thw = current_gap / ego_current_speed;

    AINFO << "[IsFrontObstacleSafe] Obstacle " << frenet_obstacle.id
          << ": Calculated THW=" << thw << "s (gap=" << current_gap
          << "m / ego_speed=" << ego_current_speed << "m/s)";

    // 检查车头时距是否小于最小要求
    if (thw < kMinFrontTHW) {
        AINFO << "[IsFrontObstacleSafe] Unsafe: Front obstacle "
              << frenet_obstacle.id << " has insufficient THW. THW " << thw
              << "s < kMinFrontTHW " << kMinFrontTHW << "s.";
        return false;
    }

    // --- 步骤 4: 所有检查通过 ---
    AINFO << "[IsFrontObstacleSafe] Safe: Front obstacle " << frenet_obstacle.id
          << " passed all checks.";
    return true;
}

/**
 * @brief 从自车当前位置开始，计算目标车道侧的可跨越车道线长度，
 *        直到遇到第一段不可跨越的车道线为止。
 *
 * @param line_id 目标参考线的 ID
 * @return double 从自车位置起的可跨越车道线长度
 */
double
LaneChangeTransitionEvaluator::calculate_crossable_length_from_ego_until_solid(
    const uint32_t& lane_id) {
    // 1. 基本的有效性检查
    if (request_type_ == RequestType::NO_CHANGE) {
        return 0.0;
    }

    const auto& ref_line_ptr = session_->reference_lines().Find(lane_id);
    if (!ref_line_ptr || ref_line_ptr->smoothed_points().size() < 2 ||
        ref_line_ptr->frenet_coord() == nullptr) {
        return 0.0;
    }

    // 2. 获取投影点
    const auto& ego_projection = ref_line_ptr->frenet_coord()->ego_frenet_sl();
    const auto& points = ref_line_ptr->smoothed_points();
    if (ego_projection.s > points.back().s) {
        return 0.0;
    }

    // 3. 从第一个点开始遍历,计算可跨越长度
    double crossable_length = 0.0;

    for (size_t i = 0; i + 1 < points.size(); ++i) {
        const auto& current_point = points[i];
        const auto& next_point = points[i + 1];

        // 如果当前点的s值小于自车位置,跳过该点
        if (next_point.s <= ego_projection.s) {
            continue;
        }

        // 判断当前路段的线型是否可跨越
        LaneDividerType target_divider_type = LaneDividerType::UNKNOWN;
        if (request_type_ == RequestType::LEFT_CHANGE) {
            target_divider_type = current_point.left_divider_type;
        } else if (request_type_ == RequestType::RIGHT_CHANGE) {
            target_divider_type = current_point.right_divider_type;
        }

        if (!is_divider_crossable(target_divider_type)) {
            break;
        }

        // 计算当前路段的可跨越长度
        double segment_start_s = std::max(ego_projection.s, current_point.s);
        double segment_end_s = next_point.s;

        if (segment_end_s > segment_start_s) {
            crossable_length += (segment_end_s - segment_start_s);
        }
    }

    return crossable_length;
}

bool LaneChangeTransitionEvaluator::is_divider_crossable(
    LaneDividerType divider_type) const {
    switch (divider_type) {
        case LaneDividerType::DASHED:
        case LaneDividerType::DASHED_SOLID:  // 虚线在内侧，允许跨越
        case LaneDividerType::DASHED_DASHED:
            return true;

        case LaneDividerType::UNKNOWN:
        case LaneDividerType::SOLID:
        case LaneDividerType::SOLID_DASHED:  // 实线在内侧，不允许跨越
        case LaneDividerType::SOLID_SOLID:
        default:
            return false;
    }
}

const bool LaneChangeTransitionEvaluator::can_transition_from_keep(
    const RequestSource source, const ScenarioStateEnum target_state) {
    bool transition_success = false;
    switch (target_state) {
        case CRUISE_KEEP: {
            transition_success = true;
            AINFO << "Transition Status: KEEP_2_KEEP";
            break;
        }

        case CRUISE_WAIT: {
            switch (source) {
                case MANUAL_REQUEST: {
                    transition_success = check_keep2wait_conditions();
                    AINFO << "zll test MANUAL KEEP_2_WAIT transition_success"
                          << transition_success;
                    break;
                }
                case NAVI_REQUEST: {
                    AINFO << "Transition Status: KEEP_2_WAIT";
                    transition_success = check_keep2wait_conditions();
                    break;
                }
                default: {
                    transition_success = false;
                    break;
                }
            }
            break;
        }

        default:
            break;
    }

    return transition_success;
}

const bool LaneChangeTransitionEvaluator::can_transition_from_wait(
    const RequestSource source, const ScenarioStateEnum target_state) {
    bool transition_success = false;
    switch (target_state) {
        case CRUISE_KEEP: {
            transition_success = true;
            AINFO << "Transition Status: WAIT_2_KEEP";
            break;
        }
        case CRUISE_WAIT: {
            switch (source) {
                case MANUAL_REQUEST: {
                    transition_success = same_direction_;

                    break;
                }
                case NAVI_REQUEST: {
                    // [TODO]
                    transition_success = same_direction_;

                    break;
                }
                default: {
                    transition_success = false;
                    break;
                }
            }
            AINFO << "Transition Status: WAIT_2_WAIT " << transition_success;
            break;
        }
        case CRUISE_CHANGE: {
            switch (source) {
                case MANUAL_REQUEST: {
                    transition_success =
                        same_direction_ && is_turn_signal_on_enough_;
                    if (!same_direction_) {
                        AINFO << "same_direction_ : false";
                    }
                    if (!is_turn_signal_on_enough_) {
                        AINFO << "is_turn_signal_on_enough_: false";
                    }
                    AINFO << "zll test MANUAL WAIT_2_CHANGE transition_success"
                             "evalautor"
                          << transition_success;
                    break;
                }
                case NAVI_REQUEST: {
                    // [TODO]
                    // transition_success = same_direction_ && is_gap_available_
                    // &&
                    //                      is_turn_signal_on_enough_;
                    AINFO << "Enter wait_2_change evalautor";
                    if (!check_target_lane_collision_risk()) {
                        AINFO << "check_target_lane_collision_risk() : false";
                    }

                    transition_success = same_direction_ &&
                                         check_target_lane_collision_risk() &&
                                         is_turn_signal_on_enough_;
                    break;
                }
                default: {
                    transition_success = false;
                    break;
                }
            }
            AINFO << "Transition Status: WAIT_2_CHANGE " << transition_success;

            break;
        }
    }

    return transition_success;
}

/**
 * @brief 判断换道过程是否完成。
 * @return 如果车辆已在新车道内稳定，则返回true。
 */
bool LaneChangeTransitionEvaluator::is_lane_change_finish(
    const RequestSource request_source, const RequestType request_type) {
    const auto& target_ref_line = session_->target_lane();

    // [TODO]
    if (target_ref_line == nullptr) {
        AINFO << "target_ref_line is nullptr";
        return true;
    }

    // 添加对手动拨杆变道取消的退出判断
    if (request_source == MANUAL_REQUEST && request_type == NO_CHANGE &&
        !session_->has_reached_target_lane()) {
        AINFO
            << "request_source is MANUAL_REQUEST and request_type is NO_CHANGE "
               "and has not reached target lane";
        return true;
    }

    if (!session_) {
        AERROR << "Session is not available.";
        return false;
    }

    const FrenetCoordinateSystem* frenet_coord =
        target_ref_line->frenet_coord();
    if (frenet_coord == nullptr) {
        AERROR << "Frenet coordinate system for target lane is null.";
        return false;
    }

    auto planning_start_point = session_->planning_start_point();
    double lateral_offset;
    double heading_offset;

    if (!frenet_coord->GetLateralOffsetAndHeading(
            planning_start_point.x, planning_start_point.y,
            planning_start_point.heading, lateral_offset, heading_offset)) {
        AERROR << "Failed to compute lateral offset and heading.";
        return false;
    }

    constexpr double kLateralOffsetThreshold = 0.5;  // 横向偏移阈值（米）
    constexpr double kFinishHeadingDiffThreshold =
        2.5 * M_PI / 180.0;  // 航向角差异阈值（弧度）

    bool is_position_ok = std::abs(lateral_offset) < kLateralOffsetThreshold;
    bool is_heading_ok = heading_offset < kFinishHeadingDiffThreshold;
    AINFO << "current_lane_id: " << session_->current_lane()->id();
    AINFO << "target_lane_id: " << session_->target_lane()->id();
    if (!is_position_ok) {
        AINFO << "position is not ok: " << std::abs(lateral_offset);
    }
    if (!is_heading_ok) {
        AINFO << "heading is not ok: " << std::abs(heading_offset);
    }

    if (is_position_ok && is_heading_ok &&
        session_->current_lane()->id() == session_->target_lane()->id()) {
        AINFO << "is finish lanechange: " << std::abs(heading_offset);
        return true;
    }
    return false;
}

const bool LaneChangeTransitionEvaluator::can_transition_from_change(
    const RequestSource request_source, const RequestType request_type,
    ScenarioStateEnum target_state) {
    bool transition_success = false;
    AINFO << "Transition evaluate: CHANGE_2_?";

    switch (target_state) {
        case CRUISE_KEEP: {
            // TODO: 计算是否完成变道
            transition_success =
                is_lane_change_finish(request_source, request_type);
            AINFO << "zll test CHANGE_2_KEEP transition_success"
                  << transition_success;
            AINFO << "Transition Status: CHANGE_2_KEEP";
            break;
        }
        case CRUISE_WAIT: {
            transition_success = false;
            AINFO << "Transition Status: CHANGE_2_WAIT";
            break;
        }
        case CRUISE_CHANGE: {
            // TODO: 计算是否还维持变道，tlane是否存在等等
            transition_success = same_direction_;
            AINFO << "Transition Status: CHANGE_2_CHANGE "
                  << transition_success;

            break;
        }
    }

    return transition_success;

    return true;
}

const bool LaneChangeTransitionEvaluator::can_transition_from_back(
    const RequestSource source, const ScenarioStateEnum target_state) {
    // [TODO]
    return true;
}

}  // namespace planning
}  // namespace arcsoft