{"lane_follow_path": {"path_reference_l_weight": 100.0, "lateral_derivative_bound_default": 2.0, "dddl_weight": 500.0, "ddl_weight": 50.0, "dl_weight": 20.0, "l_weight": 0.1, "weight_end_state_l": 100.0, "weight_end_state_dl": 100.0, "weight_end_state_ddl": 100.0, "max_iteration": 4000}, "lane_change_path": {"path_reference_l_weight": 100.0, "lateral_derivative_bound_default": 2.0, "dddl_weight": 5000.0, "ddl_weight": 500.0, "dl_weight": 0.5, "l_weight": 0.05, "weight_end_state_l": 10.0, "weight_end_state_dl": 10.0, "weight_end_state_ddl": 10.0, "max_iteration": 4000}}