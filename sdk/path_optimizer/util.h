/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#pragma once

#include <algorithm>
#include <cmath>
#include <iostream>
#include <string>
#include <tuple>
#include <utility>
#include <vector>

namespace arcsoft {
namespace planning {
namespace util {

bool left_arc_bound_with_heading_with_reverse_kappa(double delta_x, double r,
                                                    double heading,
                                                    double kappa,
                                                    double* result);

bool left_arc_bound_with_heading(double delta_x, double r, double heading,
                                 double* result);

bool right_arc_bound_with_heading_with_reverse_kappa(double delta_x, double r,
                                                     double heading,
                                                     double kappa,
                                                     double* result);
bool right_arc_bound_with_heading(double delta_x, double r, double heading,
                                  double* result);

}  // namespace util
}  // namespace planning
}  // namespace arcsoft