/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __PATH_OPTIMIZER_H__
#define __PATH_OPTIMIZER_H__

#include "common/basic_types.h"
#include "common/discrete_points_math.h"
#include "common/frenet_coordinate_system.h"
#include "framework/task.h"
#include "framework/task_runner.h"
#include "path_bounds_decider_util.h"
#include "piecewise_jerk_path_optimizer.h"
#include "sl_polygon.h"

namespace arcsoft {
namespace planning {

using json = nlohmann::json;

class PathOptimizer : public framework::Task {
public:
    PathOptimizer(const std::string& name, framework::Session* session)
        : Task(name, session), path_opti_method_(1) {}
    ~PathOptimizer() = default;

    void Init() override;
    bool Process(const size_t candidate_transition_context_id) override;

    bool LoadPathOptimizerParams(
        ScenarioStateEnum scenario_state = ScenarioStateEnum::CRUISE_KEEP);

    PathData GetCartesianPathFromFrenet(const FrenetFramePath& frenet_path);

private:
    json cached_config_;
    int path_opti_method_;
    std::shared_ptr<ReferenceLine> reference_line_ = nullptr;
    SLPoint ego_frenet_sl_info_;
    SLState init_sl_state_;
    std::vector<Point2D> adc_bound_points_;
    PiecewiseJerkPathConfig piecewise_jerk_path_config_;
    RequestType request_type_ = RequestType::NO_CHANGE;

    bool DecideLaneFollowPathBounds(std::vector<PathBoundary>* boundary);
    bool DecideLaneChangePathBounds(std::vector<PathBoundary>* boundary);
};

}  // namespace planning
}  // namespace arcsoft
#endif /* __PATH_OPTIMIZER_H__ */