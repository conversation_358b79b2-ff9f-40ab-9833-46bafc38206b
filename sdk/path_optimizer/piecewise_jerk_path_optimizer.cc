/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "piecewise_jerk_path_optimizer.h"

namespace arcsoft {
namespace planning {

bool PiecewiseJerkPathOptimizer::Solve(
    std::vector<PathBoundary>& path_boundaries,
    FrenetFramePath& candidate_path_data) {
    std::array<double, 3> end_state = {0.0, 0.0, 0.0};

    for (const auto& path_boundary : path_boundaries) {
        size_t path_boundary_size = path_boundary.boundary().size();
        if (path_boundary_size <= 1U) {
            AERROR << "Get invalid path boundary with size: "
                   << path_boundary_size;
            return false;
        }

        std::vector<double> opt_l, opt_dl, opt_ddl;
        std::vector<std::pair<double, double>> ddl_bounds;
        CalculateAccBound(path_boundary, *reference_line_, &ddl_bounds);

        double jerk_bound =
            EstimateJerkBoundary(std::fmax(init_sl_state_.first[1], 1e-12));
        if (is_change_lane_) {
            jerk_bound = 0.0001;
        }
        AINFO << "jerk_bound: " << jerk_bound;
        std::vector<double> ref_l(path_boundary_size, 0);
        std::vector<double> weight_ref_l(path_boundary_size, 0);

        UpdatePathRefWithBound(path_boundary, config_.path_reference_l_weight,
                               &ref_l, &weight_ref_l);
        bool res_opt = OptimizePath(
            init_sl_state_, end_state, ref_l, weight_ref_l, path_boundary,
            ddl_bounds, jerk_bound, config_, &opt_l, &opt_dl, &opt_ddl);
        if (res_opt) {
            candidate_path_data = ToPiecewiseJerkPath(opt_l, opt_dl, opt_ddl,
                                                      path_boundary.delta_s(),
                                                      path_boundary.start_s());
        }
    }
    if (candidate_path_data.empty()) {
        return false;
    }
    // for (size_t i = 0; i < 10; ++i) {
    //     const auto& point = candidate_path_data[i];
    //     AINFO << "FrenetFramePoint[" << i << "]: "
    //           << "s: " << point.s << ", l: " << point.l;
    // }

    return true;
}

FrenetFramePath PiecewiseJerkPathOptimizer::ToPiecewiseJerkPath(
    const std::vector<double>& x, const std::vector<double>& dx,
    const std::vector<double>& ddx, const double delta_s,
    const double start_s) {
    std::vector<Point2D> piecewise_jerk_path;
    piecewise_jerk_path.reserve(x.size());
    FrenetFramePath frenet_frame_path;

    for (size_t i = 0; i < x.size(); ++i) {
        double s = start_s + i * delta_s;
        double l = x[i];
        double dl = dx[i];
        double ddl = ddx[i];
        piecewise_jerk_path.emplace_back(s, l);

        // FrenetFramePoint frenet_frame_point;
        // frenet_frame_point.s = s;
        // frenet_frame_point.l = l;
        // frenet_frame_point.dl = dl;
        // frenet_frame_point.ddl = ddl;
        // frenet_frame_path.emplace_back(frenet_frame_point);
    }

    PiecewiseJerkTrajectory1d piecewise_jerk_traj(x.front(), dx.front(),
                                                  ddx.front());

    for (std::size_t i = 1; i < x.size(); ++i) {
        const auto dddl = (ddx[i] - ddx[i - 1]) / delta_s;
        piecewise_jerk_traj.AppendSegment(dddl, delta_s);
    }

    double accumulated_s = 0.0;
    while (accumulated_s < piecewise_jerk_traj.ParamLength()) {
        double l = piecewise_jerk_traj.Evaluate(0, accumulated_s);
        double dl = piecewise_jerk_traj.Evaluate(1, accumulated_s);
        double ddl = piecewise_jerk_traj.Evaluate(2, accumulated_s);

        FrenetFramePoint frenet_frame_point;
        frenet_frame_point.s = accumulated_s + start_s;
        frenet_frame_point.l = l;
        frenet_frame_point.dl = dl;
        frenet_frame_point.ddl = ddl;
        frenet_frame_path.push_back(std::move(frenet_frame_point));

        accumulated_s += 0.5;
    }

    // for (size_t i = 0; i < 10; i++) {
    //     AINFO << "FrenetFramePoint[" << i << "]: "
    //           << "s: " << frenet_frame_path[i].s
    //           << ", l: " << frenet_frame_path[i].l;
    // }
    return frenet_frame_path;
}

bool PiecewiseJerkPathOptimizer::OptimizePath(
    const SLState& init_state, const std::array<double, 3>& end_state,
    std::vector<double> l_ref, std::vector<double> l_ref_weight,
    const PathBoundary& path_boundary,
    const std::vector<std::pair<double, double>>& ddl_bounds, double dddl_bound,
    const PiecewiseJerkPathConfig& config, std::vector<double>* x,
    std::vector<double>* dx, std::vector<double>* ddx) {
    const auto& lat_boundaries = path_boundary.boundary();
    const size_t kNumKnots = lat_boundaries.size();
    AINFO << "kNumKnots: " << kNumKnots;

    double delta_s = path_boundary.delta_s();
    PiecewiseJerkPathProblem piecewise_jerk_problem(kNumKnots, delta_s,
                                                    init_state.second);

    const auto& adc_vertex_constraints = path_boundary.adc_vertex_bound();
    const auto& extra_bound = path_boundary.extra_path_bound();
    // get reference towing l
    std::vector<double> towing_l_ref;
    for (auto& path_boundary_pt : path_boundary) {
        towing_l_ref.emplace_back(path_boundary_pt.towing_l);
    }
    std::array<double, 3U> end_state_weight = {config.weight_end_state_l,
                                               config.weight_end_state_dl,
                                               config.weight_end_state_ddl};
    piecewise_jerk_problem.set_end_state_ref(end_state_weight, end_state);
    piecewise_jerk_problem.set_x_ref(std::move(l_ref_weight), l_ref);
    piecewise_jerk_problem.set_towing_x_ref(config.l_weight, towing_l_ref);

    // for debug:here should use std::move
    piecewise_jerk_problem.set_weight_x(config.l_weight);
    piecewise_jerk_problem.set_weight_dx(config.dl_weight);
    piecewise_jerk_problem.set_weight_ddx(config.ddl_weight);
    piecewise_jerk_problem.set_weight_dddx(config.dddl_weight);

    piecewise_jerk_problem.set_scale_factor({1.0, 10.0, 100.0});
    piecewise_jerk_problem.set_extra_constraints(extra_bound);
    piecewise_jerk_problem.set_vertex_constraints(adc_vertex_constraints);

    piecewise_jerk_problem.set_x_bounds(lat_boundaries);
    piecewise_jerk_problem.set_dx_bounds(
        -config.lateral_derivative_bound_default,
        config.lateral_derivative_bound_default);

    piecewise_jerk_problem.set_ddx_bounds(ddl_bounds);

    // for (size_t i = 0; i < ddl_bounds.size(); ++i) {
    //     AINFO << "ddl_bounds[" << i << "]: (" << ddl_bounds[i].first << ", "
    //           << ddl_bounds[i].second << ")";
    // }

    piecewise_jerk_problem.set_dddx_bound(dddl_bound);

    // AINFO << "dddl_bound: (" << -dddl_bound << ", " << dddl_bound << ")";

    bool success = piecewise_jerk_problem.Optimize(config.max_iteration);

    if (!success) {
        AERROR << path_boundary.label()
               << " piecewise jerk path optimizer failed";
        AINFO << "init s(" << init_state.first[0] << "," << init_state.first[1]
              << "," << init_state.first[2] << ") l (" << init_state.second[0]
              << "," << init_state.second[1] << "," << init_state.second[2];
        AINFO << "dx bound: " << config.lateral_derivative_bound_default;
        AINFO << "jerk bound: " << dddl_bound;
        return false;
    }

    *x = piecewise_jerk_problem.opt_x();
    *dx = piecewise_jerk_problem.opt_dx();
    *ddx = piecewise_jerk_problem.opt_ddx();
    // PrintBox print_box("opt_l_box");
    // for (size_t i = 0; i < kNumKnots; i++) {
    //     double s = i * path_boundary.delta_s() + path_boundary.start_s();
    //     print_curve.AddPoint(path_boundary.label() + "_opt_l", s, (*x)[i]);
    //     print_curve.AddPoint(path_boundary.label() + "_opt_dl", s, (*dx)[i]);
    //     print_curve.AddPoint(path_boundary.label() + "_opt_ddl", s,
    //     (*ddx)[i]); print_box.AddAdcBox(s, (*x)[i], std::atan((*dx)[i]),
    //     true);
    // }
    // print_curve.PrintToLog();
    // print_box.PrintToLog();
    return true;
}

void PiecewiseJerkPathOptimizer::CalculateAccBound(
    const PathBoundary& path_boundary, const ReferenceLine& reference_line,
    std::vector<std::pair<double, double>>* ddl_bounds) {
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();
    const double lat_acc_bound =
        std::tan(vehicle_config.max_steer_angle / vehicle_config.steer_ratio) /
        vehicle_config.wheel_base;
    size_t path_boundary_size = path_boundary.boundary().size();
    for (size_t i = 0; i < path_boundary_size; ++i) {
        double s = static_cast<double>(i) * path_boundary.delta_s() +
                   path_boundary.start_s() +
                   reference_line.frenet_coord()->ego_frenet_sl().s;
        double kappa = reference_line.GetNearestReferencePoint(s).curvature;
        // AINFO << "path kappa " << i << " s: " << s << " kappa: " << kappa;
        //  小角度下ddl与曲率近似一样
        //  车辆实际轨迹的曲率 ≈ 参考线曲率（kappa）+ 轨迹偏移引起的曲率（ddl）
        ddl_bounds->emplace_back(-lat_acc_bound - kappa, lat_acc_bound - kappa);
    }
}

double PiecewiseJerkPathOptimizer::EstimateJerkBoundary(
    const double vehicle_speed) {
    // [TODO]
    return 1.0;

    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();
    const double axis_distance = vehicle_config.wheel_base;
    const double max_yaw_rate =
        vehicle_config.max_steer_angle_rate / vehicle_config.steer_ratio;
    return max_yaw_rate / axis_distance / vehicle_speed;
}

void PiecewiseJerkPathOptimizer::UpdatePathRefWithBound(
    const PathBoundary& path_boundary, double weight,
    std::vector<double>* ref_l, std::vector<double>* weight_ref_l) {
    ref_l->resize(path_boundary.size());
    weight_ref_l->resize(path_boundary.size());
    const double kEpison = 1e-2;
    for (size_t i = 0; i < ref_l->size(); i++) {
        bool is_need_update_path_ref =
            (path_boundary[i].l_lower.type == BoundType::OBSTACLE ||
             path_boundary[i].l_upper.type == BoundType::OBSTACLE) &&
            (path_boundary[i].l_lower.l > path_boundary[i].towing_l - kEpison ||
             path_boundary[i].l_upper.l < path_boundary[i].towing_l + kEpison);
        if (is_need_update_path_ref) {
            ref_l->at(i) =
                (path_boundary[i].l_lower.l + path_boundary[i].l_upper.l) / 2.0;
            weight_ref_l->at(i) = weight;
            AINFO << "need_update_path_ref: s: " << path_boundary[i].s
                  << ", l: " << ref_l->at(i);
        } else {
            weight_ref_l->at(i) = 0;
        }
    }
}

}  // namespace planning
}  // namespace arcsoft