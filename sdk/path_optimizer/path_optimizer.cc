/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "path_optimizer.h"

namespace arcsoft {
namespace planning {

bool PathOptimizer::LoadPathOptimizerParams(ScenarioStateEnum scenario_state) {
    if (!cached_config_.empty()) {
        if (!cached_config_.contains("lane_follow_path")) {
            AERROR << "Missing 'lane_follow_path' in config";
            return false;
        }

        AINFO << "this is target_state: " << scenario_state;

        json param_config;
        if (scenario_state == ScenarioStateEnum::CRUISE_CHANGE) {
            param_config = cached_config_["lane_change_path"];
            AINFO << "Using lane_change_path parameters.";
        } else {
            param_config = cached_config_["lane_follow_path"];
            AINFO << "Using lane_follow_path parameters.";
        }
        piecewise_jerk_path_config_.path_reference_l_weight =
            param_config.value("path_reference_l_weight", 0.0);
        piecewise_jerk_path_config_.lateral_derivative_bound_default =
            param_config.value("lateral_derivative_bound_default", 0.0);
        piecewise_jerk_path_config_.dddl_weight =
            param_config.value("dddl_weight", 0.0);
        piecewise_jerk_path_config_.ddl_weight =
            param_config.value("ddl_weight", 0.0);
        piecewise_jerk_path_config_.dl_weight =
            param_config.value("dl_weight", 0.0);
        piecewise_jerk_path_config_.l_weight =
            param_config.value("l_weight", 0.0);
        piecewise_jerk_path_config_.weight_end_state_l =
            param_config.value("weight_end_state_l", 0.0);
        piecewise_jerk_path_config_.weight_end_state_dl =
            param_config.value("weight_end_state_dl", 0.0);
        piecewise_jerk_path_config_.weight_end_state_ddl =
            param_config.value("weight_end_state_ddl", 0.0);
        piecewise_jerk_path_config_.max_iteration =
            param_config.value("max_iteration", 0);

        // AINFO << "Loaded path optimizer parameters: "
        //       << "path_reference_l_weight: "
        //       << piecewise_jerk_path_config_.path_reference_l_weight
        //       << ", lateral_derivative_bound_default: "
        //       << piecewise_jerk_path_config_.lateral_derivative_bound_default
        //       << ", dddl_weight: " << piecewise_jerk_path_config_.dddl_weight
        //       << ", ddl_weight: " << piecewise_jerk_path_config_.ddl_weight
        //       << ", dl_weight: " << piecewise_jerk_path_config_.dl_weight
        //       << ", l_weight: " << piecewise_jerk_path_config_.l_weight
        //       << ", weight_end_state_l: "
        //       << piecewise_jerk_path_config_.weight_end_state_l
        //       << ", weight_end_state_dl: "
        //       << piecewise_jerk_path_config_.weight_end_state_dl
        //       << ", weight_end_state_ddl: "
        //       << piecewise_jerk_path_config_.weight_end_state_ddl
        //       << ", max_iteration: "
        //       << piecewise_jerk_path_config_.max_iteration;
    } else {
        if (scenario_state == ScenarioStateEnum::CRUISE_CHANGE) {
            // lane_change_path
            piecewise_jerk_path_config_.path_reference_l_weight = 100.0;
            piecewise_jerk_path_config_.lateral_derivative_bound_default = 2.0;
            piecewise_jerk_path_config_.dddl_weight = 5000.0;
            piecewise_jerk_path_config_.ddl_weight = 500.0;
            piecewise_jerk_path_config_.dl_weight = 0.5;
            piecewise_jerk_path_config_.l_weight = 0.05;
            piecewise_jerk_path_config_.weight_end_state_l = 10.0;
            piecewise_jerk_path_config_.weight_end_state_dl = 10.0;
            piecewise_jerk_path_config_.weight_end_state_ddl = 10.0;
            piecewise_jerk_path_config_.max_iteration = 4000;
        } else {
            piecewise_jerk_path_config_.path_reference_l_weight = 100.0;
            piecewise_jerk_path_config_.lateral_derivative_bound_default = 2.0;
            piecewise_jerk_path_config_.dddl_weight = 500.0;
            piecewise_jerk_path_config_.ddl_weight = 50.0;
            piecewise_jerk_path_config_.dl_weight = 20;
            piecewise_jerk_path_config_.l_weight = 0.1;
            piecewise_jerk_path_config_.weight_end_state_l = 100.0;
            piecewise_jerk_path_config_.weight_end_state_dl = 100.0;
            piecewise_jerk_path_config_.weight_end_state_ddl = 100.0;
            piecewise_jerk_path_config_.max_iteration = 4000;
        }
    }

    return true;
}

void PathOptimizer::Init() {
    std::ifstream input_file(PARAM_CONFIG_PATH);
    if (input_file) {
        json json_data;
        input_file >> json_data;
        // 解析并缓存配置
        cached_config_ = json_data;
    }
    return;
}

bool PathOptimizer::Process(const size_t candidate_transition_context_id) {
    std::unordered_map<size_t, std::vector<PathData>> path_map_info;
    // 平滑失败的话 使用空的
    session_->set_path_data_map(path_map_info);
    const auto& candidate_transition_context =
        session_->candidate_transition_context(candidate_transition_context_id);

    // 根据当前状态选择reference_line
    AINFO << "current_lane_id: " << session_->current_lane()->id();
    reference_line_ = (candidate_transition_context.target_state !=
                       ScenarioStateEnum::CRUISE_CHANGE)
                          ? session_->current_lane()
                          : session_->target_lane();

    LoadPathOptimizerParams(candidate_transition_context.target_state);
    request_type_ = session_->best_trigger_decision().request_type;
    AINFO << "request_type: " << request_type_;
    if (reference_line_ == nullptr) {
        AERROR << "reference_line_ is nullptr! ";
        return false;
    }

    // 获取当前车辆位置在参考线下的frenet坐标
    const auto* frenet_coord = reference_line_->frenet_coord();
    if (frenet_coord == nullptr) {
        AERROR << "frenet_coord is nullptr! ";
        return false;
    }

    ego_frenet_sl_info_ = frenet_coord->ego_frenet_sl();
    AINFO << "ego_frenet_sl_info: "
          << "s: " << ego_frenet_sl_info_.s << ", l: " << ego_frenet_sl_info_.l;

    const auto& planning_start_point = session_->planning_start_point();
    AINFO << "planning_start_point: "
          << "x: " << planning_start_point.x
          << ", y: " << planning_start_point.y
          << ", v: " << planning_start_point.v
          << ", a: " << planning_start_point.a
          << ", heading: " << planning_start_point.heading
          << ", curvature: " << planning_start_point.curvature;
    DLOG_DICT_BEGIN("planning_start_point");
    DLOG_DICT_ITEM("x", planning_start_point.x);
    DLOG_DICT_ITEM("y", planning_start_point.y);
    DLOG_DICT_ITEM("heading", planning_start_point.heading);
    DLOG_DICT_ITEM("curvature", planning_start_point.curvature);
    DLOG_DICT_ITEM("v", planning_start_point.v);
    DLOG_DICT_ITEM("a", planning_start_point.a);
    DLOG_DICT_END("planning_start_point");

    std::array<double, 3> s_condition;
    std::array<double, 3> l_condition;

    frenet_coord->CartCoord2FrenetCoord(
        planning_start_point.x, planning_start_point.y, planning_start_point.v,
        planning_start_point.a, planning_start_point.heading,
        planning_start_point.curvature, &s_condition, &l_condition);

    // AINFO << "s_condition: " << s_condition[0] << ", " << s_condition[1] <<
    // ", "
    //       << s_condition[2];
    // AINFO << "l_condition: " << l_condition[0] << ", " << l_condition[1] <<
    // ", "
    //       << l_condition[2];
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();

    adc_bound_points_ = PathBoundsDeciderUtil::GetAdcBoundPoints(
        planning_start_point.x, planning_start_point.y,
        planning_start_point.heading, vehicle_config.front_edge_to_center,
        vehicle_config.back_edge_to_center, vehicle_config.width);
    init_sl_state_ = std::make_pair(s_condition, l_condition);
    // 计算boundary
    std::vector<PathBoundary> path_boundaries;
    std::vector<PathData> path_datas;
    PathData path_data;

    FrenetFramePath sl_qp_path;
    bool is_bondary_vaild = false;

    if (candidate_transition_context.target_state ==
        ScenarioStateEnum::CRUISE_CHANGE) {
        is_bondary_vaild = DecideLaneChangePathBounds(&path_boundaries);
    } else {
        is_bondary_vaild = DecideLaneFollowPathBounds(&path_boundaries);
    }

    AINFO << "path_boundaries size: " << path_boundaries.size();
    // if (!is_bondary_vaild) {
    //     AWARN << "path boundary is not valid!";
    //     return false;
    // }

    // 多封一层，方便切换别的算法
    if (1 == path_opti_method_) {
        std::vector<planning::Point2D> piecewise_jerk_path;
        PiecewiseJerkPathOptimizer path_optimizer;
        path_optimizer.set_optimizer_params(piecewise_jerk_path_config_);
        path_optimizer.set_reference_line(reference_line_.get());
        path_optimizer.set_init_sl_state(init_sl_state_);
        if (candidate_transition_context.target_state ==
            ScenarioStateEnum::CRUISE_CHANGE) {
            path_optimizer.set_is_change_lane(true);
        } else {
            path_optimizer.set_is_change_lane(false);
        }
        if (!path_optimizer.Solve(path_boundaries, sl_qp_path)) {
            AERROR << "candidate_transition_context_id: "
                   << candidate_transition_context_id
                   << " path_optimizer failed! ";
            return false;
        }

        path_data = GetCartesianPathFromFrenet(sl_qp_path);

        // 重要：此处存储为了方便绘制单帧的曲率和heading
        for (size_t i = 0; i < path_data.size(); ++i) {
            const auto& point = path_data[i];
            piecewise_jerk_path.emplace_back(point.heading, point.curvature);
        }
        auto& data_manager = session_->data_manager();
        data_manager.store_array("piecewise_jerk_path", piecewise_jerk_path);
    }
    path_datas.emplace_back(path_data);
    path_map_info[candidate_transition_context_id] = path_datas;
    session_->set_path_data_map(path_map_info);
    return true;
}

bool PathOptimizer::DecideLaneFollowPathBounds(
    std::vector<PathBoundary>* boundary) {
    boundary->emplace_back();
    auto& path_bound = boundary->back();
    std::string blocking_obstacle_id = "";
    std::string lane_type = "";
    double path_narrowest_width = 0;

    // AINFO << "reference_line length_to_ego: " <<
    // reference_line_->LengthToEgo();

    // 1. Initialize the path boundaries to be an indefinitely large area.
    if (!PathBoundsDeciderUtil::InitPathBoundary(*reference_line_, &path_bound,
                                                 init_sl_state_)) {
        const std::string msg = "Failed to initialize path boundaries.";
        AERROR << msg;
        return false;
    }

    AINFO << "path_boundary start_s: " << path_bound.start_s()
          << " back s : " << path_bound.back().s
          << " size: " << path_bound.size();

    // 由于是跟随车道行驶，考虑自车的宽度，设置车道边界
    bool is_include_adc = true;
    // 2. Decide a rough boundary based on lane info and ADC's position
    if (!PathBoundsDeciderUtil::GetBoundaryFromSelfLane(
            *reference_line_, ego_frenet_sl_info_, &path_bound)) {
        AERROR << "Failed to decide a rough boundary based on self lane.";
        return false;
    }

    // 开启后就比较依赖原始感知给出的边界信息，一旦不准可能规划出较危险的道路
    if (is_include_adc) {
        PathBoundsDeciderUtil::ExtendBoundaryByADC(
            *reference_line_, init_sl_state_, lane_follow_extern_buffer,
            &path_bound);
    }

    // 3. Fine-tune the boundary based on static obstacles
    PathBound temp_path_bound = path_bound;
    std::vector<SLPolygon> obs_sl_polygons;
    PathBoundsDeciderUtil::GetSLPolygons(*reference_line_, &obs_sl_polygons,
                                         init_sl_state_, adc_bound_points_);

    AINFO << "obs_sl_polygons size: " << obs_sl_polygons.size();

    // // 存储obs，绘制
    // std::vector<Point2D> obs;
    // int i = 0;
    // for (const auto& polygon : obs_sl_polygons) {
    //     obs.clear();
    //     Point2D cartesian_point;
    //     for (const auto& point : polygon.get_SLBoundary().boundary_point) {
    //         reference_line_->frenet_coord()->FrenetCoord2CartCoord(
    //             {point.s, point.l}, cartesian_point);
    //         obs.emplace_back(cartesian_point);
    //     }
    //     obs.emplace_back(polygon.get_SLBoundary().boundary_point.at(0).s,
    //                      polygon.get_SLBoundary().boundary_point.at(0).l);
    //     auto& data_manager = session_->data_manager();
    //     data_manager.store_array("obs_sl_polygon_" + std::to_string(i), obs);
    //     i++;
    // }

    if (!PathBoundsDeciderUtil::GetBoundaryFromStaticObstacles(
            *reference_line_, &obs_sl_polygons, init_sl_state_, &path_bound,
            &blocking_obstacle_id, &path_narrowest_width)) {
        const std::string msg =
            "Failed to decide fine tune the boundaries after "
            "taking into consideration all static obstacles.";
        AERROR << msg;
        return false;
    }

    AINFO << "Completed generating path boundaries.";
    // for (const auto& point : path_bound) {
    //     AINFO << "path_bound s: " << point.s << ", l_lower: " <<
    //     point.l_lower.l
    //           << ", l_upper: " << point.l_upper.l;
    // }

    if (init_sl_state_.second[0] > path_bound[0].l_upper.l ||
        init_sl_state_.second[0] < path_bound[0].l_lower.l) {
        AINFO << "not in self lane maybe lane borrow or out of road. init l : "
              << init_sl_state_.second[0] << ", path_bound l: [ "
              << path_bound[0].l_lower.l << "," << path_bound[0].l_upper.l
              << " ]";
        return false;
    }
    path_bound.set_blocking_obstacle_id(blocking_obstacle_id);

    // 存储bound，绘制
    // std::vector<Point2D> path_left_bound;
    // std::vector<Point2D> path_right_bound;
    // for (const auto& point : path_bound) {
    //     Point2D cartesian_point_upper;
    //     Point2D cartesian_point_lower;
    //     reference_line_->frenet_coord()->FrenetCoord2CartCoord(
    //         {point.s, point.l_upper.l}, cartesian_point_upper);
    //     reference_line_->frenet_coord()->FrenetCoord2CartCoord(
    //         {point.s, point.l_lower.l}, cartesian_point_lower);

    //     path_left_bound.emplace_back(cartesian_point_upper);
    //     path_right_bound.emplace_back(cartesian_point_lower);
    // }

    // auto& data_manager = session_->data_manager();
    // data_manager.store_array("path_left_bound", path_left_bound);
    // data_manager.store_array("path_right_bound", path_right_bound);

    return true;
}

bool PathOptimizer::DecideLaneChangePathBounds(
    std::vector<PathBoundary>* boundary) {
    boundary->emplace_back();
    auto& path_bound = boundary->back();
    std::string blocking_obstacle_id = "";
    std::string lane_type = "";
    double path_narrowest_width = 0;

    AINFO << "DecideLaneChangePathBounds";
    // 1. Initialize the path boundaries to be an indefinitely large area.
    if (!PathBoundsDeciderUtil::InitPathBoundary(*reference_line_, &path_bound,
                                                 init_sl_state_)) {
        const std::string msg = "Failed to initialize path boundaries.";
        AERROR << msg;
        return false;
    }

    AINFO << "path_boundary start_s: " << path_bound.start_s()
          << " back s : " << path_bound.back().s
          << " size: " << path_bound.size();

    // 2. Decide a rough boundary based on lane info and ADC's position
    if (!PathBoundsDeciderUtil::GetBoundaryFromSelfLane(
            *reference_line_, ego_frenet_sl_info_, &path_bound)) {
        AERROR << "Failed to decide a rough boundary based on self lane.";
        return false;
    }
    PathBoundsDeciderUtil::ExtendBoundaryByADC(*reference_line_, init_sl_state_,
                                               lane_change_extern_buffer,
                                               &path_bound);

    // 3. Extend the boundary from target lane
    PathBoundsDeciderUtil::ExtendBoundaryFromTargetLane(
        *reference_line_, ego_frenet_sl_info_, &path_bound, request_type_);

    // // 4. Remove the S-length of target lane out of the path-bound.
    // GetBoundaryFromLaneChangeForbiddenZone(&path_bound);
    // 5. Fine-tune the boundary based on static obstacles
    PathBound temp_path_bound = path_bound;
    std::vector<SLPolygon> obs_sl_polygons;
    PathBoundsDeciderUtil::GetSLPolygons(*reference_line_, &obs_sl_polygons,
                                         init_sl_state_, adc_bound_points_);
    if (!PathBoundsDeciderUtil::GetBoundaryFromStaticObstacles(
            *reference_line_, &obs_sl_polygons, init_sl_state_, &path_bound,
            &blocking_obstacle_id, &path_narrowest_width)) {
        const std::string msg =
            "Failed to decide fine tune the boundaries after "
            "taking into consideration all static obstacles.";
        AERROR << msg;
        return false;
    }

    AINFO << "Completed generating path boundaries.";
    return false;
}

PathData PathOptimizer::GetCartesianPathFromFrenet(
    const FrenetFramePath& frenet_path) {
    PathData path_data;
    // PathData path_data_raw;

    const auto* frenet_coord = reference_line_->frenet_coord();
    if (frenet_coord == nullptr) {
        AERROR << "frenet_coord is nullptr! ";
    }

    for (size_t i = 0; i < frenet_path.size(); ++i) {
        Point2D cartesian_point;
        // 规划出来的s值是基于自车为原点的s,需要加上自车在参考线下的s才是真实的坐标
        frenet_coord->FrenetCoord2CartCoord(
            {frenet_path[i].s + ego_frenet_sl_info_.s, frenet_path[i].l},
            cartesian_point);

        // path_data_raw.emplace_back(
        //     PathPoint{cartesian_point.x, cartesian_point.y, 0.0, 0.0, 0.0});

        const ReferenceLinePoint ref_point =
            reference_line_->GetNearestReferencePoint(frenet_path[i].s +
                                                      ego_frenet_sl_info_.s);
        const double theta = FrenetCoordinateSystem::CalculateTheta(
            ref_point.heading, ref_point.curvature, frenet_path[i].l,
            frenet_path[i].dl);

        const double kappa = FrenetCoordinateSystem::CalculateKappa(
            ref_point.curvature, ref_point.dcurvature, frenet_path[i].l,
            frenet_path[i].dl, frenet_path[i].ddl);

        double s = 0.0;
        double dcurvature = 0.0;
        if (!path_data.empty()) {
            auto last = path_data.back();
            const double distance = std::hypot(last.x - cartesian_point.x,
                                               last.y - cartesian_point.y);
            s = path_data.back().s + distance;
            dcurvature = (kappa - path_data.back().curvature) / distance;
        }
        path_data.push_back({cartesian_point.x, cartesian_point.y, theta, kappa,
                             dcurvature, 0.0, s});
    }

    // DiscretePointsMath::ComputePathProfile(path_data_raw);

    // for (size_t i = 0; i < path_data.size(); ++i) {
    //     const auto& point1 = path_data[i];
    //     const auto& point2 = path_data_raw[i];
    //     // 将原始路径点的曲率和dcurvature赋值给新的路径
    //     AINFO << "PathPoint[" << i << "]: "
    //           << "new_heading: " << point1.heading
    //           << ", raw_heading: " << point2.heading;
    // }
    return path_data;
}

}  // namespace planning
}  // namespace arcsoft