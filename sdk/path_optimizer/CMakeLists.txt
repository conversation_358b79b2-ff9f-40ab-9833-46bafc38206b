cmake_minimum_required(VERSION 3.15)
project(path_optimizer)
set(SRC_LIST
    path_optimizer.cc
    piecewise_jerk_path_problem.cc
    path_boundary.cc
    piecewise_jerk_path_optimizer.cc
    path_bounds_decider_util.cc
    sl_polygon.cc
    util.cc
    constant_jerk_trajectory1d.cc
    piecewise_jerk_trajectory1d.cc)
add_library(path_optimizer OBJECT ${SRC_LIST})

target_compile_definitions(
  path_optimizer
  PRIVATE
    PARAM_CONFIG_PATH="${CMAKE_CURRENT_SOURCE_DIR}/path_optimizer_config.json")
target_link_libraries(
  path_optimizer
  PUBLIC common framework)
