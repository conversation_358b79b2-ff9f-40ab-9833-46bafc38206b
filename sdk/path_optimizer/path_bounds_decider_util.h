/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#pragma once
#include "common/basic_types.h"
#include "common/reference_line.h"
#include "common/vehicle_config_helper.h"
#include "path_boundary.h"
#include "sl_polygon.h"
#include "util.h"

namespace arcsoft {
namespace planning {
// ObstacleEdge contains: (is_start_s, s, l_min, l_max, obstacle_id).
using ObstacleEdge = std::tuple<int, double, double, double, std::string>;
// SLSstate contains: (s ,s' ,s''), (l, l', l'')
using SLState = std::pair<std::array<double, 3>, std::array<double, 3>>;

const double path_bounds_horizon = 100.0;
const double trajectory_time_length = 8.0;
const double lane_follow_extern_buffer = 0.2;
const double lane_change_extern_buffer = 0.5;
const double max_nudge_check_distance_in_lk = 4.0;
const double path_bounds_decider_resolution = 0.5;
const double obstacle_lon_end_buffer_park = 0.6;
const double obstacle_lat_buffer = 0.4;
const double relax_ego_radius_buffer = 10.0;
const double relax_path_s_threshold = 5.0;
const double ego_front_slack_buffer = 0.25;
const double nonstatic_obstacle_nudge_l_buffer = 0.4;
const double static_obstacle_nudge_l_buffer = 0.3;
const double ego_static_safety_distance = 6.0;

const bool enable_corner_constraint = true;
const bool enable_adc_vertex_constraint = true;

const int num_extra_tail_bound_point = 20;

class PathBoundsDeciderUtil {
public:
    /**
     * @brief Starting from ADC's current position, increment until the path
     * length, and set lateral bounds to be infinite at every spot.
     */
    static bool InitPathBoundary(const ReferenceLine& reference_line,
                                 PathBoundary* const path_bound,
                                 SLState init_sl_state);
    static bool GetBoundaryFromSelfLane(const ReferenceLine& reference_line,
                                        const SLPoint& init_sl_state,
                                        PathBoundary* const path_bound);

    static double GetADCLaneWidth(const ReferenceLine& reference_line,
                                  const double adc_frenet_s);

    static bool UpdatePathBoundaryWithBuffer(
        double left_bound, double right_bound, BoundType left_type,
        BoundType right_type, std::string left_id, std::string right_id,
        PathBoundPoint* const bound_point);

    static bool UpdateLeftPathBoundaryWithBuffer(
        double left_bound, BoundType left_type, std::string left_id,
        PathBoundPoint* const bound_point);

    static bool UpdateRightPathBoundaryWithBuffer(
        double right_bound, BoundType right_type, std::string right_id,
        PathBoundPoint* const bound_point);

    static void TrimPathBounds(const int path_blocked_idx,
                               PathBoundary* const path_boundaries);

    static bool ExtendBoundaryByADC(const ReferenceLine& reference_line,
                                    const SLState& init_sl_state,
                                    const double extend_buffer,
                                    PathBoundary* const path_bound);

    static void GetSLPolygons(const ReferenceLine& reference_line,
                              std::vector<SLPolygon>* polygons,
                              const SLState& init_sl_state,
                              const std::vector<Point2D>& adc_bound_points);

    static std::vector<Point2D> GetAdcBoundPoints(
        double x, double y,              // 后轴中心坐标
        double theta,                    // 偏航角（弧度）
        double front_edge_to_rear_axle,  // 后轴中心 → 车头
        double rear_edge_to_rear_axle,   // 后轴中心 → 车尾
        double width                     // 车宽
    );

    static bool IsWithinPathDeciderScopeObstacle(
        const FrenetObstacle& obstacle);

    static bool GetBoundaryFromStaticObstacles(
        const ReferenceLine& reference_line_info,
        std::vector<SLPolygon>* const sl_polygons, const SLState& init_sl_state,
        PathBoundary* const path_boundaries,
        std::string* const blocking_obstacle_id, double* const narrowest_width);

    static bool UpdatePathBoundaryBySLPolygon(
        const ReferenceLine& reference_line_info,
        std::vector<SLPolygon>* const sl_polygon, const SLState& init_sl_state,
        PathBoundary* const path_boundary, std::string* const blocked_id,
        double* const narrowest_width);

    static double GetBufferBetweenADCCenterAndEdge();

    static bool AddExtraPathBound(const std::vector<SLPolygon>& sl_polygons,
                                  PathBoundary* const path_boundary,
                                  const SLState& init_sl_state,
                                  std::string* const blocked_id);

    static bool RelaxEgoPathBoundary(PathBoundary* const path_boundary,
                                     const SLState& init_sl_state);

    static bool RelaxBoundaryPoint(PathBoundPoint* const path_bound_point,
                                   bool is_left, double init_l, double heading,
                                   double delta_s, double init_frenet_kappa,
                                   double min_radius);

    static void AddCornerBounds(const std::vector<SLPolygon>& sl_polygons,
                                PathBoundary* const path_boundary);

    static bool AddCornerPoint(SLPoint sl_pt, const PathBoundary& path_boundary,
                               ObsCornerConstraints* extra_constraints,
                               bool is_left, std::string obs_id,
                               bool is_front_pt);

    static bool RelaxObsCornerBoundary(PathBoundary* const path_boundary,
                                       const SLState& init_sl_state);

    static bool UpdateBlockInfoWithObsCornerBoundary(
        PathBoundary* const path_boundary, std::string* const blocked_id);

    static void AddAdcVertexBounds(PathBoundary* const path_boundary);
    static bool ExtendBoundaryFromTargetLane(
        const ReferenceLine& target_reference_line,
        const SLPoint& init_sl_state, PathBoundary* const path_bound,
        RequestType change_type);
    // static void GetBoundaryFromLaneChangeForbiddenZone(
    //     PathBoundary* const path_bound);
};
}  // namespace planning
}  // namespace arcsoft