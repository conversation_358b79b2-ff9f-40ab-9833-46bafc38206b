/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#pragma once

#include <string>

namespace arcsoft {
namespace planning {

// Base type for various types of 1-dimensional curves

class Curve1d {
public:
    Curve1d() = default;

    virtual ~Curve1d() = default;

    virtual double Evaluate(const std::uint32_t order,
                            const double param) const = 0;

    virtual double ParamLength() const = 0;

    virtual std::string ToString() const = 0;
};

}  // namespace planning
}  // namespace arcsoft
