/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#pragma once

#include <utility>
#include <vector>

#include "common/piecewise_jerk_problem.h"
#include "path_boundary.h"

namespace arcsoft {
namespace planning {

/*
 * @brief:
 * FEM stands for finite element method.
 * This class solve an optimization problem:
 * x
 * |
 * |                       P(s1, x1)  P(s2, x2)
 * |            P(s0, x0)                       ... P(s(k-1), x(k-1))
 * |P(start)
 * |
 * |________________________________________________________ s
 *
 * we suppose s(k+1) - s(k) == s(k) - s(k-1)
 *
 * Given the x, x', x'' at P(start),  The goal is to find x0, x1, ... x(k-1)
 * which makes the line P(start), P0, P(1) ... P(k-1) "smooth".
 */

class PiecewiseJerkPathProblem : public PiecewiseJerkProblem {
public:
    PiecewiseJerkPathProblem(const size_t num_of_knots, const double delta_s,
                             const std::array<double, 3>& x_init);

    virtual ~PiecewiseJerkPathProblem() = default;
    void set_extra_constraints(const ObsCornerConstraints& extra_constraints) {
        extra_constraints_ = extra_constraints;
    }

    void set_vertex_constraints(const ADCVertexConstraints& vertexs) {
        vertex_constraints_ = vertexs;
    }

protected:
    void CalculateKernel(std::vector<c_float>* P_data,
                         std::vector<c_int>* P_indices,
                         std::vector<c_int>* P_indptr) override;

    void CalculateOffset(std::vector<c_float>* q) override;

    void CalculateAffineConstraint(std::vector<c_float>* A_data,
                                   std::vector<c_int>* A_indices,
                                   std::vector<c_int>* A_indptr,
                                   std::vector<c_float>* lower_bounds,
                                   std::vector<c_float>* upper_bounds) override;

private:
    ObsCornerConstraints extra_constraints_;
    ADCVertexConstraints vertex_constraints_;
};

}  // namespace planning
}  // namespace arcsoft