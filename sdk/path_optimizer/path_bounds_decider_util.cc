#include "path_bounds_decider_util.h"

#include "common/log.h"

namespace arcsoft {
namespace planning {

// 此函数作用初始化道路边界，设置为-∞~+∞，并记录每个采样点/路径点的reference_line_towing_l
// bounds的起始位置s是0，即从自车位置开始出发，自车位置s处为0
bool PathBoundsDeciderUtil::InitPathBoundary(
    const ReferenceLine& reference_line, PathBoundary* const path_bound,
    SLState init_sl_state) {
    path_bound->clear();
    path_bound->set_delta_s(path_bounds_decider_resolution);
    // apollo=====================================================================
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();

    double index = 0;
    AINFO << " reference_line.LengthToEgo(): " << reference_line.LengthToEgo();
    for (double curr_s = 0.0; curr_s <= reference_line.LengthToEgo();
         curr_s += path_bounds_decider_resolution) {
        path_bound->emplace_back(curr_s, std::numeric_limits<double>::lowest(),
                                 std::numeric_limits<double>::max());

        path_bound->back().towing_l = 0.0;
        index++;
    }
    // apollo=====================================================================

    if (path_bound->empty()) {
        AINFO << "Empty path boundary in InitPathBoundary";
        return false;
    }
    return true;
}

// 根据车辆当前s匹配当前位置处的车道宽度,改为从s自己匹配，这样不管车辆s是几都通用
double PathBoundsDeciderUtil::GetADCLaneWidth(
    const ReferenceLine& reference_line, const double adc_s) {
    double lane_left_width = 0.0;
    double lane_right_width = 0.0;

    reference_line.GetLaneWidth(adc_s, lane_left_width, lane_right_width);
    return (lane_left_width + lane_right_width);
}
bool PathBoundsDeciderUtil::ExtendBoundaryFromTargetLane(
    const ReferenceLine& target_reference_line, const SLPoint& init_sl_state,
    PathBoundary* const path_bound, RequestType change_type) {
    // 根据车辆当前s匹配当前位置处的车道宽度
    double target_lane_width =
        GetADCLaneWidth(target_reference_line, init_sl_state.s);

    double past_target_lane_left_width = target_lane_width / 2.0;
    double past_target_lane_right_width = target_lane_width / 2.0;
    int path_blocked_idx = -1;
    for (size_t i = 0; i < path_bound->size(); ++i) {
        double curr_s = (*path_bound)[i].s;
        // 1. Get the target lane width at current point.
        double target_lane_left_width = 0.0;
        double target_lane_right_width = 0.0;

        const double evaluate_s =
            curr_s + target_reference_line.frenet_coord()->ego_frenet_sl().s;
        target_reference_line.GetLaneWidth(evaluate_s, target_lane_left_width,
                                           target_lane_right_width);

        // AINFO << "path bound " << i << " s: " << curr_s
        //       << " curr_lane_left_width: " << curr_lane_left_width
        //       << " curr_lane_right_width: " << curr_lane_right_width;
        if (target_lane_left_width < 0.0 || target_lane_right_width < 0.0) {
            AWARN << "current lane width is invalid! left: "
                  << target_lane_left_width
                  << " right: " << target_lane_right_width;
            target_lane_left_width = past_target_lane_left_width;
            target_lane_right_width = past_target_lane_right_width;
        }
        past_target_lane_left_width = target_lane_left_width;
        past_target_lane_right_width = target_lane_right_width;

        double curr_left_bound = 0;
        double curr_right_bound = 0;

        if (change_type == RequestType::LEFT_CHANGE) {
            curr_left_bound = path_bound->at(i).l_upper.l + target_lane_width;
            curr_right_bound = path_bound->at(i).l_lower.l;
        } else if (change_type == RequestType::RIGHT_CHANGE) {
            curr_left_bound = path_bound->at(i).l_upper.l;
            curr_right_bound = path_bound->at(i).l_lower.l - target_lane_width;
        } else {
            curr_left_bound = path_bound->at(i).l_upper.l;
            curr_right_bound = path_bound->at(i).l_lower.l;
        }

        // AINFO << "path bound " << i << " s: " << curr_s
        //       << " curr_left_bound: " << curr_left_bound
        //       << " curr_right_bound: " << curr_right_bound;

        // 2. Update the boundary.
        if (!UpdatePathBoundaryWithBuffer(curr_left_bound, curr_right_bound,
                                          BoundType::LANE, BoundType::LANE, "",
                                          "", &path_bound->at(i))) {
            path_blocked_idx = static_cast<int>(i);
        }
        if (path_blocked_idx != -1) {
            AINFO << "Path is blocked at index: " << path_blocked_idx
                  << " s: " << curr_s;
            break;
        }
    }
    PathBoundsDeciderUtil::TrimPathBounds(path_blocked_idx, path_bound);
    return true;
}
bool PathBoundsDeciderUtil::GetBoundaryFromSelfLane(
    const ReferenceLine& reference_line, const SLPoint& init_sl_state,
    PathBoundary* const path_bound) {
    // CHECK_NOTNULL(path_bound);
    double adc_lane_width = GetADCLaneWidth(reference_line, init_sl_state.s);
    // Go through every point, update the boundary based on lane info and
    // ADC's position.
    double past_lane_left_width = adc_lane_width / 2.0;
    double past_lane_right_width = adc_lane_width / 2.0;
    int path_blocked_idx = -1;
    for (size_t i = 0; i < path_bound->size(); ++i) {
        double curr_s = (*path_bound)[i].s;
        // 1. Get the current lane width at current point.
        double curr_lane_left_width = 0.0;
        double curr_lane_right_width = 0.0;

        // 新增方法：reference_line下，get_lane_width
        // 输入：find_s
        //
        const double evaluate_s =
            curr_s + reference_line.frenet_coord()->ego_frenet_sl().s;
        reference_line.GetLaneWidth(evaluate_s, curr_lane_left_width,
                                    curr_lane_right_width);

        // AINFO << "path bound " << i << " s: " << curr_s
        //       << " curr_lane_left_width: " << curr_lane_left_width
        //       << " curr_lane_right_width: " << curr_lane_right_width;
        if (curr_lane_left_width < 0.0 || curr_lane_right_width < 0.0) {
            AWARN << "current lane width is invalid! left: "
                  << curr_lane_left_width
                  << " right: " << curr_lane_right_width;
            curr_lane_left_width = past_lane_left_width;
            curr_lane_right_width = past_lane_right_width;
        }
        past_lane_left_width = curr_lane_left_width;
        past_lane_right_width = curr_lane_right_width;

        double curr_left_bound = curr_lane_left_width;
        double curr_right_bound = -curr_lane_right_width;
        // 2. Update the boundary.
        if (!UpdatePathBoundaryWithBuffer(curr_left_bound, curr_right_bound,
                                          BoundType::LANE, BoundType::LANE, "",
                                          "", &path_bound->at(i))) {
            path_blocked_idx = static_cast<int>(i);
        }
        if (path_blocked_idx != -1) {
            AINFO << "Path is blocked at index: " << path_blocked_idx
                  << " s: " << curr_s;
            break;
        }
    }
    PathBoundsDeciderUtil::TrimPathBounds(path_blocked_idx, path_bound);
    return true;
}

void PathBoundsDeciderUtil::TrimPathBounds(
    const int path_blocked_idx, PathBoundary* const path_boundaries) {
    if (path_blocked_idx != -1) {
        if (path_blocked_idx == 0) {
            AWARN << "Completely blocked. Cannot move at all.";
        }
        const auto& vehicle_config =
            common::VehicleConfigHelper::Instance().GetVehicleParam();
        // 这个决定了剩余停车的距离
        double front_edge_to_center =
            vehicle_config.front_edge_to_center + ego_static_safety_distance;
        // 裁剪后，要保证车辆停止时，车头不碰撞前方交叉位置，因为规划原点为车辆后轴中心，不包含静态安全距离和车头到中心的距离
        double trimmed_s =
            path_boundaries->at(path_blocked_idx).s - front_edge_to_center;
        AINFO << "Trimmed from " << path_boundaries->back().s << " to "
              << path_boundaries->at(path_blocked_idx).s - front_edge_to_center;
        while (path_boundaries->size() > 1 &&
               path_boundaries->back().s > trimmed_s) {
            path_boundaries->pop_back();
        }
    }
}

bool PathBoundsDeciderUtil::UpdateLeftPathBoundaryWithBuffer(
    double left_bound, BoundType left_type, std::string left_id,
    PathBoundPoint* const bound_point) {
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();
    double adc_half_width = vehicle_config.width / 2.0;
    left_bound = left_bound - adc_half_width;
    PathBoundPoint new_point = *bound_point;

    if (new_point.l_upper.l > left_bound) {
        new_point.l_upper.l = left_bound;
        new_point.l_upper.type = left_type;
        new_point.l_upper.id = left_id;
    }
    // Check if ADC is blocked.
    // If blocked, don't update anything, return false.
    if (new_point.l_lower.l > new_point.l_upper.l) {
        AWARN << "Path is blocked at " << new_point.l_lower.l << " "
              << new_point.l_upper.l;
        return false;
    }
    // Otherwise, update path_boundaries and center_line; then return true.
    *bound_point = new_point;
    return true;
}

bool PathBoundsDeciderUtil::UpdateRightPathBoundaryWithBuffer(
    double right_bound, BoundType right_type, std::string right_id,
    PathBoundPoint* const bound_point)

{
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();
    double adc_half_width = vehicle_config.width / 2.0;
    right_bound = right_bound + adc_half_width;
    PathBoundPoint new_point = *bound_point;
    if (new_point.l_lower.l < right_bound) {
        new_point.l_lower.l = right_bound;
        new_point.l_lower.type = right_type;
        new_point.l_lower.id = right_id;
    }
    // Check if ADC is blocked.
    // If blocked, don't update anything, return false.
    if (new_point.l_lower.l > new_point.l_upper.l) {
        AWARN << "Path is blocked at" << new_point.l_lower.l << " "
              << new_point.l_upper.l;
        return false;
    }
    // Otherwise, update path_boundaries and center_line; then return true.
    *bound_point = new_point;
    return true;
}

bool PathBoundsDeciderUtil::UpdatePathBoundaryWithBuffer(
    double left_bound, double right_bound, BoundType left_type,
    BoundType right_type, std::string left_id, std::string right_id,
    PathBoundPoint* const bound_point) {
    if (!UpdateLeftPathBoundaryWithBuffer(left_bound, left_type, left_id,
                                          bound_point)) {
        return false;
    }
    if (!UpdateRightPathBoundaryWithBuffer(right_bound, right_type, right_id,
                                           bound_point)) {
        return false;
    }
    return true;
}

bool PathBoundsDeciderUtil::ExtendBoundaryByADC(
    const ReferenceLine& reference_line, const SLState& init_sl_state,
    const double extend_buffer, PathBoundary* const path_bound) {
    // CHECK_NOTNULL(path_bound);
    double adc_l_to_lane_center = init_sl_state.second[0];
    static constexpr double kMaxLateralAccelerations = 1.5;

    double ADC_speed_buffer =
        (init_sl_state.second[1] > 0 ? 1.0 : -1.0) * init_sl_state.second[1] *
        init_sl_state.second[1] / kMaxLateralAccelerations / 2.0;

    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();
    double adc_half_width = vehicle_config.width / 2.0;
    // 车辆最可能的左侧 extend_buffer：预留安全距离
    double left_bound_adc = std::fmax(adc_l_to_lane_center,
                                      adc_l_to_lane_center + ADC_speed_buffer) +
                            adc_half_width + extend_buffer;
    // 车辆最可能的右侧
    double right_bound_adc =
        std::fmin(adc_l_to_lane_center,
                  adc_l_to_lane_center + ADC_speed_buffer) -
        adc_half_width - extend_buffer;
    static constexpr double kEpsilon = 0.05;
    for (size_t i = 0; i < path_bound->size(); ++i) {
        double curr_s = (*path_bound)[i].s;
        // 1. Get the current lane width at current point.
        double road_left_width = 0.0;
        double road_right_width = 0.0;
        const double evaluate_s =
            curr_s + reference_line.frenet_coord()->ego_frenet_sl().s;
        reference_line.GetRoadWidth(evaluate_s, road_left_width,
                                    road_right_width);

        if (road_left_width < 0.0 || road_right_width < 0.0) {
            AWARN << "current lane width is invalid! left: " << road_left_width
                  << " right: " << road_right_width;
            road_left_width = std::fabs(left_bound_adc) + kEpsilon;
            road_right_width = std::fabs(right_bound_adc) + kEpsilon;
        }

        double left_bound_road = road_left_width - adc_half_width;
        double right_bound_road = -road_right_width + adc_half_width;

        // 如果当前车可能的左侧比道路bound更靠左，则取较小值，避免超出道路边界，同时不做收缩
        // 保守扩展，不做收缩
        if (left_bound_adc > (*path_bound)[i].l_upper.l) {
            (*path_bound)[i].l_upper.l =
                std::max(std::min(left_bound_adc, left_bound_road),
                         (*path_bound)[i].l_upper.l);
            (*path_bound)[i].l_upper.type = BoundType::ADC;
            (*path_bound)[i].l_upper.id = "adc";
        }

        // 如果当前车可能的右侧比道路bound更靠右
        if (right_bound_adc < (*path_bound)[i].l_lower.l) {
            (*path_bound)[i].l_lower.l =
                std::min(std::max(right_bound_adc, right_bound_road),
                         (*path_bound)[i].l_lower.l);
            (*path_bound)[i].l_lower.type = BoundType::ADC;
            (*path_bound)[i].l_lower.id = "adc";
        }
    }
    return true;
}

void PathBoundsDeciderUtil::GetSLPolygons(
    const ReferenceLine& reference_line, std::vector<SLPolygon>* polygons,
    const SLState& init_sl_state,
    const std::vector<Point2D>& adc_bound_points) {
    polygons->clear();

    // std::vector<FrenetObstacle> obstacles;
    // FrenetObstacle obstacle;
    // obstacle.id = 0;
    // obstacle.is_static = true;
    // obstacle.s = 50.0;
    // obstacle.l = 0.0;
    // obstacle.sl_boundary.start_s = 48.0;
    // obstacle.sl_boundary.end_s = 52.0;
    // obstacle.sl_boundary.start_l = -3.0;
    // obstacle.sl_boundary.end_l = 1.0;
    // obstacle.type = PerceptionObstacleType::VEHICLE;
    // obstacle.sl_boundary.boundary_point.push_back(
    //     Point2D(obstacle.sl_boundary.start_s, obstacle.sl_boundary.start_l));
    // obstacle.sl_boundary.boundary_point.push_back(
    //     Point2D(obstacle.sl_boundary.end_s, obstacle.sl_boundary.start_l));
    // obstacle.sl_boundary.boundary_point.push_back(
    //     Point2D(obstacle.sl_boundary.end_s, obstacle.sl_boundary.end_l));
    // obstacle.sl_boundary.boundary_point.push_back(
    //     Point2D(obstacle.sl_boundary.start_s, obstacle.sl_boundary.end_l));

    // obstacles.push_back(obstacle);
    // obstacle.id = 1;
    // obstacle.is_static = true;
    // obstacle.s = 70.0;
    // obstacle.l = 0.0;
    // obstacle.sl_boundary.start_s = 68.0;
    // obstacle.sl_boundary.end_s = 72.0;
    // obstacle.sl_boundary.start_l = 0.0;
    // obstacle.sl_boundary.end_l = 4.0;
    // obstacle.type = PerceptionObstacleType::VEHICLE;
    // obstacle.sl_boundary.boundary_point.clear();
    // obstacle.sl_boundary.boundary_point.push_back(
    //     Point2D(obstacle.sl_boundary.start_s, obstacle.sl_boundary.start_l));
    // obstacle.sl_boundary.boundary_point.push_back(
    //     Point2D(obstacle.sl_boundary.end_s, obstacle.sl_boundary.start_l));
    // obstacle.sl_boundary.boundary_point.push_back(
    //     Point2D(obstacle.sl_boundary.end_s, obstacle.sl_boundary.end_l));
    // obstacle.sl_boundary.boundary_point.push_back(
    //     Point2D(obstacle.sl_boundary.start_s, obstacle.sl_boundary.end_l));

    // obstacles.push_back(obstacle);

    auto obstacles = reference_line.frenet_obstacles();

    const double adc_back_edge_s = reference_line.adc_sl_boundary().start_s;
    for (const auto obstacle : obstacles) {
        // AINFO << "GetSLPolygons1, obstacle id: " << obstacle.id
        //       << ", obstacle.s: " << obstacle.s
        //       << ", obstacle.l: " << obstacle.l
        //       << ", obstacle.start_s: " << obstacle.sl_boundary.start_s
        //       << ", obstacle.start_l: " << obstacle.sl_boundary.start_l
        //       << ", obstacle.end_s: " << obstacle.sl_boundary.end_s
        //       << ", obstacle.end_l: " << obstacle.sl_boundary.end_l
        //       << ", obstacle.type: " << static_cast<int>(obstacle.type)
        //       << ", obstacle.is_static: " << obstacle.is_static;

        // 只考虑静态障碍物，动态障碍物是ST规划需要考虑的
        if (!IsWithinPathDeciderScopeObstacle(obstacle)) {
            continue;
        }

        // AINFO << " obstacle.sl_boundary.end_s: " <<
        // obstacle.sl_boundary.end_s
        //       << " adc_back_edge_s: " << adc_back_edge_s;
        // 只考虑在adc前方的障碍物
        if (obstacle.sl_boundary.end_s < 0) {
            AINFO << "Obstacle is behind ADC, skipping.";
            continue;
        }
        // AINFO << "GetSLPolygons2, obstacle id: " << obstacle.id
        //       << ", obstacle.s: " << obstacle.s
        //       << ", obstacle.l: " << obstacle.l
        //       << ", obstacle.start_s: " << obstacle.sl_boundary.start_s
        //       << ", obstacle.start_l: " << obstacle.sl_boundary.start_l
        //       << ", obstacle.end_s: " << obstacle.sl_boundary.end_s
        //       << ", obstacle.end_l: " << obstacle.sl_boundary.end_l
        //       << ", obstacle.type: " << static_cast<int>(obstacle.type)
        //       << ", obstacle.is_static: " << obstacle.is_static;
        const auto& obstacle_sl = obstacle.sl_boundary;

        polygons->emplace_back(
            SLPolygon(obstacle_sl, std::to_string(obstacle.id), obstacle.type));
    }
    sort(polygons->begin(), polygons->end(),
         [](const SLPolygon& a, const SLPolygon& b) {
             return a.MinS() < b.MinS();
         });
    return;
}

// 返回：左前、右前、左后、右后角点的全局坐标
std::vector<Point2D> PathBoundsDeciderUtil::GetAdcBoundPoints(
    double x, double y,              // 后轴中心坐标
    double theta,                    // 偏航角（弧度）
    double front_edge_to_rear_axle,  // 后轴中心 → 车头
    double rear_edge_to_rear_axle,   // 后轴中心 → 车尾
    double width                     // 车宽
) {
    double half_w = width / 2.0;

    // 车辆坐标系中定义四个角点位置
    std::vector<std::pair<double, double>> local_corners = {
        {front_edge_to_rear_axle, half_w},   // 左前
        {front_edge_to_rear_axle, -half_w},  // 右前
        {-rear_edge_to_rear_axle, half_w},   // 左后
        {-rear_edge_to_rear_axle, -half_w}   // 右后
    };

    // 旋转 + 平移到全局坐标系
    std::vector<Point2D> global_corners;
    double cos_theta = std::cos(theta);
    double sin_theta = std::sin(theta);

    for (const auto& [dx, dy] : local_corners) {
        double x_corner = x + dx * cos_theta - dy * sin_theta;
        double y_corner = y + dx * sin_theta + dy * cos_theta;
        global_corners.emplace_back(x_corner, y_corner);
    }

    return global_corners;
}

bool PathBoundsDeciderUtil::IsWithinPathDeciderScopeObstacle(
    const FrenetObstacle& obstacle) {
    if (!obstacle.is_static) {
        return false;
    }
    AINFO << "IsWithinPathDeciderScopeObstacle, obstacle id: " << obstacle.id
          << ", obstacle.is_static: " << obstacle.is_static;
    return true;
}

bool PathBoundsDeciderUtil::GetBoundaryFromStaticObstacles(
    const ReferenceLine& reference_line_info,
    std::vector<SLPolygon>* const sl_polygons, const SLState& init_sl_state,
    PathBoundary* const path_boundary, std::string* const blocking_obstacle_id,
    double* const narrowest_width) {
    UpdatePathBoundaryBySLPolygon(reference_line_info, sl_polygons,
                                  init_sl_state, path_boundary,
                                  blocking_obstacle_id, narrowest_width);
    // AddExtraPathBound(*sl_polygons, path_boundary, init_sl_state,
    //                   blocking_obstacle_id);
    return true;
}

bool PathBoundsDeciderUtil::UpdatePathBoundaryBySLPolygon(
    const ReferenceLine& reference_line_info,
    std::vector<SLPolygon>* const sl_polygon, const SLState& init_sl_state,
    PathBoundary* const path_boundary, std::string* const blocked_id,
    double* const narrowest_width) {
    AINFO << "UpdatePathBoundaryBySLPolygon, sl_polygon size: "
          << sl_polygon->size();
    // CHECK_NOTNULL(path_boundary);
    std::vector<double> center_l;
    double max_nudge_check_distance;
    // apollo
    //   if (reference_line_info.IsChangeLanePath() ||
    //       path_boundary->label().find("regular/left") != std::string::npos ||
    //       path_boundary->label().find("regular/right") != std::string::npos)
    //       {
    //     center_l.push_back(
    //         (path_boundary->front().l_upper.l +
    //         path_boundary->front().l_lower.l) * 0.5);
    //     max_nudge_check_distance = FLAGS_max_nudge_check_distance_in_lk;
    //   } else {
    //     center_l.push_back(0.0);
    //     max_nudge_check_distance = FLAGS_max_nudge_check_distance_in_lc;
    //   }
    // apollo

    center_l.push_back(
        (path_boundary->front().l_upper.l + path_boundary->front().l_lower.l) *
        0.5);
    max_nudge_check_distance = max_nudge_check_distance_in_lk;

    *narrowest_width =
        path_boundary->front().l_upper.l - path_boundary->front().l_lower.l;
    double mid_l =
        (path_boundary->front().l_upper.l + path_boundary->front().l_lower.l) /
        2;
    size_t nudge_check_count =
        size_t(max_nudge_check_distance / path_bounds_decider_resolution);
    double last_max_nudge_l = center_l.front();
    // bool obs_overlap_with_refer_center = false;

    for (size_t i = 1; i < path_boundary->size(); ++i) {
        double path_boundary_s = path_boundary->at(i).s;

        // auto& 改为 auto 不再根据静态障碍物更新边界
        auto left_bound = path_boundary->at(i).l_upper;
        auto right_bound = path_boundary->at(i).l_lower;
        double default_width = right_bound.l - left_bound.l;

        // 找到最近4米内绝对值最大的center_l
        auto begin_it =
            center_l.end() - std::min(nudge_check_count, center_l.size());
        last_max_nudge_l = *std::max_element(
            begin_it, center_l.end(),
            [](double a, double b) { return std::fabs(a) < std::fabs(b); });
        // AINFO << "last max nudge l: " << last_max_nudge_l;
        for (size_t j = 0; j < sl_polygon->size(); j++) {
            // AINFO << "sl_polygon->at(" << j
            //       << ").id(): " << sl_polygon->at(j).id()
            //       << ", MinS: " << sl_polygon->at(j).MinS()
            //       << ", MaxS: " << sl_polygon->at(j).MaxS()
            //       << ", MinL: " << sl_polygon->at(j).MinL()
            //       << ", MaxL: " << sl_polygon->at(j).MaxL()
            //       << ", NudgeInfo: " << sl_polygon->at(j).NudgeInfo()
            //       << ", is_passable: "
            //       << sl_polygon->at(j).is_passable()[LEFT_INDEX] << ", "
            //       << sl_polygon->at(j).is_passable()[RIGHT_INDEX]
            //       << ", left_boundary: "
            //       << sl_polygon->at(j).GetLeftBoundaryByS(path_boundary_s)
            //       << ", right_boundary: "
            //       << sl_polygon->at(j).GetRightBoundaryByS(path_boundary_s)
            //       << ", sl_boundary: "
            //       << sl_polygon->at(j).get_SLBoundary().start_s << ", "
            //       << sl_polygon->at(j).get_SLBoundary().end_s << ", "
            //       << sl_polygon->at(j).get_SLBoundary().start_l << ", "
            //       << sl_polygon->at(j).get_SLBoundary().end_l;

            if (sl_polygon->at(j).NudgeInfo() == SLPolygon::IGNORE) {
                AINFO << "UpdatePathBoundaryBySLPolygon, ignore obs: "
                      << sl_polygon->at(j).id();
                continue;
            }
            double min_s = sl_polygon->at(j).MinS();
            double max_s =
                sl_polygon->at(j).MaxS() + obstacle_lon_end_buffer_park;
            // 障碍物太小，为了不被忽视，手动扩大一些
            if (max_s - min_s < path_bounds_decider_resolution) {
                max_s += path_bounds_decider_resolution;
                min_s -= path_bounds_decider_resolution;
            }
            // 障碍物在当前后方
            if (max_s < path_boundary_s) {
                continue;
            }
            // 障碍物在当前前方,且sl_plygon已经排序了，说明剩下的都在前方，不影响当前点bound计算
            if (min_s > path_boundary_s) {
                break;
            }
            double adc_obs_edge_buffer = GetBufferBetweenADCCenterAndEdge();
            sl_polygon->at(j).UpdatePassableInfo(left_bound.l, right_bound.l,
                                                 adc_obs_edge_buffer);

            // 计算障碍物的左边界和右边界
            double l_lower =
                sl_polygon->at(j).GetRightBoundaryByS(path_boundary_s);
            double l_upper =
                sl_polygon->at(j).GetLeftBoundaryByS(path_boundary_s);
            PathBoundPoint obs_left_nudge_bound(
                path_boundary_s, l_upper + adc_obs_edge_buffer, left_bound.l);
            obs_left_nudge_bound.towing_l = path_boundary->at(i).towing_l;
            PathBoundPoint obs_right_nudge_bound(path_boundary_s, right_bound.l,
                                                 l_lower - adc_obs_edge_buffer);
            obs_right_nudge_bound.towing_l = path_boundary->at(i).towing_l;
            // obs_overlap_with_refer_center =
            //     left_bound.l < path_boundary->at(i).towing_l ||
            //     right_bound.l > path_boundary->at(i).towing_l;

            if (sl_polygon->at(j).NudgeInfo() == SLPolygon::UNDEFINED) {
                AINFO << "last_max_nudge_l: " << last_max_nudge_l
                      << ", obs id: " << sl_polygon->at(j).id()
                      << ", obs l: " << l_lower << ", " << l_upper;
                double obs_l = (l_lower + l_upper) / 2;
                if (sl_polygon->at(j).is_passable()[RIGHT_INDEX]) {
                    if (sl_polygon->at(j).is_passable()[LEFT_INDEX]) {
                        if (std::fabs(obs_l - mid_l) < 0.4 &&
                            std::fabs(path_boundary_s) <
                                5.0) {  // 距离障碍物太近，直接根据自车状态判断
                            if (init_sl_state.second[0] < obs_l) {
                                sl_polygon->at(j).SetNudgeInfo(
                                    SLPolygon::RIGHT_NUDGE);
                                AINFO << sl_polygon->at(j).id()
                                      << " right nudge with init_sl_state";
                            } else {
                                sl_polygon->at(j).SetNudgeInfo(
                                    SLPolygon::LEFT_NUDGE);
                                AINFO << sl_polygon->at(j).id()
                                      << " left nudge width init_sl_state";
                            }
                        } else {  // 障碍物较远，根据历史最大l判断
                            if (last_max_nudge_l < obs_l) {
                                sl_polygon->at(j).SetNudgeInfo(
                                    SLPolygon::RIGHT_NUDGE);
                                AINFO << sl_polygon->at(j).id()
                                      << " right nudge, according max_nudge_l";
                            } else {
                                sl_polygon->at(j).SetNudgeInfo(
                                    SLPolygon::LEFT_NUDGE);
                                AINFO << sl_polygon->at(j).id()
                                      << " left nudge, according max_nudge_l";
                            }
                        }
                    } else {
                        sl_polygon->at(j).SetNudgeInfo(SLPolygon::RIGHT_NUDGE);
                        AINFO << sl_polygon->at(j).id()
                              << " right nudge, left is not passable";
                    }
                } else {
                    sl_polygon->at(j).SetNudgeInfo(SLPolygon::LEFT_NUDGE);
                    AINFO << sl_polygon->at(j).id()
                          << " left nudge, right is not passable";
                }
            } else {
                // AINFO << "last_max_nudge_l: " << last_max_nudge_l
                //       << ", obs id: " << sl_polygon->at(j).id()
                //       << ", obs l: " << l_lower << ", " << l_upper
                //       << ", nudge info: " << sl_polygon->at(j).NudgeInfo();
            }
            // 往右侧避让
            if (sl_polygon->at(j).NudgeInfo() == SLPolygon::RIGHT_NUDGE) {
                // right nudge
                if (obs_right_nudge_bound.l_upper.l <
                    path_boundary->at(i).towing_l) {
                    sl_polygon->at(j).SetOverlapeWithReferCenter(true);
                    sl_polygon->at(j).SetOverlapeSizeWithReference(
                        path_boundary->at(i).towing_l -
                        obs_right_nudge_bound.l_upper.l);
                }
                if (!sl_polygon->at(j).is_passable()[RIGHT_INDEX]) {
                    // boundary is blocked
                    *blocked_id = sl_polygon->at(j).id();
                    AINFO << "blocked at " << *blocked_id
                          << ", s: " << path_boundary_s
                          << ", left bound: " << left_bound.l
                          << ", right bound: " << right_bound.l;
                    sl_polygon->at(j).SetNudgeInfo(SLPolygon::BLOCKED);
                    break;
                }
                if (obs_right_nudge_bound.l_upper.l < left_bound.l) {
                    AINFO << "update left_bound: s " << path_boundary_s
                          << ", l " << left_bound.l << " -> "
                          << obs_right_nudge_bound.l_upper.l;
                    left_bound.l = obs_right_nudge_bound.l_upper.l;
                    left_bound.type = BoundType::OBSTACLE;
                    left_bound.id = sl_polygon->at(j).id();
                    *narrowest_width = std::min(*narrowest_width,
                                                left_bound.l - right_bound.l);
                }
            } else if (sl_polygon->at(j).NudgeInfo() == SLPolygon::LEFT_NUDGE) {
                // left nudge
                if (obs_left_nudge_bound.l_lower.l >
                    path_boundary->at(i).towing_l) {
                    sl_polygon->at(j).SetOverlapeWithReferCenter(true);
                    sl_polygon->at(j).SetOverlapeSizeWithReference(
                        obs_left_nudge_bound.l_lower.l -
                        path_boundary->at(i).towing_l);
                }
                if (!sl_polygon->at(j).is_passable()[LEFT_INDEX]) {
                    // boundary is blocked
                    *blocked_id = sl_polygon->at(j).id();
                    AINFO << "blocked at " << *blocked_id
                          << ", s: " << path_boundary_s
                          << ", left bound: " << left_bound.l
                          << ", right bound: " << right_bound.l;
                    sl_polygon->at(j).SetNudgeInfo(SLPolygon::BLOCKED);
                    break;
                }
                if (obs_left_nudge_bound.l_lower.l > right_bound.l) {
                    AINFO << "update right_bound: s " << path_boundary_s
                          << ", l " << right_bound.l << " -> "
                          << obs_left_nudge_bound.l_lower.l;
                    right_bound.l = obs_left_nudge_bound.l_lower.l;
                    right_bound.type = BoundType::OBSTACLE;
                    right_bound.id = sl_polygon->at(j).id();
                    *narrowest_width = std::min(*narrowest_width,
                                                left_bound.l - right_bound.l);
                }
            }
            // obs_overlap_with_refer_center =
            //     left_bound.l < path_boundary->at(i).towing_l ||
            //     right_bound.l > path_boundary->at(i).towing_l;

            // double current_center_l = obs_overlap_with_refer_center
            //                               ? (left_bound.l + right_bound.l)
            //                               / 2.0 :
            //                               path_boundary->at(i).towing_l;
            // last_max_nudge_l = std::fabs(current_center_l - mid_l) >
            //                            std::fabs(last_max_nudge_l - mid_l)
            //                        ? current_center_l
            //                        : last_max_nudge_l;
            last_max_nudge_l =
                std::fabs((left_bound.l + right_bound.l) / 2.0 - mid_l) >
                        std::fabs(last_max_nudge_l - mid_l)
                    ? (left_bound.l + right_bound.l) / 2.0
                    : last_max_nudge_l;
        }
        // if blocked, trim path
        if (!blocked_id->empty()) {
            TrimPathBounds(static_cast<int>(i), path_boundary);
            *narrowest_width = default_width;
            AINFO << "Path is blocked by obstacle: " << *blocked_id
                  << ", at s: " << path_boundary_s
                  << ", left bound: " << left_bound.l
                  << ", right bound: " << right_bound.l;
            AINFO << "narrowest width: " << *narrowest_width;
            return false;
        }
        // double current_center_l = obs_overlap_with_refer_center
        //                               ? (left_bound.l + right_bound.l) / 2.0
        //                               : path_boundary->at(i).towing_l;
        // center_l.push_back(current_center_l);
        center_l.push_back((left_bound.l + right_bound.l) / 2.0);
        // AINFO << "update s: " << path_boundary_s
        //       << ", center_l: " << center_l.back();
    }
    return true;
}

double PathBoundsDeciderUtil::GetBufferBetweenADCCenterAndEdge() {
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();
    double adc_half_width = vehicle_config.width / 2.0;

    return (adc_half_width + obstacle_lat_buffer);
}

bool PathBoundsDeciderUtil::AddExtraPathBound(
    const std::vector<SLPolygon>& sl_polygons,
    PathBoundary* const path_boundary, const SLState& init_sl_state,
    std::string* const blocked_id)

{
    RelaxEgoPathBoundary(path_boundary, init_sl_state);
    if (enable_corner_constraint) {
        AddCornerBounds(sl_polygons, path_boundary);
        RelaxObsCornerBoundary(path_boundary, init_sl_state);
        UpdateBlockInfoWithObsCornerBoundary(path_boundary, blocked_id);
    }
    if (enable_adc_vertex_constraint) {
        AddAdcVertexBounds(path_boundary);
    }
    return true;
}

void PathBoundsDeciderUtil::AddAdcVertexBounds(
    PathBoundary* const path_boundary) {
    auto* adc_vertex_bound = path_boundary->mutable_adc_vertex_bound();
    // front_edge_to_center in Apollo is the front edge to rear center
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();

    double front_edge_to_center = vehicle_config.front_edge_to_center;
    for (size_t i = 0; i < path_boundary->size(); i++) {
        double rear_axle_s = path_boundary->at(i).s - front_edge_to_center;
        if (rear_axle_s <= path_boundary->start_s()) {
            continue;
        }
        size_t left_index = 0;
        size_t right_index = 0;
        double left_weight = 0.0;
        double right_weight = 0.0;
        if (!path_boundary->get_interpolated_s_weight(
                rear_axle_s, &left_weight, &right_weight, &left_index,
                &right_index)) {
            AERROR << "Fail to find vertex path bound point in path boundary: "
                   << path_boundary->at(i).s
                   << "path boundary start s: " << path_boundary->front().s
                   << ", path boundary end s: " << path_boundary->back().s;
            continue;
        }
        adc_vertex_bound->emplace_back(left_weight, right_weight,
                                       path_boundary->at(i).l_lower.l,
                                       path_boundary->at(i).l_upper.l,
                                       left_index, right_index, rear_axle_s);
    }
    adc_vertex_bound->front_edge_to_center = front_edge_to_center;
}

void PathBoundsDeciderUtil::AddCornerBounds(
    const std::vector<SLPolygon>& sl_polygons,
    PathBoundary* const path_boundary) {
    auto* extra_path_bound = path_boundary->mutable_extra_path_bound();
    for (const auto& obs_polygon : sl_polygons) {
        if (obs_polygon.MinS() > path_boundary->back().s) {
            AINFO << "obs_polygon.MinS()" << obs_polygon.MinS()
                  << "path_boundary->back().s" << path_boundary->back().s;
            break;
        }
        if (obs_polygon.MaxS() < path_boundary->front().s) {
            continue;
        }
        if (obs_polygon.NudgeInfo() == SLPolygon::LEFT_NUDGE) {
            for (size_t i = 0; i < obs_polygon.LeftBoundary().size(); i++) {
                auto pt = obs_polygon.LeftBoundary().at(i);
                bool is_front_pt =
                    i < (obs_polygon.LeftBoundary().size() * 0.5);
                if (!AddCornerPoint(pt, *path_boundary, extra_path_bound, false,
                                    obs_polygon.id(), is_front_pt)) {
                    break;
                }
            }
            // for (auto pt : obs_polygon.LeftBoundary()) {
            //   if (!AddCornerPoint(pt, *path_boundary, extra_path_bound,
            //   false,
            //                       obs_polygon.id())) {
            //     break;
            //   }
            // }
        } else if (obs_polygon.NudgeInfo() == SLPolygon::RIGHT_NUDGE) {
            for (size_t i = 0; i < obs_polygon.RightBoundary().size(); i++) {
                auto pt = obs_polygon.RightBoundary().at(i);
                bool is_front_pt =
                    i > (obs_polygon.RightBoundary().size() * 0.5);
                if (!AddCornerPoint(pt, *path_boundary, extra_path_bound, true,
                                    obs_polygon.id(), is_front_pt)) {
                    break;
                }
            }
            // for (auto pt : obs_polygon.RightBoundary()) {
            //   if (!AddCornerPoint(pt, *path_boundary, extra_path_bound, true,
            //                       obs_polygon.id())) {
            //     break;
            //   }
            // }
        } else {
            AINFO << "no nugde info, ignore obs: " << obs_polygon.id();
        }
        if (!extra_path_bound->blocked_id.empty()) {
            break;
        }
    }
    // sort(extra_path_bound->begin(), extra_path_bound->end(),
    //      [](const InterPolatedPoint& a, const InterPolatedPoint& b) {
    //        return a.rear_axle_s < b.rear_axle_s;
    //      });
}

bool PathBoundsDeciderUtil::AddCornerPoint(
    SLPoint sl_pt, const PathBoundary& path_boundary,
    ObsCornerConstraints* extra_constraints, bool is_left, std::string obs_id,
    bool is_front_pt) {
    size_t left_index = 0;
    size_t right_index = 0;
    double left_weight = 0.0;
    double right_weight = 0.0;
    if (!path_boundary.get_interpolated_s_weight(
            sl_pt.s, &left_weight, &right_weight, &left_index, &right_index)) {
        AERROR << "Fail to find extra path bound point in path boundary: "
               << sl_pt.s
               << ", path boundary start s: " << path_boundary.front().s
               << ", path boundary end s: " << path_boundary.back().s;
        return true;
    }
    // if (left_weight < 0.05 || right_weight < 0.05) {
    //   // filter contraint that near evaulated point
    //   return true;
    // }

    double bound_l_upper = path_boundary.get_upper_bound_by_interpolated_index(
        left_weight, right_weight, left_index, right_index);
    double bound_l_lower = path_boundary.get_lower_bound_by_interpolated_index(
        left_weight, right_weight, left_index, right_index);

    double corner_l = is_left ? sl_pt.l - GetBufferBetweenADCCenterAndEdge()
                              : sl_pt.l + GetBufferBetweenADCCenterAndEdge();
    if ((is_left && corner_l < bound_l_upper) ||
        (!is_left && corner_l > bound_l_lower)) {
        if (is_left) {
            bound_l_upper = corner_l;
        } else {
            bound_l_lower = corner_l;
        }
        extra_constraints->emplace_back(
            left_weight, right_weight, bound_l_lower, bound_l_upper, left_index,
            right_index, sl_pt.s, obs_id);
        if (bound_l_upper < bound_l_lower) {
            extra_constraints->blocked_id = obs_id;
            extra_constraints->block_left_index = left_index;
            extra_constraints->block_right_index = right_index;
            AINFO << "AddCornerPoint blocked id: " << obs_id << ", index ["
                  << left_index << ", " << right_index << "]";
            return false;
        }
    }

    //   if (FLAGS_enable_expand_obs_corner) {
    //     double add_s = is_front_pt ? sl_pt.x -
    //     FLAGS_expand_obs_corner_lon_buffer
    //                                : sl_pt.x +
    //                                FLAGS_expand_obs_corner_lon_buffer;
    //     if (!path_boundary.get_interpolated_s_weight(
    //             add_s, &left_weight, &right_weight, &left_index,
    //             &right_index)) {
    //       return true;
    //     }

    //     bound_l_upper = path_boundary.get_upper_bound_by_interpolated_index(
    //         left_weight, right_weight, left_index, right_index);
    //     bound_l_lower = path_boundary.get_lower_bound_by_interpolated_index(
    //         left_weight, right_weight, left_index, right_index);

    //     if ((is_left && corner_l < bound_l_upper) ||
    //         (!is_left && corner_l > bound_l_lower)) {
    //       if (is_left) {
    //         bound_l_upper = corner_l;
    //       } else {
    //         bound_l_lower = corner_l;
    //       }
    //       extra_constraints->emplace_back(left_weight, right_weight,
    //       bound_l_lower,
    //                                       bound_l_upper, left_index,
    //                                       right_index, add_s, obs_id);
    //       if (bound_l_upper < bound_l_lower) {
    //         extra_constraints->blocked_id = obs_id;
    //         extra_constraints->block_left_index = left_index;
    //         extra_constraints->block_right_index = right_index;
    //         AINFO << "AddCornerPoint blocked id: " << obs_id << ", index ["
    //               << left_index << ", " << right_index << "]";
    //         return false;
    //       }
    //     }
    //   }
    return true;
}

bool PathBoundsDeciderUtil::RelaxEgoPathBoundary(
    PathBoundary* const path_boundary, const SLState& init_sl_state)

{
    if (path_boundary->size() < 2) {
        AINFO << "path_boundary size = 0, return.";
        return false;
    }

    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();

    double min_radius = std::max(
        vehicle_config.min_turn_radius,
        std::tan(vehicle_config.max_steer_angle / vehicle_config.steer_ratio) /
            vehicle_config.wheel_base);

    double init_frenet_kappa =
        init_sl_state.second[2] /
        std::pow(1 + std::pow(init_sl_state.second[1], 2), 1.5);
    if (init_frenet_kappa < 0) {
        init_frenet_kappa = std::min(
            -1.0 / (min_radius + relax_ego_radius_buffer), init_frenet_kappa);
    } else {
        init_frenet_kappa = std::max(
            1.0 / (min_radius + relax_ego_radius_buffer), init_frenet_kappa);
    }

    const auto& init_pt = path_boundary->at(0);
    double init_frenet_heading = Vec2d(1.0, init_sl_state.second[1]).Angle();
    double init_pt_l = init_sl_state.second[0];
    bool left_calculate_success = true;
    bool right_calculate_success = true;
    for (size_t i = 1; i < path_boundary->size(); ++i) {
        auto& left_bound = path_boundary->at(i).l_upper;
        auto& right_bound = path_boundary->at(i).l_lower;
        double delta_s = path_boundary->at(i).s - init_pt.s;
        // 近距离范围内进行扩展，太远不管了
        if (delta_s > relax_path_s_threshold) {
            left_calculate_success = false;
            right_calculate_success = false;
            break;
        }
        if (left_calculate_success &&
            (left_bound.type == BoundType::OBSTACLE ||
             path_boundary->at(i).is_nudge_bound[LEFT_INDEX])) {
            left_calculate_success = RelaxBoundaryPoint(
                &path_boundary->at(i), true, init_pt_l, init_frenet_heading,
                delta_s, init_frenet_kappa, min_radius);
        }
        if (right_calculate_success &&
            (right_bound.type == BoundType::OBSTACLE ||
             path_boundary->at(i).is_nudge_bound[RIGHT_INDEX])) {
            right_calculate_success = RelaxBoundaryPoint(
                &path_boundary->at(i), false, init_pt_l, init_frenet_heading,
                delta_s, init_frenet_kappa, min_radius);
        }
        if (!left_calculate_success && !right_calculate_success) {
            break;
        }
    }
    return true;
}

bool PathBoundsDeciderUtil::RelaxBoundaryPoint(
    PathBoundPoint* const path_bound_point, bool is_left, double init_l,
    double heading, double delta_s, double init_frenet_kappa, double min_radius)

{
    bool is_success = false;
    double protective_restrict = 0.0;
    double relax_constraint = 0.0;
    double radius = 1.0 / std::fabs(init_frenet_kappa);
    double old_buffer = obstacle_lat_buffer;
    double new_buffer =
        std::max(ego_front_slack_buffer, nonstatic_obstacle_nudge_l_buffer);
    if (is_left) {
        if (init_frenet_kappa > 0 && heading < 0) {
            is_success = util::left_arc_bound_with_heading_with_reverse_kappa(
                delta_s, min_radius, heading, init_frenet_kappa,
                &protective_restrict);
        } else {
            is_success = util::left_arc_bound_with_heading(
                delta_s, radius, heading, &protective_restrict);
        }

        relax_constraint =
            std::max(path_bound_point->l_upper.l, init_l + protective_restrict);
        AINFO << "init_pt_l: " << init_l
              << ", left_bound: " << path_bound_point->l_upper.l
              << ",  diff s: " << delta_s << ", radius: " << radius
              << ", protective_restrict: " << protective_restrict
              << ", left_obs_constraint: " << relax_constraint;

        if (path_bound_point->is_nudge_bound[LEFT_INDEX]) {
            old_buffer =
                std::max(obstacle_lat_buffer, static_obstacle_nudge_l_buffer);
        }

        relax_constraint =
            std::min(path_bound_point->l_upper.l + old_buffer - new_buffer,
                     relax_constraint);
        AINFO << "left_obs_constraint: " << relax_constraint;
        path_bound_point->l_upper.l = relax_constraint;
    } else {
        if (init_frenet_kappa < 0 && heading > 0) {
            is_success = util::right_arc_bound_with_heading_with_reverse_kappa(
                delta_s, min_radius, heading, init_frenet_kappa,
                &protective_restrict);
        } else {
            is_success = util::right_arc_bound_with_heading(
                delta_s, radius, heading, &protective_restrict);
        }
        relax_constraint =
            std::min(path_bound_point->l_lower.l, init_l + protective_restrict);
        AINFO << "init_pt_l: " << init_l
              << ", right_bound: " << path_bound_point->l_lower.l
              << ",  diff s: " << delta_s << ", radius: " << radius
              << ", protective_restrict: " << protective_restrict
              << ", right_obs_constraint: " << relax_constraint;

        if (path_bound_point->is_nudge_bound[RIGHT_INDEX]) {
            old_buffer =
                std::max(obstacle_lat_buffer, static_obstacle_nudge_l_buffer);
        }

        relax_constraint =
            std::max(path_bound_point->l_lower.l - old_buffer + new_buffer,
                     relax_constraint);
        AINFO << "right_obs_constraint: " << relax_constraint;
        path_bound_point->l_lower.l = relax_constraint;
    }
    return is_success;
}

bool PathBoundsDeciderUtil::RelaxObsCornerBoundary(
    PathBoundary* const path_boundary, const SLState& init_sl_state) {
    if (path_boundary->size() < 2) {
        AINFO << "path_boundary size = 0, return.";
        return false;
    }

    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();

    double min_radius = std::max(
        vehicle_config.min_turn_radius,
        std::tan(vehicle_config.max_steer_angle / vehicle_config.steer_ratio) /
            vehicle_config.wheel_base);

    double init_frenet_kappa =
        std::fabs(init_sl_state.second[2] /
                  std::pow(1 + std::pow(init_sl_state.second[1], 2), 1.5));
    if (init_frenet_kappa < 0) {
        init_frenet_kappa = std::min(
            -1.0 / (min_radius + relax_ego_radius_buffer), init_frenet_kappa);
    } else {
        init_frenet_kappa = std::max(
            1.0 / (min_radius + relax_ego_radius_buffer), init_frenet_kappa);
    }
    double kappa_radius = 1.0 / std::fabs(init_frenet_kappa);

    const auto& init_pt = path_boundary->at(0);
    double init_frenet_heading = Vec2d(1.0, init_sl_state.second[1]).Angle();
    double init_pt_l = init_sl_state.second[0];
    bool left_calculate_success = true;
    bool right_calculate_success = true;
    double new_buffer =
        std::max(ego_front_slack_buffer, nonstatic_obstacle_nudge_l_buffer);
    for (auto& extra_path_bound :
         *(path_boundary->mutable_extra_path_bound())) {
        double delta_s = extra_path_bound.rear_axle_s - init_pt.s;
        if (delta_s > relax_path_s_threshold) {
            break;
        }

        // calculate the left side
        if (left_calculate_success) {
            double left_protective_restrict = 0.0;
            if (init_frenet_kappa > 0 && init_frenet_heading < 0) {
                left_calculate_success =
                    util::left_arc_bound_with_heading_with_reverse_kappa(
                        delta_s, min_radius, init_frenet_heading,
                        init_frenet_kappa, &left_protective_restrict);
            } else {
                left_calculate_success = util::left_arc_bound_with_heading(
                    delta_s, kappa_radius, init_frenet_heading,
                    &left_protective_restrict);
            }

            double left_obs_constraint =
                std::max(extra_path_bound.upper_bound,
                         init_pt_l + left_protective_restrict);
            AINFO << "extra_path_bound, init_pt_l: " << init_pt_l
                  << ", left_bound: " << extra_path_bound.upper_bound
                  << ",  diff s: " << delta_s << ", min_radius: " << min_radius
                  << ", init_frenet_heading: " << init_frenet_heading
                  << ", protective_restrict: " << left_protective_restrict
                  << ", left_obs_constraint: " << left_obs_constraint;
            left_obs_constraint = std::min(
                extra_path_bound.upper_bound + obstacle_lat_buffer - new_buffer,
                left_obs_constraint);
            AINFO << "extra_path_bound left_obs_constraint: "
                  << left_obs_constraint;
            extra_path_bound.upper_bound = left_obs_constraint;
        }

        if (right_calculate_success) {
            // calculate the right side
            double right_protective_restrict = 0.0;
            if (init_frenet_kappa < 0 && init_frenet_heading > 0) {
                right_calculate_success =
                    util::right_arc_bound_with_heading_with_reverse_kappa(
                        delta_s, min_radius, init_frenet_heading,
                        init_frenet_kappa, &right_protective_restrict);
            } else {
                right_calculate_success = util::right_arc_bound_with_heading(
                    delta_s, kappa_radius, init_frenet_heading,
                    &right_protective_restrict);
            }

            double right_obs_constraint =
                std::min(extra_path_bound.lower_bound,
                         init_pt_l + right_protective_restrict);
            AINFO << "extra_path_bound, init_pt_l: " << init_pt_l
                  << ", right_bound: " << extra_path_bound.lower_bound
                  << ",  diff s: " << delta_s << ", min_radius: " << min_radius
                  << ", init_frenet_heading: " << init_frenet_heading
                  << ", protective_restrict: " << right_protective_restrict
                  << ", right_obs_constraint: " << right_obs_constraint;
            right_obs_constraint = std::max(
                extra_path_bound.lower_bound - obstacle_lat_buffer + new_buffer,
                right_obs_constraint);
            AINFO << "extra_path_bound, right_obs_constraint: "
                  << right_obs_constraint;
            extra_path_bound.lower_bound = right_obs_constraint;
        }

        if (!left_calculate_success && !right_calculate_success) {
            break;
        }
    }
    return true;
}

bool PathBoundsDeciderUtil::UpdateBlockInfoWithObsCornerBoundary(
    PathBoundary* const path_boundary, std::string* const blocked_id) {
    if (path_boundary->extra_path_bound().blocked_id.empty()) {
        AINFO << "UpdateBlockInfoWithObsCornerBoundary, block id empty";
        return true;
    }
    auto* extra_path_bound = path_boundary->mutable_extra_path_bound();
    size_t path_boundary_block_index = extra_path_bound->block_right_index;

    // trim path boundary width corner constraints block obstacle id
    *blocked_id = extra_path_bound->blocked_id;
    TrimPathBounds(static_cast<int>(path_boundary_block_index), path_boundary);
    AINFO << "update block id: " << *blocked_id
          << ", path_boundary size: " << path_boundary->size();

    if (path_boundary->size() < 1) {
        extra_path_bound->clear();
        AERROR << "UpdateBlockInfoWithObsCornerBoundary, new_path_index < 1";
        return false;
    }

    size_t new_path_index = path_boundary->size() - 1;
    while (extra_path_bound->size() > 0 &&
           (extra_path_bound->back().id == *blocked_id ||
            extra_path_bound->back().right_index > new_path_index)) {
        AINFO << "remove extra_path_bound: s "
              << extra_path_bound->back().rear_axle_s << ", index ["
              << extra_path_bound->back().left_index << ", "
              << extra_path_bound->back().right_index << "]";
        extra_path_bound->pop_back();
    }
    return false;
}

// void PathBoundsDeciderUtil::GetBoundaryFromLaneChangeForbiddenZone(
//     PathBoundary* const path_bound) {
//     if (path_bound->empty()) {
//         AINFO << "Path boundary is empty, return.";
//         return;
//     }

//     if (is_clear_to_change_lane_) {
//         is_exist_lane_change_start_position_ = false;
//         return;
//     }
//     double lane_change_start_s = 0.0;
//     const ReferenceLine& reference_line =
//         reference_line_info_->reference_line();
//     // If there is a pre-determined lane-change starting position, then use
//     it;
//     // otherwise, decide one.
//     if (is_exist_lane_change_start_position_) {
//         common::SLPoint point_sl;
//         reference_line.XYToSL(lane_change_start_xy_, &point_sl);
//         lane_change_start_s = point_sl.s();
//     } else {
//         // TODO(jiacheng): train ML model to learn this.
//         lane_change_start_s =
//             config_.lane_change_prepare_length() + init_sl_state_.first[0];

//         // Update the lane_change_start_xy_ decided by lane_change_start_s
//         GetLaneChangeStartPoint(reference_line, init_sl_state_.first[0],
//                                 &lane_change_start_xy_);
//     }

//     // Remove the target lane out of the path-boundary, up to the decided S.
//     if (lane_change_start_s < init_sl_state_.first[0]) {
//         // If already passed the decided S, then return.
//         return;
//     }
//     double adc_half_width =
//         VehicleConfigHelper::GetConfig().vehicle_param().width() / 2.0;
//     for (size_t i = 0; i < path_bound->size(); ++i) {
//         double curr_s = (*path_bound)[i].s;
//         if (curr_s > lane_change_start_s) {
//             break;
//         }
//         double curr_lane_left_width = 0.0;
//         double curr_lane_right_width = 0.0;
//         double offset_to_map = 0.0;
//         reference_line.GetOffsetToMap(curr_s, &offset_to_map);
//         if (reference_line.GetLaneWidth(curr_s, &curr_lane_left_width,
//                                         &curr_lane_right_width)) {
//             double offset_to_lane_center = 0.0;
//             reference_line.GetOffsetToMap(curr_s, &offset_to_lane_center);
//             curr_lane_left_width += offset_to_lane_center;
//             curr_lane_right_width -= offset_to_lane_center;
//         }
//         curr_lane_left_width -= offset_to_map;
//         curr_lane_right_width += offset_to_map;

//         (*path_bound)[i].l_lower.l =
//             init_sl_state_.second[0] > curr_lane_left_width
//                 ? curr_lane_left_width + adc_half_width
//                 : (*path_bound)[i].l_lower.l;
//         (*path_bound)[i].l_lower.l = std::fmin((*path_bound)[i].l_lower.l,
//                                                init_sl_state_.second[0] -
//                                                0.1);
//         (*path_bound)[i].l_upper.l =
//             init_sl_state_.second[0] < -curr_lane_right_width
//                 ? -curr_lane_right_width - adc_half_width
//                 : (*path_bound)[i].l_upper.l;
//         (*path_bound)[i].l_upper.l = std::fmax((*path_bound)[i].l_upper.l,
//                                                init_sl_state_.second[0] +
//                                                0.1);
//     }
// }

}  // namespace planning
}  // namespace arcsoft