/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#pragma once

#include "common/reference_line.h"
#include "common/vehicle_config_helper.h"
#include "path_bounds_decider_util.h"
#include "piecewise_jerk_path_problem.h"
#include "piecewise_jerk_trajectory1d.h"

namespace arcsoft {
namespace planning {

struct PiecewiseJerkPathConfig {
    double path_reference_l_weight = 100.0;
    double lateral_derivative_bound_default = 2.0;
    double dddl_weight = 500.0;  // ⬇️ 降低 jerk 抑制过度平滑
    double ddl_weight = 100.0;   // ⬆️ 增加加速度权重，压制回摆
    double dl_weight = 200.0;    // ⬆️ 增加速度权重，加快衰减
    double l_weight = 5.0;
    double weight_end_state_l = 0.0;
    double weight_end_state_dl = 0.0;
    double weight_end_state_ddl = 0.0;
    int max_iteration = 4000;
};

class PiecewiseJerkPathOptimizer {
public:
    PiecewiseJerkPathOptimizer() = default;
    virtual ~PiecewiseJerkPathOptimizer() = default;

    bool Solve(std::vector<PathBoundary>& path_boundaries,
               FrenetFramePath& candidate_path_data);

    void set_reference_line(const ReferenceLine* reference_line) {
        reference_line_ = reference_line;
    }

    void set_init_sl_state(const SLState& init_sl_state) {
        init_sl_state_ = init_sl_state;
    }
    void set_optimizer_params(const PiecewiseJerkPathConfig& config) {
        config_ = config;
    }

    void set_is_change_lane(const bool is_change_lane) {
        is_change_lane_ = is_change_lane;
    }

private:
    static void CalculateAccBound(
        const PathBoundary& path_boundary, const ReferenceLine& reference_line,
        std::vector<std::pair<double, double>>* ddl_bounds);

    static double EstimateJerkBoundary(const double vehicle_speed);

    static void UpdatePathRefWithBound(const PathBoundary& path_boundary,
                                       double weight,
                                       std::vector<double>* ref_l,
                                       std::vector<double>* weight_ref_l);

    static bool OptimizePath(
        const SLState& init_state, const std::array<double, 3>& end_state,
        std::vector<double> l_ref, std::vector<double> l_ref_weight,
        const PathBoundary& path_boundary,
        const std::vector<std::pair<double, double>>& ddl_bounds,
        double dddl_bound, const PiecewiseJerkPathConfig& config,
        std::vector<double>* x, std::vector<double>* dx,
        std::vector<double>* ddx);

    static FrenetFramePath ToPiecewiseJerkPath(const std::vector<double>& x,
                                               const std::vector<double>& dx,
                                               const std::vector<double>& ddx,
                                               const double delta_s,
                                               const double start_s);

private:
    const ReferenceLine* reference_line_ = nullptr;
    SLState init_sl_state_;
    PiecewiseJerkPathConfig config_;
    bool is_change_lane_;
};

}  // namespace planning
}  // namespace arcsoft