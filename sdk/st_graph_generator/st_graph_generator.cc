#include "st_graph_generator.h"

#include "common/box2d.h"
#include "common/speed/speed_limit.h"

namespace arcsoft {
namespace planning {

void STGraphGenerator::Init() {}

// 接收优化路径、障碍物预测轨迹，生成ST图
bool STGraphGenerator::Process(const size_t candidate_transition_context_id) {
    ///
    auto& st_graph_data_map = session_->mutable_st_graph_data_map();
    st_graph_data_map.clear();

    const auto* path_data_vec =
        session_->path_data_vec(candidate_transition_context_id);

    if (path_data_vec == nullptr) {
        AERROR << "candidate_transition_context_id: "
               << candidate_transition_context_id << " path_data is nullptr! ";
        return false;
    }

    if (path_data_vec->size() <= 0) {
        AERROR << "candidate_transition_context_id: "
               << candidate_transition_context_id << " path_data is empty! ";
        return false;
    }

    const auto& candidate_transition_context =
        session_->candidate_transition_context(candidate_transition_context_id);
    auto& scenario_state = candidate_transition_context.target_state;

    reference_line_ = (scenario_state != ScenarioStateEnum::CRUISE_CHANGE)
                          ? session_->current_lane()
                          : session_->target_lane();

    if (reference_line_ == nullptr) {
        AERROR << "reference_line_ is nullptr! ";
        return false;
    }

    std::vector<StGraphData> st_graph_data_vec;
    const auto& frenet_obstacles = reference_line_->frenet_obstacles();
    constexpr double kCollisionBuffer = 45.0;
    constexpr double kIgnoreSpeedDelta = -1.0;
    const auto& init_point = session_->planning_start_point();
    AINFO << "frenet_obstacles size: " << frenet_obstacles.size();
    // 前期该vector中仅有一条path
    for (const auto& path_data : *path_data_vec) {
        std::vector<STBoundary> boundaries;
        auto& obstacles = session_->mutable_obstacles();
        std::unordered_set<int> obstacles_collsion_sets;
        for (auto& frenet_obstacle : frenet_obstacles) {
            auto obstacle = obstacles.Find(frenet_obstacle.id);
            if (obstacle == nullptr) {
                AERROR << "obstacle id : " << frenet_obstacle.id
                       << " is not found in obstacles! ";
                continue;
            }
            if (obstacle->trajectory().size() <= 0) {
                AINFO << "obstacle id : " << obstacle->id()
                      << " Has No Prediction Trajectory. ";
                continue;
            }
            // 过滤不必要的障碍物投影
            if (scenario_state != ScenarioStateEnum::CRUISE_CHANGE) {
                // AINFO << "scenario_state is not CRUISE_CHANGE";
                if (frenet_obstacle.sl_boundary.start_s <=
                    vehicle_param_.front_edge_to_center) {
                    // AINFO
                    //     << "obstacle id : " << obstacle->id()
                    //     << " is behind the ego vehicle , ignore st boundary
                    //     !";
                    continue;
                }
            } else {
                if (std::fabs(frenet_obstacle.l) > 1.5 &&
                    frenet_obstacle.sl_boundary.end_s <=
                        vehicle_param_.front_edge_to_center) {
                    // AINFO << "obstacle id : " << obstacle->id()
                    //       << " is out of target lane thd && behind ego "
                    //          "vehicle, ignore st "
                    //          "boundary!";
                    continue;
                }
            }
            if (frenet_obstacle.sl_boundary.start_s > path_data.back().s) {
                // AINFO << "obstacle id : " << obstacle->id()
                //       << " is before the endpoint of path , ignore st "
                //          "boundary!";
                continue;
            }

            // if (frenet_obstacle.sl_boundary.start_s > kCollisionBuffer &&
            //     obstacle.get()->speed() - init_point.v > kIgnoreSpeedDelta) {
            //     AINFO << "obstacle id : " << obstacle->id()
            //           << " is far and fast from ego, ignore st "
            //              "boundary!";
            //     continue;
            // }
            // 粗略判断障碍物路径是否和自车路径有碰撞
            if (!RoughCollisionDetect(path_data, obstacle.get())) {
                AINFO << "obstacle id : " << obstacle->id()
                      << " will not collide with the planned path, ignore st "
                         "boundary!";
                continue;
            }
            // 投影障碍物预测轨迹
            auto start_time = std::chrono::system_clock::now();
            std::vector<STPoint> lower_points;
            std::vector<STPoint> upper_points;
            AINFO << "obstacle id : " << obstacle->id()
                  << " will detect collision in st boundary";
            if (!GetOverlapBoundaryPoints(path_data, obstacle.get(),
                                          &lower_points, &upper_points)) {
                // AINFO << "obstacle id : " << obstacle->id()
                //       << " GetOverlapBoundaryPoints is failed! ";
                continue;
            }
            // for (size_t i = 0; i < lower_points.size(); ++i) {
            //     AINFO << "[lower points] " << i
            //           << "  s: " << lower_points[i].s()
            //           << " t: " << lower_points[i].t();
            // }
            auto end_time = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end_time - start_time;
            AINFO << "[TimeCost] GetOverlapBoundaryPoints used time: "
                  << diff.count() * 1000 << " ms.";
            AINFO << "obstacle id : " << obstacle->id()
                  << " will collision with path in st boundary!";

            obstacles_collsion_sets.insert(obstacle->id());
            if (obstacle_collision_count_map_.find(obstacle->id()) !=
                obstacle_collision_count_map_.end()) {
                obstacle_collision_count_map_[obstacle->id()] = std::min(
                    50, obstacle_collision_count_map_[obstacle->id()] + 1);
            } else {
                obstacle_collision_count_map_[obstacle->id()] = 1;
            }
            if (obstacle_collision_count_map_[obstacle->id()] <= 2) {
                AINFO << "obstacle id : " << obstacle->id()
                      << " collision frequency is "
                      << obstacle_collision_count_map_[obstacle->id()]
                      << " that less than 2 times, ignore st "
                         "boundary!";
                continue;
            }

            auto path_st_boundary =
                STBoundary::CreateInstance(lower_points, upper_points);
            path_st_boundary.set_id(std::to_string(obstacle->id()));

            if (path_st_boundary.IsEmpty()) {
                AINFO << "obstacle id : " << obstacle->id()
                      << " path_st_boundary is emtpy! ";
                continue;
            }

            obstacle->set_path_st_boundary(path_st_boundary);
            boundaries.emplace_back(path_st_boundary);
        }

        std::vector<int> keysToRemove;
        // 第一遍遍历：记录需要删除的键
        for (const auto& pair : obstacle_collision_count_map_) {
            if (obstacles_collsion_sets.find(pair.first) ==
                obstacles_collsion_sets.end()) {
                keysToRemove.push_back(pair.first);
            }
        }
        // 第二遍：统一删除
        for (const auto& key : keysToRemove) {
            obstacle_collision_count_map_.erase(key);
        }

        // 路径末端放置虚拟障碍物
        // std::vector<STPoint> lower_points_virtual;
        // std::vector<STPoint> upper_points_virtual;
        // lower_points_virtual.emplace_back(path_data.back().s - 1, 0);
        // lower_points_virtual.emplace_back(path_data.back().s - 1, 3.5);
        // upper_points_virtual.emplace_back(path_data.back().s + 1, 0);
        // upper_points_virtual.emplace_back(path_data.back().s + 1, 3.5);
        // auto path_st_boundary = STBoundary::CreateInstance(
        //     lower_points_virtual, upper_points_virtual);
        // path_st_boundary.set_id(std::to_string(-1));
        // if (!path_st_boundary.IsEmpty()) {
        //     AINFO << "add virtual obstacle succeed ";
        //     boundaries.emplace_back(path_st_boundary);
        // }
        auto start_time1 = std::chrono::system_clock::now();
        const auto& discretized_path = DiscretizedPath(path_data);
        const auto& init_point = session_->planning_start_point();

        // [TODO] 计算speed_limit
        SpeedLimit speed_limit;
        GetSpeedLimitByS(discretized_path, speed_limit);
        AINFO << "[Time Cost] After GetSpeedLimitByS";

        const double path_data_length = discretized_path.Length();
        const double st_graph_total_time = 4.0;

        // 计算min_s，用于停车计算
        double min_s_on_st_boundaries = path_data_length;

        // [TODO] 获取巡航速度
        double cruise_speed = kDefaultCruiseSpeed;

        // 更新st图信息
        StGraphData st_graph_data;
        st_graph_data.LoadData(boundaries, min_s_on_st_boundaries, init_point,
                               speed_limit, cruise_speed, path_data_length,
                               st_graph_total_time);

        st_graph_data_vec.emplace_back(st_graph_data);
        auto end_time1 = std::chrono::system_clock::now();
        std::chrono::duration<double> diff1 = end_time1 - start_time1;
        AINFO << "[TimeCost] GetSpeedLimitByS used time: "
              << diff1.count() * 1000 << " ms.";
    }

    st_graph_data_map.emplace(candidate_transition_context_id,
                              st_graph_data_vec);

    return true;
}

DiscretizedPath STGraphGenerator::filterCurvature(
    const DiscretizedPath& discretized_path, int window_size) {
    // 检查输入有效性
    if (window_size <= 0 || window_size % 2 == 0) {
        throw std::invalid_argument("窗口大小必须是正奇数");
    }
    if (discretized_path.empty()) {
        return discretized_path;  // 空路径直接返回
    }

    auto filtered_path = discretized_path;  // 复制原始路径，仅修改曲率
    int half_window = window_size / 2;
    int path_size = discretized_path.size();

    for (int i = 0; i < path_size; ++i) {
        // 计算窗口的起始和结束索引，处理边界情况
        int start = std::max(0, i - half_window);
        int end = std::min(path_size - 1, i + half_window);

        // 计算窗口内的曲率平均值
        double sum = 0.0;
        int count = 0;
        for (int j = start; j <= end; ++j) {
            sum += discretized_path[j].curvature;
            count++;
        }

        // 赋值滤波后的曲率
        filtered_path[i].curvature = sum / count;
    }

    return filtered_path;
}

bool STGraphGenerator::GetSpeedLimitByS(const DiscretizedPath& discretized_path,
                                        SpeedLimit& speed_limit) {
    const auto& scenario_type = session_->scenario_type();
    // AINFO << "scenario_type: " << static_cast<int>(scenario_type);
    DLOG_ARRAY_BEGIN("speed_limits");
    // auto curvatureFilter = filterCurvature(discretized_path, 5);
    // for (size_t i = 0; i < discretized_path.size(); i++) {
    //     AINFO << "filterCurvature: " << curvatureFilter[i].curvature
    //           << " originCurvature: " << discretized_path[i].curvature;
    // }

    double speed_limit_by_lane_nums = planning_upper_speed_limit;
    double curvature_gain_by_lane_nums = 1;
    if (scenario_type == ScenarioType::SINGLE_LANE) {
        speed_limit_by_lane_nums = 11.1;
        curvature_gain_by_lane_nums = 0.6;
    } else if (scenario_type == ScenarioType::TWO_LANE_ROAD) {
        speed_limit_by_lane_nums = 15.0;
        curvature_gain_by_lane_nums = 0.8;
    }

    double speed_limit_by_lane_nums_delta =
        (speed_limit_by_lane_nums - speed_limit_by_lane_nums_last_);
    if (speed_limit_by_lane_nums_delta > 0.3) {
        speed_limit_by_lane_nums_delta = 0.3;
    } else if (speed_limit_by_lane_nums_delta < -0.3) {
        speed_limit_by_lane_nums_delta = -0.3;
    }
    speed_limit_by_lane_nums_last_ += speed_limit_by_lane_nums_delta;

    // AINFO << "speed_limit_by_lane_nums_last_: "
    //       << speed_limit_by_lane_nums_last_
    //       << " speed_limit_by_lane_nums: " << speed_limit_by_lane_nums;

    for (size_t i = 0; i < discretized_path.size(); ++i) {
        const double path_s = discretized_path[i].s;

        // soft constraints
        // static constexpr double max_centric_acceleration_limit = 0.5;
        // static constexpr double minimal_kappa = 0.00001;
        // const double speed_limit_from_centripetal_acc =
        //     std::sqrt(max_centric_acceleration_limit /
        //               std::fmax(std::fabs(discretized_path.at(i).curvature),
        //                         minimal_kappa));

        const double speed_limit_from_centripetal_acc =
            curvature_gain_by_lane_nums *
            LookUpTableSpeedLimit(std::fabs(discretized_path[i].curvature));

        double speed_limit_soft_constraints = std::fmin(
            speed_limit_by_lane_nums_last_, speed_limit_from_centripetal_acc);

        // hard constraints
        double hmi_speed_limit =
            session_->local_view().vehicle_chassis.hmi_speed_limit;
        double speed_limit_hard_constraints =
            std::fmin(planning_upper_speed_limit, hmi_speed_limit);
        DLOG_DICT_BEGIN();
        DLOG_DICT_ITEM("hmi", hmi_speed_limit);
        DLOG_DICT_ITEM("curve", speed_limit_from_centripetal_acc);
        DLOG_DICT_ITEM("lane_num", speed_limit_by_lane_nums);

        DLOG_DICT_END();

        // AINFO << "path_s: " << path_s
        //       << " upper_speed: " << planning_upper_speed_limit
        //       << " hmi_speed_limit: " << hmi_speed_limit
        //       << " curvature: " << discretized_path.at(i).curvature
        //       << "  centripetal_acc: " << speed_limit_from_centripetal_acc
        //       << "speed_limit_lane_nums: " << speed_limit_by_lane_nums
        //       << " speed_limit_soft_constraints: "
        //       << speed_limit_soft_constraints
        //       << " speed_limit_hard_constraints: "
        //       << speed_limit_hard_constraints;

        speed_limit.AppendSpeedLimitForHardConstraints(
            path_s, speed_limit_hard_constraints);
        speed_limit.AppendSpeedLimitForSoftConstraints(
            path_s, speed_limit_soft_constraints);
    }
    DLOG_ARRAY_END("speed_limits");

    return true;
}

bool STGraphGenerator::RoughCollisionDetect(const PathData& path_points,
                                            const Obstacle* obstacle) {
    if (path_points.empty()) {
        AERROR << "No points in path_data_.discretized_path().";
        return false;
    }

    const auto& obs_trajectory = obstacle->trajectory();
    const double obstacle_length = obstacle->length();
    const double obstacle_width = obstacle->width();

    std::vector<common::Vec2d> polygon_point;
    polygon_point.reserve(obstacle->polygon().points().size());
    for (size_t i = 0; i < obstacle->polygon().points().size(); i++) {
        double relative_x =
            obstacle->polygon().points()[i].x() - obstacle->center_x();
        double relative_y =
            obstacle->polygon().points()[i].y() - obstacle->center_y();
        double x, y;
        if (obstacle->speed() >= 0) {
            if (i == 0 || i == 3) {
                x = relative_x + obs_trajectory.back().x;
                y = relative_y + obs_trajectory.back().y;
            } else {
                x = relative_x + obs_trajectory[0].x;
                y = relative_y + obs_trajectory[0].y;
            }
        } else {
            if (i == 1 || i == 2) {
                x = relative_x + obs_trajectory.back().x;
                y = relative_y + obs_trajectory.back().y;
            } else {
                x = relative_x + obs_trajectory[0].x;
                y = relative_y + obs_trajectory[0].y;
            }
        }
        polygon_point.emplace_back(x, y);
    }
    common::Polygon2d trajectory_point_polygon_path(polygon_point);

    // 遍历自车路径点
    DiscretizedPath discretized_path = DiscretizedPath(path_points);
    auto path_len = discretized_path.Length();
    const double step_length = vehicle_param_.length;
    // 横向buffer
    double l_buffer = 0.6;
    for (double path_s = 0.0; path_s < path_len; path_s += step_length) {
        const auto curr_adc_path_point =
            discretized_path.Evaluate(path_s + discretized_path.front().s);
        if (CheckOverlap(curr_adc_path_point, trajectory_point_polygon_path,
                         l_buffer)) {
            return true;
        }
    }
    return false;
}

bool STGraphGenerator::GetOverlapBoundaryPoints(
    const PathData& path_points, const Obstacle* obstacle,
    std::vector<STPoint>* lower_points, std::vector<STPoint>* upper_points) {
    if (path_points.empty()) {
        AERROR << "No points in path_data_.discretized_path().";
        return false;
    }

    // 横向buffer
    double l_buffer = 0.3;

    const auto& obs_trajectory = obstacle->trajectory();
    const double obstacle_length = obstacle->length();
    const double obstacle_width = obstacle->width();

    DiscretizedPath discretized_path = DiscretizedPath(path_points);

    // 2. 遍历预测轨迹点并做碰撞检测
    for (size_t i = 0; i < obs_trajectory.size(); ++i) {
        const auto& obs_trajectory_point = obs_trajectory[i];

        Polygon2d obstacle_shape =
            obstacle->GetObstacleTrajectoryPolygon(obs_trajectory_point);

        double trajectory_point_time = obs_trajectory_point.t;
        static constexpr double kNegtiveTimeThreshold = -1.0;
        if (trajectory_point_time < kNegtiveTimeThreshold) {
            continue;
        }
        bool collision = CheckOverlapWithTrajectoryPoint(
            discretized_path, obstacle_shape, upper_points, lower_points,
            l_buffer, obstacle_length, obstacle_width, trajectory_point_time);
    }

    // Sanity checks and return.
    std::sort(lower_points->begin(), lower_points->end(),
              [](const STPoint& a, const STPoint& b) { return a.t() < b.t(); });
    std::sort(upper_points->begin(), upper_points->end(),
              [](const STPoint& a, const STPoint& b) { return a.t() < b.t(); });
    // DCHECK_EQ(lower_points->size(), upper_points->size());
    return (lower_points->size() > 1 && upper_points->size() > 1);
}

bool STGraphGenerator::CheckOverlapWithTrajectoryPoint(
    const DiscretizedPath& discretized_path, const Polygon2d& obstacle_shape,
    std::vector<STPoint>* upper_points, std::vector<STPoint>* lower_points,
    const double l_buffer, const double obstacle_length,
    const double obstacle_width, const double trajectory_point_time) const {
    const double step_length = vehicle_param_.front_edge_to_center;
    auto path_len = discretized_path.Length();

    constexpr double point_extension = 0.0;
    // 遍历自车路径点
    for (double path_s = 0.0; path_s < path_len; path_s += step_length) {
        const auto curr_adc_path_point =
            discretized_path.Evaluate(path_s + discretized_path.front().s);
        if (CheckOverlap(curr_adc_path_point, obstacle_shape, l_buffer)) {
            // Found overlap, start searching with higher resolution
            // const double backward_distance = -step_length;
            const double backward_distance = 0.0;
            const double forward_distance = vehicle_param_.length +
                                            vehicle_param_.width +
                                            obstacle_length + obstacle_width;
            const double fine_tuning_step_length = 0.4;  // in meters

            bool find_low = false;
            bool find_high = false;
            double low_s = std::fmax(0.0, path_s + backward_distance);
            double high_s =
                std::fmin(discretized_path.Length(), path_s + forward_distance);

            // Keep shrinking by the resolution bidirectionally until
            // finally locating the tight upper and lower bounds.
            while (low_s < high_s) {
                if (find_low && find_high) {
                    break;
                }
                if (!find_low) {
                    const auto& point_low = discretized_path.Evaluate(
                        low_s + discretized_path.front().s);
                    if (!CheckOverlap(point_low, obstacle_shape, l_buffer)) {
                        low_s += fine_tuning_step_length;
                    } else {
                        find_low = true;
                    }
                }
                if (!find_high) {
                    const auto& point_high = discretized_path.Evaluate(
                        high_s + discretized_path.front().s);
                    if (!CheckOverlap(point_high, obstacle_shape, l_buffer)) {
                        high_s -= fine_tuning_step_length;
                    } else {
                        find_high = true;
                    }
                }
            }
            if (find_high && find_low) {
                lower_points->emplace_back(low_s - point_extension,
                                           trajectory_point_time);
                upper_points->emplace_back(high_s + point_extension,
                                           trajectory_point_time);
            }
            return true;
        }
    }
    return false;
}

bool STGraphGenerator::CheckOverlap(const PathPoint& path_point,
                                    const Polygon2d& obs_polygon,
                                    const double l_buffer) const {
    // Convert reference point from center of rear axis to center of ADC.
    Vec2d ego_center_map_frame((vehicle_param_.front_edge_to_center -
                                vehicle_param_.back_edge_to_center) *
                                   0.5,
                               (vehicle_param_.left_edge_to_center -
                                vehicle_param_.right_edge_to_center) *
                                   0.5);
    ego_center_map_frame.SelfRotate(path_point.heading);
    ego_center_map_frame.set_x(ego_center_map_frame.x() + path_point.x);
    ego_center_map_frame.set_y(ego_center_map_frame.y() + path_point.y);

    // Compute the ADC bounding box.
    common::Box2d adc_box(ego_center_map_frame, path_point.heading,
                          vehicle_param_.length,
                          vehicle_param_.width + l_buffer * 2);

    // Check whether ADC polygon overlaps with obstacle polygon.
    Polygon2d adc_polygon(adc_box);
    return obs_polygon.HasOverlap(adc_polygon);
}

double STGraphGenerator::LookUpTableSpeedLimit(
    const double path_point_curvature) const {
    // 1. 检查容器是否为空
    if (curvature_table.empty() || speed_limit_table.empty()) {
        return 22.0;  // 默认安全值
    }

    // 2. 检查输入是否为有效数值
    if (!std::isfinite(path_point_curvature)) {
        AINFO << " path_point_curvature: " << path_point_curvature;
        return speed_limit_table.front();  // 保守值
    }

    // 3. 检查边界情况
    if (path_point_curvature <= curvature_table.front()) {
        return speed_limit_table.front();
    }
    if (path_point_curvature >= curvature_table.back()) {
        return speed_limit_table.back();
    }

    auto it = std::lower_bound(curvature_table.begin(), curvature_table.end(),
                               path_point_curvature);
    if (it == curvature_table.begin()) {  // 理论上不会发生（因前置检查）
        return speed_limit_table.front();
    }
    if (it == curvature_table.end()) {  // 理论上不会发生（因前置检查）
        return speed_limit_table.back();
    }
    size_t upper_idx = it - curvature_table.begin();
    size_t lower_idx = upper_idx - 1;

    // AINFO << " path_point_curvature: " << path_point_curvature
    //       << " upper_idx: " << upper_idx << "lower_idx" << lower_idx;

    // 使用提供的lerp函数进行线性插值
    return math::lerp(speed_limit_table[lower_idx], curvature_table[lower_idx],
                      speed_limit_table[upper_idx], curvature_table[upper_idx],
                      path_point_curvature);
}

}  // namespace planning
}  // namespace arcsoft
