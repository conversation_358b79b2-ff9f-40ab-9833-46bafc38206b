/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __ST_GRAPH_GENERATOR_H__
#define __ST_GRAPH_GENERATOR_H__

#include <unordered_set>

#include "common/discretized_path.h"
#include "common/speed/st_boundary.h"
#include "common/vehicle_config_helper.h"
#include "framework/task.h"

namespace arcsoft {
namespace planning {

class STGraphGenerator : public framework::Task {
public:
    STGraphGenerator(const std::string& name, framework::Session* session)
        : Task(name, session) {
        vehicle_param_ =
            common::VehicleConfigHelper::Instance().GetVehicleParam();
    }
    ~STGraphGenerator() = default;

    void Init() override;
    bool Process(const size_t candidate_transition_context_id) override;

private:
    bool GetOverlapBoundaryPoints(const PathData& path_data,
                                  const Obstacle* obstacle,
                                  std::vector<STPoint>* lower_points,
                                  std::vector<STPoint>* upper_points);

    bool RoughCollisionDetect(const PathData& path_data,
                              const Obstacle* obstacle);

    bool CheckOverlapWithTrajectoryPoint(
        const DiscretizedPath& discretized_path,
        const Polygon2d& obstacle_shape, std::vector<STPoint>* upper_points,
        std::vector<STPoint>* lower_points, const double l_buffer,
        const double obstacle_length, const double obstacle_width,
        const double trajectory_point_time) const;

    bool CheckOverlap(const PathPoint& path_point, const Polygon2d& obs_polygon,
                      const double l_buffer) const;

    bool GetSpeedLimitByS(const DiscretizedPath& discretized_path,
                          SpeedLimit& speed_limit);
    double LookUpTableSpeedLimit(const double path_point_curvature) const;
    DiscretizedPath filterCurvature(const DiscretizedPath& discretized_path,
                                    int window_size);

private:
    common::VehicleParamConfig vehicle_param_;
    std::shared_ptr<ReferenceLine> reference_line_ = nullptr;
    std::unordered_map<int, int> obstacle_collision_count_map_;

    std::vector<double> curvature_table = {0.0,   0.001, 0.005, 0.01, 0.011,
                                           0.012, 0.013, 0.015, 0.02, 0.1};
    std::vector<double> speed_limit_table = {27, 22, 18, 15, 13,
                                             12, 11, 10, 10, 10};
    double speed_limit_by_lane_nums_last_ = planning_upper_speed_limit;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __ST_GRAPH_GENERATOR_H__ */