set(PROJECT_NAME arcsoft_ads_planning)

arcpkg_import(
  "arcsoft_ads_noa_i/0.4@ads"
  "Eigen/3.4.0"
 )

# Validate interface version with sdk version
arcpkg_get_pkg_property(arcsoft_ads_noa_i_version
                        ads::arcsoft_ads_noa_i ARCPKG_VERSION)
message(
  STATUS "arcsoft_ads_noa_i version: ${arcsoft_ads_noa_i_version}")
message(STATUS "arcsoft_ads_planning   version: ${PROJECT_VERSION}")
if((PROJECT_VERSION STREQUAL "main") OR (PROJECT_VERSION MATCHES
                                         "^${arcsoft_ads_noa_i_version}"))
  message(STATUS "Sanity check of version: PASSED")
else()
  message(
    FATAL_ERROR
      "Sanity check of version: FAILED. Please set version w.r.t. arcsoft_ads_noa_i, e.g. ${arcsoft_ads_noa_i_version}.x"
  )
endif()

# 添加子目录（顺序确保依赖关系）
add_subdirectory(common)
add_subdirectory(framework)
add_subdirectory(auto_drive_mode_state_machine)
add_subdirectory(utils)
add_subdirectory(reference_line_manager)
add_subdirectory(obstacle_manager)
add_subdirectory(lane_change_decider)
add_subdirectory(scenario)
add_subdirectory(behavior_generator)
add_subdirectory(path_optimizer)
add_subdirectory(st_graph_generator)
add_subdirectory(speed_planner)
add_subdirectory(speed_optimizer)
add_subdirectory(trajectory_evaluator)
add_subdirectory(trajectory_stitcher)

file(GLOB HDRS "*.h" "*.hpp")
file(GLOB SRCS "*.cpp" "*.c" "*.cc")
add_library(
  ${PROJECT_NAME}
  ${SRCS}
  ${HDRS} 
)

# Avoid eigen namespace conflict with other libraries if using different versions
target_compile_definitions(${PROJECT_NAME}
  PRIVATE
  Eigen=ArcAdsPlanningEigen
)

target_include_directories(
  ${PROJECT_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}
                          ${CMAKE_CURRENT_SOURCE_DIR}/../third_party)
target_link_libraries(
  ${PROJECT_NAME}
  PUBLIC 
    ads::arcsoft_ads_noa_i
  PRIVATE
    arcpkg::mlog 
    arcpkg::dlog
    common
    framework
    auto_drive_mode_state_machine
    utils
    reference_line_manager
    obstacle_manager
    lane_change_decider
    scenario
    behavior_generator
    path_optimizer
    st_graph_generator
    speed_planner
    speed_optimizer
    trajectory_evaluator
    trajectory_stitcher
    osqpstatic
    arcpkg::ghc-filesystem
    arcpkg::Eigen
)
if(CMAKE_SYSTEM_NAME MATCHES "Linux")
  target_link_libraries(${PROJECT_NAME} PRIVATE dl)
endif()

# Package define
arcpkg_define(
  NAME ${PROJECT_NAME}
  VERSION ${PROJECT_VERSION}
  USER "ads"
  LIBS ${PROJECT_NAME}
  INSTALL "${CMAKE_CURRENT_SOURCE_DIR}/../CHANGELOG.md" DESTINATION .
)