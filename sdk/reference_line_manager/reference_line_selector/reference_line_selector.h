/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __REFERENCE_LINE_SELECTOR_H__
#define __REFERENCE_LINE_SELECTOR_H__

#include "common/basic_types.h"
#include "common/forward_simulator.h"
#include "common/vehicle_config_helper.h"
#include "framework/session.h"

namespace arcsoft {
namespace planning {

class ReferenceLineSelector {
public:
    explicit ReferenceLineSelector(framework::Session* session);
    ~ReferenceLineSelector() = default;

    bool Process();

private:
    void filter_reference_lines();
    bool select_current_lane();
    bool calculate_candidates_score(
        const std::shared_ptr<ReferenceLine>& last_current_lane,
        std::vector<std::shared_ptr<ReferenceLine>>& candidate_reference_lines);
    double calculate_comfortability_cost(ReferenceLine* reference_line);
    void set_ego_state(const TrajectoryPoint& planning_start_point,
                       const FrenetCoordinateSystem* frenet_coordinate,
                       AgentPose& ego_pose);
    void set_behavior_candidate(const double sim_time,
                                const TrajectoryPoint& planning_start_point,
                                BehaviorCandidate& behavior);

private:
    framework::Session* session_;
    double t_step_ = 0.2;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __REFERENCE_LINE_SELECTOR_H__ */