/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "reference_line_selector.h"

namespace arcsoft {
namespace planning {

ReferenceLineSelector::ReferenceLineSelector(framework::Session* session)
    : session_(session) {}

bool ReferenceLineSelector::Process() {
    // const auto& last_scenario_state =
    //     session_->scenario_state_info().target_scenario_state;
    // if (last_scenario_state == ScenarioStateEnum::CRUISE_KEEP) {
    //     if (!select_current_lane()) {
    //         AERROR << "Select current lane failed! ";
    //         return false;
    //     }
    // }
    if (!select_current_lane()) {
        AERROR << "Select current lane failed! ";
        return false;
    }

    return true;
}

void ReferenceLineSelector::filter_reference_lines() {
    auto& reference_lines = session_->mutable_reference_lines().MutableItems();

    for (size_t i = 0; i < reference_lines.size(); ++i) {
        // 计算自车在该条参考线上的frenet坐标
        auto* reference_line = reference_lines[i].get();
        if (reference_line == nullptr) {
            continue;
        }

        const auto* frenet_coord = reference_line->frenet_coord();
        if (frenet_coord == nullptr) {
            continue;
        }

        const auto& planning_start_point = session_->planning_start_point();
        SLPoint ego_frenet_sl;
        frenet_coord->CartCoord2FrenetCoord(
            {planning_start_point.x, planning_start_point.y}, ego_frenet_sl);
        reference_line->mutable_frenet_coord()->set_ego_frenet_sl(
            ego_frenet_sl);

        if (ego_frenet_sl.s < 0.0 ||
            ego_frenet_sl.s > reference_line->Length()) {
            reference_line->set_is_valid(false);
            continue;
        }
    }
}

bool ReferenceLineSelector::calculate_candidates_score(
    const std::shared_ptr<ReferenceLine>& last_current_lane,
    std::vector<std::shared_ptr<ReferenceLine>>& candidate_reference_lines) {
    constexpr double kConsistencyCost = 20.0;
    constexpr double kNaviCost = 100.0;
    constexpr double kMaxConsistencyDis = 5.0;
    constexpr double KComfortability = 400.0;

    uint32_t consistent_lane_id = 0;
    uint32_t best_lane_id = 0;
    double min_total_cost = std::numeric_limits<double>::max();
    double min_hausdorff_dis = std::numeric_limits<double>::max();

    for (auto& curr_reference_line : candidate_reference_lines) {
        double curr_total_cost = 0.0;
        double consistency_cost = 0.0;
        double hausdorff_dis = 0.0;

        // 1. consistency cost 历史帧不为空才有
        if (last_current_lane != nullptr &&
            last_current_lane->smoothed_points().size() > 0) {
            hausdorff_dis = curr_reference_line->CalculateHausdorffDistance(
                last_current_lane->smoothed_points());
            consistency_cost =
                kConsistencyCost * (hausdorff_dis / kMaxConsistencyDis);
        }

        curr_total_cost += consistency_cost;

        if (hausdorff_dis < min_hausdorff_dis) {
            consistent_lane_id = curr_reference_line->id();
            min_hausdorff_dis = hausdorff_dis;
        }

        // 2. navi cost
        if (!curr_reference_line->is_on_route()) {
            curr_total_cost += kNaviCost;
        }

        // 3. comfortability cost
        const double comfortability_cost =
            calculate_comfortability_cost(curr_reference_line.get()) *
            KComfortability;
        curr_total_cost += comfortability_cost;

        if (curr_total_cost < min_total_cost) {
            min_total_cost = curr_total_cost;
            best_lane_id = curr_reference_line->id();
        }

        AINFO << "Reference Line Selector - lane_id: "
              << curr_reference_line->id()
              << " Consistency_Cost: " << consistency_cost
              << " | Hausdorff_Dis: " << hausdorff_dis
              << " | Comfortability_cost: " << comfortability_cost
              << " | Is_On_Route: " << curr_reference_line->is_on_route()
              << " | Total_Cost: " << curr_total_cost;
    }

    AINFO << "best_lane_id: " << best_lane_id
          << " consistent_lane_id: " << consistent_lane_id
          << " min_hausdorff_dis: " << min_hausdorff_dis;

    if (best_lane_id == 0 || consistent_lane_id == 0) {
        return false;
    }

    session_->set_current_lane(
        session_->mutable_reference_lines().Find(best_lane_id));

    return true;
}

// [TODO] 选择current_lane，过滤异常的lane等等；
bool ReferenceLineSelector::select_current_lane() {
    if (session_ == nullptr) {
        return false;
    }

    const auto& reference_lines = session_->reference_lines().Dict();
    if (reference_lines.size() <= 0) {
        return false;
    }

    // 过滤在自车身后的，在自车前方的中心线
    filter_reference_lines();

    // 找出所有最小ego_frenet_l的reference lines
    std::vector<std::shared_ptr<ReferenceLine>> candidate_reference_lines;
    double min_frenet_l = std::numeric_limits<double>::max();
    double correspond_frenet_s = std::numeric_limits<double>::max();
    constexpr double kMinFrenetLatThreshold = 2.0;
    for (const auto& [id, reference_line] : reference_lines) {
        if (reference_line == nullptr || !reference_line->is_valid()) {
            continue;
        }

        const auto* frenet_coord = reference_line->frenet_coord();
        if (frenet_coord == nullptr) {
            continue;
        }

        const double ego_frenet_s = frenet_coord->ego_frenet_sl().s;
        const double ego_frenet_l = frenet_coord->ego_frenet_sl().l;
        if (ego_frenet_s < 0.0 || ego_frenet_l > kMinFrenetLatThreshold) {
            continue;
        }
        if (std::fabs(ego_frenet_l) < min_frenet_l) {
            min_frenet_l = std::fabs(ego_frenet_l);
            correspond_frenet_s = ego_frenet_s;
        }
    }

    if (min_frenet_l > kMinFrenetLatThreshold) {
        AERROR << "No Current Lane Found! ";
        return false;
    }

    AINFO << "min_frenet_l: " << min_frenet_l
          << " correspond_frenet_s: " << correspond_frenet_s;

    for (const auto& [id, reference_line] : reference_lines) {
        if (!reference_line->is_valid()) {
            continue;
        }

        const double ego_frenet_l =
            reference_line->frenet_coord()->ego_frenet_sl().l;
        AINFO << "reference_line_id: " << id
              << " ego_frenet_l: " << ego_frenet_l;
        if (std::fabs(std::fabs(ego_frenet_l) - min_frenet_l) < 1.0) {
            candidate_reference_lines.emplace_back(reference_line);
        }
    }

    if (candidate_reference_lines.empty()) {
        return false;
    }

    AINFO << "candidate_reference_lines.size(): "
          << candidate_reference_lines.size();

    // 若多条reference_line距离自车很近，选择离上一帧current_lane更近的
    const auto& last_current_lane = session_->current_lane();
    if (candidate_reference_lines.size() == 1) {
        session_->set_current_lane(candidate_reference_lines.front());
        return true;
    }

    // 计算多条current lane的score
    // consistency, navi
    if (!calculate_candidates_score(last_current_lane,
                                    candidate_reference_lines)) {
        AERROR << "calculate_candidates_score is failed! ";
        return false;
    }

    return true;
}

double ReferenceLineSelector::calculate_comfortability_cost(
    ReferenceLine* reference_line) {
    auto frenet_coord = reference_line->frenet_coord();
    const auto& planning_start_point = session_->planning_start_point();

    // 1. get ego state
    AgentPose ego_pose;
    set_ego_state(planning_start_point, frenet_coord, ego_pose);

    constexpr double total_sim_time = 4.0;

    // 2. set behavior candidate
    BehaviorCandidate behavior;
    set_behavior_candidate(total_sim_time, planning_start_point, behavior);

    // 3. set target_sl_list:此处只有一个目标点的sl
    TrajectoryPoints target_sl_list;
    TrajectoryPoint pt;
    pt.s = behavior.target_s;
    pt.l = behavior.target_l;
    target_sl_list.emplace_back(pt);

    TrajectoryPoints sim_traj{};
    if (!ForwardSimulation::Simulation(frenet_coord, ego_pose, total_sim_time,
                                       behavior.st_samples.v, target_sl_list,
                                       sim_traj)) {
        behavior.valid = false;
        AERROR << "Forward Simulation is failed! ";
        // 规划失败时代价为0，不再考虑舒适性代价
        return 0.0;
    }

    // DiscretePointsMath::ComputeInterpolateTrajectory(sim_traj);
    // for (size_t i = 0; i < sim_traj.size(); i++) {
    //     AINFO << "x: " << sim_traj[i].x << " y: " << sim_traj[i].y
    //           << " theta: " << sim_traj[i].heading
    //           << " kappa: " << sim_traj[i].curvature;
    // }
    auto& data_manager = session_->data_manager();
    data_manager.store_array(
        "forward_simulated_traj_" + std::to_string(reference_line->id()),
        sim_traj);

    const double max_kappa =
        DiscretePointsMath::ComputeTrajectoryMaxFabsKappa(sim_traj);
    const double mean_kappa =
        DiscretePointsMath::ComputeTrajectoryMeanFabsKappa(sim_traj);
    const double comfort_evaluation_value = max_kappa + mean_kappa;

    // AINFO << "max_kappa: " << max_kappa << " mean_kappa: " << mean_kappa;
    // AINFO << "comfort_evaluation_value: " << comfort_evaluation_value;

    return comfort_evaluation_value;
}

void ReferenceLineSelector::set_ego_state(
    const TrajectoryPoint& planning_start_point,
    const FrenetCoordinateSystem* frenet_coordinate, AgentPose& ego_pose) {
    // 1. set vehicle param
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();

    // 2. set ego state
    AINFO << "ego state x: " << planning_start_point.x
          << " y: " << planning_start_point.y
          << " heading: " << planning_start_point.heading
          << " curvature: " << planning_start_point.curvature
          << " v: " << planning_start_point.v
          << " a: " << planning_start_point.a;
    ego_pose.state.x = planning_start_point.x;
    ego_pose.state.y = planning_start_point.y;
    ego_pose.state.heading = planning_start_point.heading;
    ego_pose.state.curvature = planning_start_point.curvature;

    const double front_wheel_angle_init =
        std::atan(planning_start_point.curvature * vehicle_config.wheel_base);
    ego_pose.state.steer = front_wheel_angle_init;
    ego_pose.state.steer_rate = 0.0;
    ego_pose.state.v = planning_start_point.v;
    ego_pose.state.acc = planning_start_point.a;

    // 3. set ego param
    ego_pose.param.wheel_base = vehicle_config.wheel_base;
    ego_pose.param.preview_time = 2.0;
    ego_pose.param.look_ahead_distance_min = 3.0;

    constexpr double kVelocityLower = 18.0;
    constexpr double kVelocityUpper = 23.0;
    constexpr double kMaxPreviewDistanceLower = 10.0;
    constexpr double kMaxPreviewDistanceUpper = 20.0;
    constexpr double kGradient =
        (kMaxPreviewDistanceUpper - kMaxPreviewDistanceLower) /
        (kVelocityUpper - kVelocityLower);
    const double max_preview_distance =
        math::clamp(kGradient * (std::fabs(ego_pose.state.v) - kVelocityLower) +
                        kMaxPreviewDistanceLower,
                    kMaxPreviewDistanceLower, kMaxPreviewDistanceUpper);
    ego_pose.param.look_ahead_distance_max = max_preview_distance;
}

void ReferenceLineSelector::set_behavior_candidate(
    const double sim_time, const TrajectoryPoint& planning_start_point,
    BehaviorCandidate& behavior) {
    int kMaxSampleNum = static_cast<int>(sim_time / t_step_) + 1;
    behavior.target_speed = planning_start_point.v;
    behavior.target_l = 0.0;
    behavior.target_s = planning_start_point.v * sim_time;
    behavior.st_samples.v.resize(kMaxSampleNum, planning_start_point.v);
}

}  // namespace planning
}  // namespace arcsoft