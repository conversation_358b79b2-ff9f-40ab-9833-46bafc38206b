/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __REFERENCE_LINE_MANAGER_H__
#define __REFERENCE_LINE_MANAGER_H__

#include "common/basic_types.h"
#include "framework/session.h"
#include "reference_line_selector/reference_line_selector.h"
#include "reference_line_smoother/reference_line_smoother.h"

namespace arcsoft {
namespace planning {

class ReferenceLineManager {
public:
    explicit ReferenceLineManager(framework::Session* session);
    ~ReferenceLineManager() = default;

    bool Process();

private:
    void scenario_recognize(ScenarioType& scenario_type);

    framework::Session* session_;
    ReferenceLineSmoother reference_line_smoother_;
    ReferenceLineSelector reference_line_selector_;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __REFERENCE_LINE_MANAGER_H__ */