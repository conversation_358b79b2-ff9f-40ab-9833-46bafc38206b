/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "reference_line_smoother.h"

#include "fem_pos_deviation_smoother.h"

namespace arcsoft {
namespace planning {

ReferenceLineSmoother::ReferenceLineSmoother(framework::Session* session)
    : session_(session), smooth_method_(1), is_offline_(0) {}

bool ReferenceLineSmoother::Process() {
    auto& reference_lines = session_->mutable_reference_lines().MutableItems();
    AINFO << "num_reference_lines: " << reference_lines.size();

    constexpr size_t kMinRawPointsSize = 3;
    bool smooth_status = false;
    for (size_t i = 0; i < reference_lines.size(); ++i) {
        auto* reference_line = reference_lines[i].get();
        const auto& raw_points = reference_line->raw_points();
        // AINFO << "reference_line index: " << i
        //       << " raw_points.size: " << raw_points.size();
        if (raw_points.size() <= kMinRawPointsSize) {
            reference_line->set_is_valid(false);
            continue;
        }

        // 对raw_points进行插值采样
        if (!reference_line->SampleRawPoints()) {
            AERROR << "reference_line id: " << reference_line->id()
                   << " sample raw points failed! ";
            continue;
        }
        const auto& sampled_raw_points = reference_line->sampled_raw_points();
        if (sampled_raw_points.size() <= kMinRawPointsSize) {
            continue;
        }

        // 后续计算都基于sampled_raw_points;
        std::vector<Point2D> sampled_raw_points_2d;
        std::vector<ReferenceLinePoint> smoothed_points;

        sampled_raw_points_2d.resize(sampled_raw_points.size());
        smoothed_points.resize(sampled_raw_points.size());

        for (size_t j = 0; j < sampled_raw_points.size(); j++) {
            sampled_raw_points_2d[j].x = sampled_raw_points[j].x;
            sampled_raw_points_2d[j].y = sampled_raw_points[j].y;
        }

        // AINFO << "Sampled raw points size: " << sampled_raw_points.size();

        std::vector<Point2D> smoothed_points_2d;
        if (!Smooth(sampled_raw_points_2d, smoothed_points_2d)) {
            AINFO << "reference_line index: " << i << " smoothed failed! ";
            reference_line->set_is_valid(false);
            continue;
        }

        for (size_t j = 0; j < smoothed_points.size(); j++) {
            smoothed_points[j].x = smoothed_points_2d[j].x;
            smoothed_points[j].y = smoothed_points_2d[j].y;
        }

        if (!DiscretePointsMath::ComputePathProfile(smoothed_points)) {
            AINFO << "reference_line index: " << i
                  << " ComputePathProfile failed! ";
            reference_line->set_is_valid(false);
            continue;
        }

        // AINFO << "smoothed_points.size: " << smoothed_points.size();

        // 设置平滑参考线及frenet坐标系
        if (!UpdateSmoothPointsAttribute(sampled_raw_points, smoothed_points)) {
            AINFO << "reference_line index: " << i
                  << " UpdateSmoothPointsAttribute failed! ";
            reference_line->set_is_valid(false);
            continue;
        }

        reference_line->set_smoothed_points(smoothed_points);

        auto& data_manager = session_->data_manager();
        data_manager.store_array("reference_line_" + std::to_string(i),
                                 reference_line->smoothed_points());

        // AINFO << "reference_line index: " << i
        //       << " id: " << reference_line->id() << " smoothed succeed! ";

        reference_line->set_is_valid(true);

        smooth_status = true;
    }

    // auto& data_manager = session_->data_manager();
    // data_manager.store_array("reference_line_", reference_lines);

    return smooth_status;
}

bool ReferenceLineSmoother::Smooth(
    const std::vector<Point2D>& sampled_raw_points_2d,
    std::vector<Point2D>& smoothed_points_2d) {
    bool smooth_status = false;
    if (1 == smooth_method_) {
        std::vector<double> road_bounds(sampled_raw_points_2d.size(), 0);

        FemPosDeviationSmoother smoother;
        smooth_status = smoother.solve(sampled_raw_points_2d,
                                       smoothed_points_2d, road_bounds);
    }

    return smooth_status;
}

double ReferenceLineSmoother::ComputeLateralOffset(double x_ref, double y_ref,
                                                   double heading_ref,
                                                   double x_smooth,
                                                   double y_smooth) {
    arcsoft::common::Vec2d p_ref(x_ref, y_ref);
    arcsoft::common::Vec2d p_smooth(x_smooth, y_smooth);

    arcsoft::common::Vec2d offset_vec = p_smooth - p_ref;

    // 构造左侧方向的单位法向量
    arcsoft::common::Vec2d normal_vec(-std::sin(heading_ref),
                                      std::cos(heading_ref));

    // 计算偏移量，单位为米
    double lateral_offset = offset_vec.InnerProd(normal_vec);

    // AINFO << "Lateral offset: " << lateral_offset;

    return lateral_offset;
}

bool ReferenceLineSmoother::UpdateSmoothPointsAttribute(
    const std::vector<ReferenceLinePoint>& sampled_raw_points,
    std::vector<ReferenceLinePoint>& smoothed_points) {
    // 计算平滑后沿法线方向的偏移量，更新距离左右边界的距离
    for (size_t i = 0; i < smoothed_points.size(); ++i) {
        double x_ref = sampled_raw_points[i].x;
        double y_ref = sampled_raw_points[i].y;
        double heading_ref = sampled_raw_points[i].heading;

        double x_smooth = smoothed_points[i].x;
        double y_smooth = smoothed_points[i].y;

        // 计算平滑后沿法线方向的偏移量,带正负号
        double lateral_offset =
            ComputeLateralOffset(x_ref, y_ref, heading_ref, x_smooth, y_smooth);

        // 左侧更新
        smoothed_points[i].left_divider_id =
            sampled_raw_points[i].left_divider_id;
        smoothed_points[i].left_divider_type =
            sampled_raw_points[i].left_divider_type;
        smoothed_points[i].distance_to_left_divider =
            sampled_raw_points[i].distance_to_left_divider - lateral_offset;

        if (smoothed_points[i].distance_to_left_divider < 0) {
            AWARN << "after smooth left divider distance < 0";
            smoothed_points[i].distance_to_left_divider = 0;
        }

        smoothed_points[i].left_boundary_id =
            sampled_raw_points[i].left_boundary_id;
        smoothed_points[i].left_boundary_type =
            sampled_raw_points[i].left_boundary_type;
        smoothed_points[i].distance_to_left_boundary =
            sampled_raw_points[i].distance_to_left_boundary - lateral_offset;

        if (smoothed_points[i].distance_to_left_boundary < 0) {
            smoothed_points[i].distance_to_left_boundary = 0;
            AWARN << "after smooth left boundary distance < 0";
        }

        // 右侧更新
        smoothed_points[i].right_divider_id =
            sampled_raw_points[i].right_divider_id;
        smoothed_points[i].right_divider_type =
            sampled_raw_points[i].right_divider_type;
        smoothed_points[i].distance_to_right_divider =
            sampled_raw_points[i].distance_to_right_divider + lateral_offset;
        if (smoothed_points[i].distance_to_right_divider < 0) {
            AWARN << "after smooth right divider distance < 0";
            smoothed_points[i].distance_to_right_divider = 0;
        }

        smoothed_points[i].right_boundary_id =
            sampled_raw_points[i].right_boundary_id;
        smoothed_points[i].right_boundary_type =
            sampled_raw_points[i].right_boundary_type;
        smoothed_points[i].distance_to_right_boundary =
            sampled_raw_points[i].distance_to_right_boundary + lateral_offset;
        if (smoothed_points[i].distance_to_right_boundary < 0) {
            smoothed_points[i].distance_to_right_boundary = 0;
            AWARN << "after smooth right boundary distance < 0";
        }
    }
    return true;
}

}  // namespace planning
}  // namespace arcsoft
