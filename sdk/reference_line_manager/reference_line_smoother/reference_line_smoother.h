/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __REFERENCE_LINE_SMOOTHER_H__
#define __REFERENCE_LINE_SMOOTHER_H__

#include "common/discrete_points_math.h"
#include "common/log.h"
#include "common/vec2d.h"
#include "framework/session.h"
#include "utils_smooth.hpp"

namespace arcsoft {
namespace planning {

class ReferenceLineSmoother {
public:
    explicit ReferenceLineSmoother(framework::Session* session);
    ~ReferenceLineSmoother() = default;

    bool Process();

private:
    bool Smooth(const std::vector<Point2D>& raw_points_2d,
                std::vector<Point2D>& smoothed_points_2d);

    bool SelectCurrentLane();
    double ComputeLateralOffset(double x_ref, double y_ref, double heading_ref,
                                double x_smooth, double y_smooth);
    bool UpdateSmoothPointsAttribute(
        const std::vector<ReferenceLinePoint>& sampled_raw_points,
        std::vector<ReferenceLinePoint>& smoothed_points);

    framework::Session* session_;
    int smooth_method_;
    bool is_offline_;
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __REFERENCE_LINE_SMOOTHER_H__ */
