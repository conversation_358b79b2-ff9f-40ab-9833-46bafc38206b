#pragma once

#include <stdlib.h>
#include <sys/stat.h>
#include <sys/types.h>

#include <string>

#include "common/basic_types.h"
#include "fem_pos_deviation_osqp_interface.h"
#include "fem_pos_deviation_sqp_osqp_interface.h"

namespace arcsoft {
namespace planning {

class FemPosDeviationSmoother {
public:
    FemPosDeviationSmoother() {
        is_using_sqp_ = false;

        weight_fem_pos_deviation_ = 100000.0;
        weight_path_length_ = 1.0;
        weight_ref_deviation_ = 100.0;
        weight_curvature_constraint_slack_var_ = 0.1;
        curvature_constraint_ = 0.18;
        x_buffer_ = 0.5;
        y_buffer_ = 0.5;
    }
    virtual ~FemPosDeviationSmoother(){};

    bool solve(const std::vector<arcsoft::planning::Point2D>& raw_points,
               std::vector<arcsoft::planning::Point2D>& smoothed_points,
               std::vector<double>& bounds);

private:
    double weight_fem_pos_deviation_;
    double weight_path_length_;
    double weight_ref_deviation_;
    double weight_curvature_constraint_slack_var_;
    double curvature_constraint_;

    double x_buffer_;
    double y_buffer_;
    bool is_using_sqp_;
};

}  // namespace planning
}  // namespace arcsoft
