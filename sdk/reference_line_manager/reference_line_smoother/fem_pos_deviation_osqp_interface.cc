#include "fem_pos_deviation_osqp_interface.h"

#include <limits>

namespace arcsoft {
namespace planning {

constexpr size_t kMinSmoothedPointSize = 6;

bool FemPosDeviationOsqpInterface::Solve() {
    if (ref_points_.empty()) {
        AINFO << "reference points is empty! Please Check!";
        return false;
    }

    if (ref_points_.size() != bounds_around_refs_.size()) {
        AINFO << "ref_points and bounds size not equal! Please Check!";
        return false;
    }

    if (ref_points_.size() < kMinSmoothedPointSize) {
        AINFO << "ref_points size smaller than 6! Please Check!";
        return false;
    }

    if (ref_points_.size() >
        static_cast<size_t>(std::numeric_limits<int>::max())) {
        AINFO << "ref_points size too large! Please Check!";
        return false;
    }

    num_of_points_ = static_cast<int>(ref_points_.size());
    num_of_variables_ = num_of_points_ * 2;
    num_of_constraints_ = num_of_variables_;

    std::vector<c_float> P_data;
    std::vector<c_int> P_indices;
    std::vector<c_int> P_indptr;
    CalculateKernel(&P_data, &P_indices, &P_indptr);

    std::vector<c_float> A_data;
    std::vector<c_int> A_indices;
    std::vector<c_int> A_indptr;
    std::vector<c_float> lower_bounds;
    std::vector<c_float> upper_bounds;
    CalculateAffineConstraint(&A_data, &A_indices, &A_indptr, &lower_bounds,
                              &upper_bounds);

    std::vector<c_float> q;
    CalculateOffset(&q);

    std::vector<c_float> primal_warm_start;
    SetPrimalWarmStart(&primal_warm_start);

    OSQPData* data = reinterpret_cast<OSQPData*>(c_malloc(sizeof(OSQPData)));
    OSQPSettings* settings =
        reinterpret_cast<OSQPSettings*>(c_malloc(sizeof(OSQPSettings)));

    osqp_set_default_settings(settings);
    settings->max_iter = max_iter_;
    settings->time_limit = time_limit_;
    settings->verbose = verbose_;
    settings->scaled_termination = scaled_termination_;
    settings->warm_start = warm_start_;

    OSQPWorkspace* work = nullptr;

    bool res = OptimizeWithOsqp(num_of_variables_, lower_bounds.size(), &P_data,
                                &P_indices, &P_indptr, &A_data, &A_indices,
                                &A_indptr, &lower_bounds, &upper_bounds, &q,
                                &primal_warm_start, data, &work, settings);
    if (res == false || work == nullptr || work->solution == nullptr) {
        AINFO << "Failed to find solution.";
        osqp_cleanup(work);
        c_free(data->A);
        c_free(data->P);
        c_free(data);
        c_free(settings);

        return false;
    }

    x_.resize(num_of_points_);
    y_.resize(num_of_points_);
    for (int i = 0; i < num_of_points_; ++i) {
        int index = i * 2;
        x_.at(i) = work->solution->x[index];
        y_.at(i) = work->solution->x[index + 1];
    }

    osqp_cleanup(work);
    c_free(data->A);
    c_free(data->P);
    c_free(data);
    c_free(settings);

    return true;
}

void FemPosDeviationOsqpInterface::CalculateKernel(
    std::vector<c_float>* P_data, std::vector<c_int>* P_indices,
    std::vector<c_int>* P_indptr) {
    // CHECK_GT(num_of_variables_, 4);

    // Three quadratic penalties are involved:
    // 1. Penalty x on distance between middle point and point by finite element
    // estimate;
    // 2. Penalty y on path length;
    // 3. Penalty z on difference between points and reference points

    // General formulation of P matrix is as below(with 6 points as an example):
    // I is a two by two identity matrix, X, Y, Z represents x * I, y * I, z * I
    // 0 is a two by two zero matrix
    // |X+Y+Z, -2X-Y,   X,       0,       0,       0    |
    // |0,     5X+2Y+Z, -4X-Y,   X,       0,       0    |
    // |0,     0,       6X+2Y+Z, -4X-Y,   X,       0    |
    // |0,     0,       0,       6X+2Y+Z, -4X-Y,   X    |
    // |0,     0,       0,       0,       5X+2Y+Z, -2X-Y|
    // |0,     0,       0,       0,       0,       X+Y+Z|

    std::vector<std::vector<std::pair<c_int, c_float>>> columns;
    columns.resize(num_of_variables_);
    int col_num = 0;

    for (int col = 0; col < 2; ++col) {
        columns[col].emplace_back(col, weight_fem_pos_deviation_ +
                                           weight_path_length_ +
                                           weight_ref_deviation_);
        ++col_num;
    }

    for (int col = 2; col < 4; ++col) {
        columns[col].emplace_back(
            col - 2, -2.0 * weight_fem_pos_deviation_ - weight_path_length_);
        columns[col].emplace_back(col, 5.0 * weight_fem_pos_deviation_ +
                                           2.0 * weight_path_length_ +
                                           weight_ref_deviation_);
        ++col_num;
    }

    int second_point_from_last_index = num_of_points_ - 2;
    for (int point_index = 2; point_index < second_point_from_last_index;
         ++point_index) {
        int col_index = point_index * 2;
        for (int col = 0; col < 2; ++col) {
            col_index += col;
            columns[col_index].emplace_back(col_index - 4,
                                            weight_fem_pos_deviation_);
            columns[col_index].emplace_back(
                col_index - 2,
                -4.0 * weight_fem_pos_deviation_ - weight_path_length_);
            columns[col_index].emplace_back(col_index,
                                            6.0 * weight_fem_pos_deviation_ +
                                                2.0 * weight_path_length_ +
                                                weight_ref_deviation_);
            ++col_num;
        }
    }

    int second_point_col_from_last_col = num_of_variables_ - 4;
    int last_point_col_from_last_col = num_of_variables_ - 2;
    for (int col = second_point_col_from_last_col;
         col < last_point_col_from_last_col; ++col) {
        columns[col].emplace_back(col - 4, weight_fem_pos_deviation_);
        columns[col].emplace_back(
            col - 2, -4.0 * weight_fem_pos_deviation_ - weight_path_length_);
        columns[col].emplace_back(col, 5.0 * weight_fem_pos_deviation_ +
                                           2.0 * weight_path_length_ +
                                           weight_ref_deviation_);
        ++col_num;
    }

    for (int col = last_point_col_from_last_col; col < num_of_variables_;
         ++col) {
        columns[col].emplace_back(col - 4, weight_fem_pos_deviation_);
        columns[col].emplace_back(
            col - 2, -2.0 * weight_fem_pos_deviation_ - weight_path_length_);
        columns[col].emplace_back(col, weight_fem_pos_deviation_ +
                                           weight_path_length_ +
                                           weight_ref_deviation_);
        ++col_num;
    }

    int ind_p = 0;
    for (int i = 0; i < col_num; ++i) {
        P_indptr->push_back(ind_p);
        for (const auto& row_data_pair : columns[i]) {
            // Rescale by 2.0 as the quadratic term in osqp default qp problem
            // setup is set as (1/2) * x' * P * x
            P_data->push_back(row_data_pair.second * 2.0);
            P_indices->push_back(row_data_pair.first);
            ++ind_p;
        }
    }
    P_indptr->push_back(ind_p);
}

void FemPosDeviationOsqpInterface::CalculateOffset(std::vector<c_float>* q) {
    for (int i = 0; i < num_of_points_; ++i) {
        const auto& ref_point_xy = ref_points_[i];
        q->push_back(-2.0 * weight_ref_deviation_ * ref_point_xy.first);
        q->push_back(-2.0 * weight_ref_deviation_ * ref_point_xy.second);
    }
}

void FemPosDeviationOsqpInterface::CalculateAffineConstraint(
    std::vector<c_float>* A_data, std::vector<c_int>* A_indices,
    std::vector<c_int>* A_indptr, std::vector<c_float>* lower_bounds,
    std::vector<c_float>* upper_bounds) {
    int ind_A = 0;
    for (int i = 0; i < num_of_variables_; ++i) {
        A_data->push_back(1.0);
        A_indices->push_back(i);
        A_indptr->push_back(ind_A);
        ++ind_A;
    }
    A_indptr->push_back(ind_A);

    for (int i = 0; i < num_of_points_; ++i) {
        const auto& ref_point_xy = ref_points_[i];

        upper_bounds->push_back(ref_point_xy.first + bounds_around_refs_[i]);
        upper_bounds->push_back(ref_point_xy.second + bounds_around_refs_[i]);
        lower_bounds->push_back(ref_point_xy.first - bounds_around_refs_[i]);
        lower_bounds->push_back(ref_point_xy.second - bounds_around_refs_[i]);
    }
}

void FemPosDeviationOsqpInterface::SetPrimalWarmStart(
    std::vector<c_float>* primal_warm_start) {
    for (const auto& ref_point_xy : ref_points_) {
        primal_warm_start->push_back(ref_point_xy.first);
        primal_warm_start->push_back(ref_point_xy.second);
    }
}

bool FemPosDeviationOsqpInterface::OptimizeWithOsqp(
    const size_t kernel_dim, const size_t num_affine_constraint,
    std::vector<c_float>* P_data, std::vector<c_int>* P_indices,
    std::vector<c_int>* P_indptr, std::vector<c_float>* A_data,
    std::vector<c_int>* A_indices, std::vector<c_int>* A_indptr,
    std::vector<c_float>* lower_bounds, std::vector<c_float>* upper_bounds,
    std::vector<c_float>* q, std::vector<c_float>* primal_warm_start,
    OSQPData* data, OSQPWorkspace** work, OSQPSettings* settings) {
    data->n = kernel_dim;
    data->m = num_affine_constraint;
    data->P = csc_matrix(data->n, data->n, P_data->size(), P_data->data(),
                         P_indices->data(), P_indptr->data());
    data->q = q->data();
    data->A = csc_matrix(data->m, data->n, A_data->size(), A_data->data(),
                         A_indices->data(), A_indptr->data());
    data->l = lower_bounds->data();
    data->u = upper_bounds->data();

    *work = osqp_setup(data, settings);
    // osqp_setup(work, data, settings);

    osqp_warm_start_x(*work, primal_warm_start->data());

    // Solve Problem
    osqp_solve(*work);

    auto status = (*work)->info->status_val;

    if (status < 0) {
        AINFO << "failed optimization status:\t" << (*work)->info->status;
        return false;
    }

    if (status != 1 && status != 2) {
        AINFO << "failed optimization status:\t" << (*work)->info->status;
        return false;
    }

    return true;
}

}  // namespace planning
}  // namespace arcsoft
