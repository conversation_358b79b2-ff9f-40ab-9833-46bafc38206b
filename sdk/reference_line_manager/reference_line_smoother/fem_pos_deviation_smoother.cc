#include "fem_pos_deviation_smoother.h"

namespace arcsoft {
namespace planning {

bool FemPosDeviationSmoother::solve(
    const std::vector<arcsoft::planning::Point2D>& raw_points,
    std::vector<arcsoft::planning::Point2D>& smoothed_points,
    std::vector<double>& bounds) {
    size_t path_size = raw_points.size();
    smoothed_points.clear();
    smoothed_points.resize(path_size);

    std::fill(bounds.begin(), bounds.end(), x_buffer_);
    bounds.front() = 0;
    bounds.back() = 0;

    if (!is_using_sqp_) {
        std::vector<std::pair<double, double>> raw_point2d(path_size,
                                                           {0.0, 0.0});
        for (size_t i = 0; i < path_size; i++) {
            raw_point2d[i].first = raw_points[i].x;
            raw_point2d[i].second = raw_points[i].y;
        }

        FemPosDeviationOsqpInterface osqp_solver;
        osqp_solver.set_ref_points(raw_point2d);
        osqp_solver.set_bounds_around_refs(bounds);
        osqp_solver.set_weight_fem_pos_deviation(weight_fem_pos_deviation_);
        osqp_solver.set_weight_path_length(weight_path_length_);
        osqp_solver.set_weight_ref_deviation(weight_ref_deviation_);

        if (osqp_solver.Solve()) {
            auto& opt_x = osqp_solver.opt_x();
            auto& opt_y = osqp_solver.opt_y();
            for (size_t i = 0; i < path_size; i++) {
                smoothed_points[i].x = opt_x[i];
                smoothed_points[i].y = opt_y[i];
            }
        } else {
            return false;
        }
    } else {
        std::vector<std::pair<double, double>> raw_point2d(path_size,
                                                           {0.0, 0.0});
        for (size_t i = 0; i < path_size; i++) {
            raw_point2d[i].first = raw_points[i].x;
            raw_point2d[i].second = raw_points[i].y;
        }

        FemPosDeviationSqpOsqpInterface sqp_osqp_solver;
        sqp_osqp_solver.set_ref_points(raw_point2d);
        sqp_osqp_solver.set_bounds_around_refs(bounds);
        sqp_osqp_solver.set_weight_fem_pos_deviation(weight_fem_pos_deviation_);
        sqp_osqp_solver.set_weight_path_length(weight_path_length_);
        sqp_osqp_solver.set_weight_ref_deviation(weight_ref_deviation_);
        sqp_osqp_solver.set_weight_curvature_constraint_slack_var(
            weight_curvature_constraint_slack_var_);
        sqp_osqp_solver.set_curvature_constraint(curvature_constraint_);

        if (sqp_osqp_solver.Solve()) {
            auto& opt_xy = sqp_osqp_solver.opt_xy();

            for (size_t i = 0; i < path_size; i++) {
                smoothed_points[i].x = opt_xy[i].first;
                smoothed_points[i].y = opt_xy[i].second;
            }
        } else {
            return false;
        }
    }

    return true;
}

}  // namespace planning
}  // namespace arcsoft
