#pragma once
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstring>
#include <fstream>
#include <iostream>
#include <numeric>
#include <string>
#include <vector>

#include "common/basic_types.h"

static bool loadPathPoints(std::string file_path,
                           std::vector<arcsoft::planning::Point2D>& points) {
    std::ifstream in_file(file_path.c_str());
    if (!in_file.is_open()) {
        printf("open %s failed\n", file_path.c_str());
        return false;
    }
    arcsoft::planning::Point2D point;
    std::string line;

    while (in_file.good()) {
        getline(in_file, line);
        std::stringstream ss(line);
        ss >> point.x >> point.y;
        points.push_back(point);
    }
    points.pop_back();
    in_file.close();
    return true;
}

static void dumpPath(
    const char* name,
    const std::vector<arcsoft::planning::ReferenceLinePoint>& path) {
    FILE* fp;
    fp = fopen(name, "w");
    size_t size = path.size();

    for (size_t i = 0; i < size; i++) {
        fprintf(fp, "%f\t\t\t%f\t\t\t%f\t\t%f\t\t%f\t\t%f\n", path[i].x,
                path[i].y, path[i].heading, path[i].s, path[i].curvature,
                path[i].dcurvature);
        fflush(fp);
    }
    fclose(fp);
}
