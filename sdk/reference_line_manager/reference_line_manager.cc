/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "reference_line_manager.h"

namespace arcsoft {
namespace planning {

ReferenceLineManager::ReferenceLineManager(framework::Session* session)
    : session_(session)
    , reference_line_smoother_(session)
    , reference_line_selector_(session) {}

bool ReferenceLineManager::Process() {
    if (!reference_line_smoother_.Process()) {
        AERROR << "Reference Line Smoother Failed! ";
        return false;
    }

    if (!reference_line_selector_.Process()) {
        AERROR << "Reference Line Selector Failed! ";
        return false;
    }

    // 车道数场景识别
    ScenarioType scenario_type = ScenarioType::UNKNOWN;
    scenario_recognize(scenario_type);
    session_->set_scenario_type(scenario_type);

    return true;
}

void ReferenceLineManager::scenario_recognize(ScenarioType& scenario_type) {
    const auto& current_lane = session_->current_lane();
    if (current_lane == nullptr) {
        scenario_type = ScenarioType::UNKNOWN;
        return;
    }

    size_t lane_count = 1;  // 当前车道

    const auto& reference_lines = session_->reference_lines();

    // 统计左侧车道数量
    auto left_lane = current_lane;
    while (left_lane != nullptr && left_lane->left_lane_id() != 0) {
        lane_count++;
        uint32_t left_lane_id = left_lane->left_lane_id();
        left_lane = reference_lines.Find(left_lane_id);
    }

    // 统计右侧车道数量
    auto right_lane = current_lane;
    while (right_lane != nullptr && right_lane->right_lane_id() != 0) {
        lane_count++;
        uint32_t right_lane_id = right_lane->right_lane_id();
        right_lane = reference_lines.Find(right_lane_id);
    }

    AINFO << "[Scenario Type] lane_count: " << lane_count;

    if (lane_count == 1) {
        scenario_type = ScenarioType::SINGLE_LANE;
    } else if (lane_count == 2) {
        scenario_type = ScenarioType::TWO_LANE_ROAD;
    } else if (lane_count >= 3) {
        scenario_type = ScenarioType::THREE_LANE_ROAD;
    } else {
        scenario_type = ScenarioType::UNKNOWN;
    }
}

}  // namespace planning

}  // namespace arcsoft
