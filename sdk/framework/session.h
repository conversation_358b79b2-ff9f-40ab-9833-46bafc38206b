/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __SESSION_H__
#define __SESSION_H__

#include <iostream>

#include "common/frenet_coordinate_system.h"
#include "common/local_view.h"
#include "common/math/index_list.h"
#include "common/obstacle.h"
#include "common/reference_line.h"
#include "common/speed/speed_data.h"
#include "common/speed/st_graph_data.h"
#include "utils/data_manager.h"

namespace arcsoft {
namespace framework {

using namespace planning;

class Session {
public:
    explicit Session(const std::string& config_path = "../data/data.json");
    ~Session() = default;

    const bool is_simulation_mode() { return is_simulation_mode_; }

    const math::IndexedList<uint32_t, ReferenceLine>& reference_lines() const {
        return reference_lines_;
    }
    math::IndexedList<uint32_t, ReferenceLine>& mutable_reference_lines() {
        return reference_lines_;
    }

    void set_scenario_type(const ScenarioType& scenario_type) {
        scenario_type_ = scenario_type;
    }
    const ScenarioType& scenario_type() { return scenario_type_; }

    const common::LocalView& local_view() const { return local_view_; }
    common::LocalView& mutable_local_view() { return local_view_; }

    utils::DataManager& data_manager() { return data_manager_; }

    const TrajectoryPoint& planning_start_point() const {
        return planning_start_point_;
    }
    void set_planning_start_point(const TrajectoryPoint& planning_start_point) {
        planning_start_point_ = planning_start_point;
    }

    const int best_transition_context_id() const {
        return best_transition_context_id_;
    }
    void set_best_transition_context_id(const int best_transition_context_id) {
        best_transition_context_id_ = best_transition_context_id;
    }

    StateTransitionContexts& mutable_transition_contexts() {
        return transition_contexts_;
    }
    const StateTransitionContexts& transition_contexts() const {
        return transition_contexts_;
    }
    const StateTransitionContext& candidate_transition_context(
        const size_t candidate_transition_context_id) const {
        return transition_contexts_[candidate_transition_context_id];
    }

    const double min_trajectory_cost() const { return min_trajectory_cost_; }
    void set_min_trajectory_cost(double min_trajectory_cost) {
        min_trajectory_cost_ = min_trajectory_cost;
    }

    std::vector<BehaviorCandidate>& mutable_behavior_candidates() {
        return behavior_candidates_;
    }
    const std::vector<BehaviorCandidate>& behavior_candidates() const {
        return behavior_candidates_;
    }

    const std::shared_ptr<ReferenceLine>& target_lane() const {
        return target_lane_;
    }
    const std::shared_ptr<ReferenceLine>& current_lane() const {
        return current_lane_;
    }
    const std::shared_ptr<ReferenceLine>& original_lane() const {
        return original_lane_;
    }

    void set_current_lane(const std::shared_ptr<ReferenceLine>& current_lane) {
        current_lane_ = current_lane;
    }
    void set_target_lane(const std::shared_ptr<ReferenceLine>& target_lane) {
        target_lane_ = target_lane;
    }
    void set_original_lane(
        const std::shared_ptr<ReferenceLine>& original_lane) {
        original_lane_ = original_lane;
    }

    void set_path_data_map(
        const std::unordered_map<size_t, std::vector<PathData>> path_data_map) {
        path_data_map_ = path_data_map;
    }
    const std::vector<PathData>* path_data_vec(
        const size_t candidate_transition_context_id) const {
        auto path_data_it =
            path_data_map_.find(candidate_transition_context_id);
        if (path_data_it != path_data_map_.end()) {
            return &path_data_it->second;
        }
        return nullptr;
    }

    const math::IndexedList<int, Obstacle>& obstacles() const {
        return obstacles_;
    }

    math::IndexedList<int, Obstacle>& mutable_obstacles() { return obstacles_; }

    const ManualRequestInfo& manual_request_info() const {
        return manual_request_info_;
    }
    ManualRequestInfo& mutable_manual_request_info() {
        return manual_request_info_;
    }

    const LaneChangeDecision& best_trigger_decision() const {
        return best_trigger_decision_;
    }
    void set_best_trigger_decision(
        const LaneChangeDecision& best_trigger_decision) {
        best_trigger_decision_ = best_trigger_decision;
    }

    const bool has_reached_target_lane() const {
        return has_reached_target_lane_;
    }
    void set_has_reached_target_lane(const bool has_reached_target_lane) {
        has_reached_target_lane_ = has_reached_target_lane;
    }

    const ScenarioStateInfo& scenario_state_info() const {
        return scenario_state_info_;
    }
    ScenarioStateInfo& mutable_scenario_state_info() {
        return scenario_state_info_;
    }

    const LaneChangeStatus& lane_change_status() const {
        return lane_change_status_;
    }
    LaneChangeStatus& mutable_lane_change_status() {
        return lane_change_status_;
    }

    const std::vector<StGraphData>* st_graph_data_vec(
        const size_t candidate_transition_context_id) const {
        auto st_graph_data_it =
            st_graph_data_map_.find(candidate_transition_context_id);
        if (st_graph_data_it != st_graph_data_map_.end()) {
            return &st_graph_data_it->second;
        }
        return nullptr;
    }
    std::unordered_map<size_t, std::vector<StGraphData>>&
    mutable_st_graph_data_map() {
        return st_graph_data_map_;
    }

    std::unordered_map<size_t, std::vector<SpeedData>>&
    mutable_speed_data_map() {
        return speed_data_map_;
    }
    const std::vector<SpeedData>* speed_data_vec(
        const size_t candidate_transition_context_id) const {
        auto speed_data_it =
            speed_data_map_.find(candidate_transition_context_id);
        if (speed_data_it != speed_data_map_.end()) {
            return &speed_data_it->second;
        }
        return nullptr;
    }

    void append_stitching_trajectory(TrajectoryPoints& trajectory) {
        if (trajectory.empty()) {
            return;
        }
        trajectory.pop_back();  // 移除最后一个点，避免重复
        // 将 trajectory 的所有元素插入到 best_trajectory_result_ 的开头
        best_trajectory_result_.insert(best_trajectory_result_.begin(),
                                       trajectory.begin(), trajectory.end());

        for (auto it = trajectory.rbegin(); it != trajectory.rend(); ++it) {
            const auto& tp = *it;
            PathPoint pp;
            pp.x = tp.x;
            pp.y = tp.y;
            pp.heading = tp.heading;
            pp.curvature = tp.curvature;
            pp.s = tp.s;
            pp.dcurvature = 0.0;   // 默认值或计算
            pp.ddcurvature = 0.0;  // 默认值或计算
            best_path_result_.insert(best_path_result_.begin(), pp);
        }
    }

    void set_best_trajectory_result(
        const TrajectoryPoints& best_trajectory_result) {
        best_trajectory_result_ = best_trajectory_result;
    }
    const TrajectoryPoints& best_trajectory_result() const {
        return best_trajectory_result_;
    }

    void set_planning_status(const PlanningStatus& planning_status) {
        planning_status_ = planning_status;
    }
    const PlanningStatus& planning_status() const { return planning_status_; }

    void set_auto_drive_mode_state(
        const common::AutoDriveModeState& auto_drive_mode_state) {
        auto_drive_mode_state_ = auto_drive_mode_state;
    }
    const common::AutoDriveModeState& auto_drive_mode_state() const {
        return auto_drive_mode_state_;
    }

    void set_best_path_result(const PathData& best_path_result) {
        best_path_result_ = best_path_result;
    }
    const PathData& best_path_result() const { return best_path_result_; }

    std::unordered_map<size_t, std::vector<SpeedData>>&
    mutable_speed_data_optimizer_map() {
        return speed_data_optimizer_map_;
    }
    const std::vector<SpeedData>* speed_data_optimizer_vec(
        const size_t candidate_transition_context_id) const {
        auto speed_data_optimizer_it =
            speed_data_optimizer_map_.find(candidate_transition_context_id);
        if (speed_data_optimizer_it != speed_data_optimizer_map_.end()) {
            return &speed_data_optimizer_it->second;
        }
        return nullptr;
    }

    // [temp] variable
    int cnt_ = 0;

private:
    bool is_simulation_mode_ = false;
    math::IndexedList<uint32_t, ReferenceLine> reference_lines_;
    common::LocalView local_view_{};
    utils::DataManager data_manager_;
    TrajectoryPoint planning_start_point_{};
    ScenarioType scenario_type_ = ScenarioType::UNKNOWN;

    //
    math::IndexedList<int, Obstacle> obstacles_;

    // 变道决策
    ManualRequestInfo manual_request_info_;
    LaneChangeDecision best_trigger_decision_;

    // 状态机相关
    int best_transition_context_id_ = -1;
    StateTransitionContexts transition_contexts_{};
    ScenarioStateInfo scenario_state_info_;

    std::vector<BehaviorCandidate> behavior_candidates_;

    std::shared_ptr<ReferenceLine> current_lane_ = nullptr;
    std::shared_ptr<ReferenceLine> target_lane_ = nullptr;
    std::shared_ptr<ReferenceLine> original_lane_ = nullptr;

    // 路径规划
    std::unordered_map<size_t, std::vector<PathData>> path_data_map_;

    // ST图信息
    std::unordered_map<size_t, std::vector<StGraphData>> st_graph_data_map_;

    // 速度规划
    std::unordered_map<size_t, std::vector<SpeedData>> speed_data_map_;

    // 速度优化
    std::unordered_map<size_t, std::vector<SpeedData>>
        speed_data_optimizer_map_;

    // 轨迹评估
    double min_trajectory_cost_ = std::numeric_limits<double>::max();

    // 最优轨迹
    PathData best_path_result_;
    TrajectoryPoints best_trajectory_result_;

    // 规划总状态
    PlanningStatus planning_status_ = PlanningStatus::NOT_INITIALIZED;
    common::AutoDriveModeState auto_drive_mode_state_ =
        common::AutoDriveModeState::OFF;

    // 变道相关变量
    bool has_reached_target_lane_ = false;
    LaneChangeStatus lane_change_status_ = LaneChangeStatus::UNINITIALIZED;
};

}  // namespace framework
}  // namespace arcsoft

#endif /* __SESSION_H__ */