/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __TASK_H__
#define __TASK_H__

#include <iostream>
#include <string>

#include "session.h"

namespace arcsoft {
namespace framework {

class Task {
public:
    explicit Task(const std::string& name, Session* session)
        : name_(name), session_(session) {}

    virtual ~Task() = default;

    const std::string& name() const { return name_; }

    //
    virtual void Init() = 0;
    virtual bool Process(const size_t candidate_transition_context_id) = 0;

protected:
    std::string name_;
    Session* session_ = nullptr;
};

}  // namespace framework
}  // namespace arcsoft

#endif /* __TASK_H__ */
