/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __TASK_RUNNER_H__
#define __TASK_RUNNER_H__

#include <memory>
#include <string>
#include <vector>

#include "task.h"

namespace arcsoft {
namespace framework {

class TaskRunner {
public:
    explicit TaskRunner(Session* session) : session_(session) {}
    ~TaskRunner() = default;

    bool Init(const std::vector<std::string>& task_list);
    bool Run();

    Session* mutable_session() { return session_; }

private:
    std::vector<std::shared_ptr<Task>> pipeline_tasks_list_;

    Session* session_;
};

}  // namespace framework
}  // namespace arcsoft

#endif /* __TASK_RUNNER_H__ */
