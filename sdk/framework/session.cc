/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "session.h"

namespace arcsoft {
namespace framework {

Session::Session(const std::string& config_path)
    : data_manager_(config_path)  // 使用初始化列表
{
    const char* mode = getenv("SIMULATION_MODE");
    is_simulation_mode_ =
        (mode != nullptr) &&
        (std::string(mode) == "True" || std::string(mode) == "true" ||
         std::string(mode) == "TRUE" || std::string(mode) == "1");
    AINFO << "is_simulation_mode: " << is_simulation_mode_;
    data_manager_.set_simulation_mode(is_simulation_mode_);
}

}  // namespace framework
}  // namespace arcsoft
