/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "task_runner.h"

#include "task_factory.h"

namespace arcsoft {
namespace framework {

bool TaskRunner::Init(const std::vector<std::string>& task_list) {
    //
    for (const auto& task_name : task_list) {
        std::shared_ptr<Task> task =
            TaskFactory::Instance().CreateTask(task_name, session_);
        if (!task) {
            AERROR << "Failed to create task: " << task_name.c_str();
            return false;
        }
        task->Init();
        pipeline_tasks_list_.emplace_back(std::move(task));
    }

    return true;
}

bool TaskRunner::Run() {
    for (const auto& task : pipeline_tasks_list_) {
        // 可能加一个candidate_id
        for (size_t i = 0; i < session_->transition_contexts().size(); ++i) {
            auto start_time = std::chrono::system_clock::now();
            if (!task->Process(i)) {
                AERROR << "Running task: " << task->name().c_str()
                       << " Failed! ";
            } else {
                AINFO << "Running task: " << task->name().c_str()
                      << " candidate " << i << " Success! ";
            }
            auto end_time = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end_time - start_time;
            AINFO << task->name() << " used time: " << diff.count() * 1000
                  << " ms.";
        }
    }

    return true;
}

}  // namespace framework
}  // namespace arcsoft