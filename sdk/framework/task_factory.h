/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __TASK_FACTORY_H__
#define __TASK_FACTORY_H__

#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "task.h"

namespace arcsoft {
namespace framework {

class TaskFactory {
public:
    using FactoryFunction = std::function<std::shared_ptr<Task>(
        const std::string &name, Session *session)>;

    // 获取单例实例（线程安全）
    static TaskFactory &Instance() {
        static TaskFactory instance;
        return instance;
    }

    void RegisterTask(const std::string &name, FactoryFunction factory);

    std::shared_ptr<Task> CreateTask(const std::string &name, Session *session);

    std::unordered_map<std::string, std::shared_ptr<Task>> CreateTasks(
        const std::vector<std::string> &task_names, Session *session);

private:
    // 私有构造函数防止外部实例化
    TaskFactory() = default;

    // 删除拷贝构造函数和赋值运算符
    TaskFactory(const TaskFactory &) = delete;
    TaskFactory &operator=(const TaskFactory &) = delete;

    std::unordered_map<std::string, FactoryFunction> task_registry_map_;
};

template <class TaskName>
class TaskRegistrar {
public:
    explicit TaskRegistrar(const std::string &name) {
        TaskFactory::Instance().RegisterTask(name, CreateInstance);
    }
    ~TaskRegistrar() = default;

    static std::shared_ptr<Task> CreateInstance(const std::string &task_name,
                                                Session *session) {
        return std::make_unique<TaskName>(task_name, session);
    }
};

#define REGISTER_TASK(task_name)                                              \
    ::arcsoft::framework::TaskRegistrar<task_name> g_##task_name##_registrar( \
        #task_name);

}  // namespace framework
}  // namespace arcsoft

#endif  // TASK_FACTORY_H
