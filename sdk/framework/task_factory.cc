/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "task_factory.h"

namespace arcsoft {
namespace framework {

void TaskFactory::RegisterTask(const std::string &name,
                               FactoryFunction factory) {
    AINFO << "RegisterTask " << name;
    task_registry_map_[name] = factory;
}

std::shared_ptr<Task> TaskFactory::CreateTask(const std::string &name,
                                              Session *session) {
    auto it = task_registry_map_.find(name);
    if (it != task_registry_map_.end()) {
        return it->second(name, session);
    }
    return nullptr;
}

std::unordered_map<std::string, std::shared_ptr<Task>> TaskFactory::CreateTasks(
    const std::vector<std::string> &task_names, Session *session) {
    std::unordered_map<std::string, std::shared_ptr<Task>> tasks;
    for (const auto &name : task_names) {
        tasks[name] = CreateTask(name, session);
    }
    return tasks;
}

}  // namespace framework
}  // namespace arcsoft
