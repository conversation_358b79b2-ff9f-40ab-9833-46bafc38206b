/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/
#ifndef __SPEED_PLANNER_H__
#define __SPEED_PLANNER_H__

#include "common/log.h"
#include "common/speed/speed_data.h"
#include "framework/task.h"
#include "gridded_path_time_graph.h"

namespace arcsoft {
namespace planning {

class SpeedPlanner : public framework::Task {
public:
    SpeedPlanner(const std::string& name, framework::Session* session)
        : Task(name, session) {}
    ~SpeedPlanner() override = default;

    void Init() override;
    bool Process(const size_t candidate_transition_context_id) override;
    bool SearchPathTimeGraph(SpeedData& speed_data);

private:
    TrajectoryPoint init_point_;
    StGraphData st_graph_data_;
    DpStSpeedOptimizerConfig DpStSpeedOptimizerConfig_;  //自己假设类型
};

}  // namespace planning
}  // namespace arcsoft

#endif /* __SPEED_PLANNER_H__ */