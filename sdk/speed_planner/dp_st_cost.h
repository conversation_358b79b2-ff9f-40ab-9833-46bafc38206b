/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

/**
 * @file
 **/

#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "common/basic_types.h"
#include "common/math/index_list.h"
#include "common/vehicle_config_helper.h"
#include "data_type_define.h"
#include "framework/session.h"
#include "st_graph_point.h"

// #include "modules/common_msgs/basic_msgs/pnc_point.pb.h"
// #include "modules/planning/planning_base/proto/st_drivable_boundary.pb.h"
// #include
// "modules/planning/tasks/path_time_heuristic/proto/path_time_heuristic.pb.h"
// #include "modules/planning/planning_base/common/obstacle.h"
// #include "modules/planning/planning_base/common/speed/st_boundary.h"
// #include "modules/planning/planning_base/common/speed/st_point.h"
// #include "modules/planning/tasks/path_time_heuristic/st_graph_point.h"

namespace arcsoft {
namespace planning {

class DpStCost {
public:
    DpStCost(const DpStSpeedOptimizerConfig& config, const double total_t,
             const double total_s,
             const math::IndexedList<int, Obstacle>& obstacles,
             const TrajectoryPoint& init_point,
             const double curr_follow_distance);

    double GetObstacleCost(const StGraphPoint& point);

    double GetSpatialPotentialCost(const StGraphPoint& point);

    double GetReferenceCost(const STPoint& point,
                            const STPoint& reference_point) const;

    double GetSpeedCost(const STPoint& first, const STPoint& second,
                        const double speed_limit,
                        const double cruise_speed) const;

    double GetAccelCostByTwoPoints(const double pre_speed, const STPoint& first,
                                   const STPoint& second);
    double GetAccelCostByThreePoints(const STPoint& first,
                                     const STPoint& second,
                                     const STPoint& third);

    double GetJerkCostByTwoPoints(const double pre_speed, const double pre_acc,
                                  const STPoint& pre_point,
                                  const STPoint& curr_point);
    double GetJerkCostByThreePoints(const double first_speed,
                                    const STPoint& first_point,
                                    const STPoint& second_point,
                                    const STPoint& third_point);

    double GetJerkCostByFourPoints(const STPoint& first, const STPoint& second,
                                   const STPoint& third, const STPoint& fourth);

private:
    double GetAccelCost(const double accel);
    double JerkCost(const double jerk);

    void AddToKeepClearRange(const math::IndexedList<int, Obstacle>& obstacles);
    static void SortAndMergeRange(
        std::vector<std::pair<double, double>>* keep_clear_range_);
    bool InKeepClearRange(double s) const;

    double LookUpTable(const double ego_speed_ratio) const;

    const DpStSpeedOptimizerConfig& config_;
    const math::IndexedList<int, Obstacle>& obstacles_;

    const TrajectoryPoint& init_point_;

    double unit_t_ = 0.0;
    double total_s_ = 0.0;

    std::unordered_map<std::string, int> boundary_map_;
    std::vector<std::vector<std::pair<double, double>>> boundary_cost_;

    std::vector<std::pair<double, double>> keep_clear_range_;

    std::array<double, 200> accel_cost_;
    std::array<double, 400> jerk_cost_;

    double curr_follow_distance_ = std::numeric_limits<double>::max();

    std::vector<double> ratio_table = {-1.0, -0.5, -0.2, -0.1, 0.0,
                                       0.1,  0.2,  0.3,  0.5,  1.0};
    std::vector<double> percent_table = {0.2, 0.2, 0.4, 0.5, 0.5,
                                         0.5, 0.6, 0.8, 1.0, 1.0};
};

}  // namespace planning
}  // namespace arcsoft
