#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "common/basic_types.h"

namespace arcsoft {
namespace planning {

class DpStSpeedOptimizerConfig {
private:
    /* data */
public:
    DpStSpeedOptimizerConfig(/* args */) {}
    ~DpStSpeedOptimizerConfig() {}

    double unit_t() const { return 1.0f; };
    float dense_unit_s() { return 0.5f; };
    float sparse_unit_s() { return 1.0f; };
    int32_t dense_dimension_s() { return 41; };
    double max_acceleration() const { return 2.0f; };
    double max_deceleration() const { return -5.0f; };
    double safe_distance() const { return 3.0f; }
    double safe_time_gap_min() const { return 1.0f; }
    double obstacle_weight() const { return 1.0f; };
    double default_obstacle_cost() const { return 300.0f; };
    double spatial_potential_penalty() const { return 15.0f; }
    double keep_clear_low_speed_penalty() const { return 10.0f; };
    double default_speed_cost() const { return 10.0f; };
    double exceed_speed_penalty() const { return 500.0f; }
    double low_speed_penalty() const { return 100.0f; };
    bool enable_dp_reference_speed() const { return false; };
    double reference_speed_penalty() const { return 1.0f; }
    double accel_penalty() const { return 2.0f; };
    double decel_penalty() const { return 2.0f; };
    double positive_jerk_coeff() const { return 1.0f; }
    double negative_jerk_coeff() const { return 3.0f; };
    double reference_weight() const { return 0.0f; };

    double hmi_time_gap_;
};

}  // namespace planning
}  // namespace arcsoft
