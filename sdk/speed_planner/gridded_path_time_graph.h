/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

/**
 * @file gridded_path_time_graph.h
 **/

#pragma once

#include <memory>
#include <vector>

#include "common/basic_types.h"
#include "common/speed/speed_data.h"
#include "dp_st_cost.h"
#include "st_graph_point.h"

// #include "modules/common_msgs/config_msgs/vehicle_config.pb.h"
// #include "modules/planning/planning_base/proto/planning_config.pb.h"
// #include
// "modules/planning/tasks/path_time_heuristic/proto/path_time_heuristic.pb.h"
// #include "modules/common/configs/vehicle_config_helper.h"
// #include "modules/common/status/status.h"
// #include "modules/planning/planning_base/common/frame.h"
// #include "modules/planning/planning_base/common/obstacle.h"
// #include "modules/planning/planning_base/common/path_decision.h"
// #include "modules/planning/planning_base/common/speed/speed_data.h"
// #include "modules/planning/planning_base/common/speed/st_point.h"
// #include "modules/planning/planning_base/common/st_graph_data.h"

namespace arcsoft {
namespace planning {

class GriddedPathTimeGraph {
public:
    GriddedPathTimeGraph(const StGraphData& st_graph_data,
                         const DpStSpeedOptimizerConfig& dp_config,
                         const math::IndexedList<int, Obstacle>& obstacles,
                         const TrajectoryPoint& init_point,
                         const double curr_follow_distance);

    bool Search(SpeedData* speed_data,
                std::vector<std::vector<double>>& lowest_highest_record);

private:
    bool InitCostTable();

    bool InitSpeedLimitLookUp();

    bool RetrieveSpeedProfile(SpeedData* speed_data);

    bool CalculateTotalCost(
        std::vector<std::vector<double>>& lowest_highest_record);

    // defined for cyber task
    struct StGraphMessage {
        StGraphMessage(const uint32_t c_, const int32_t r_) : c(c_), r(r_) {}
        uint32_t c;
        uint32_t r;
    };
    void CalculateCostAt(const std::shared_ptr<StGraphMessage>& msg);

    double CalculateEdgeCost(const STPoint& first, const STPoint& second,
                             const STPoint& third, const STPoint& forth,
                             const double speed_limit,
                             const double cruise_speed);
    double CalculateEdgeCostForSecondCol(const uint32_t row,
                                         const double speed_limit,
                                         const double cruise_speed);
    double CalculateEdgeCostForThirdCol(const uint32_t curr_row,
                                        const uint32_t pre_row,
                                        const double speed_limit,
                                        const double cruise_speed);

    // get the row-range of next time step
    void GetRowRange(const StGraphPoint& point, size_t* next_highest_row,
                     size_t* next_lowest_row);

private:
    const StGraphData& st_graph_data_;

    std::vector<double> speed_limit_by_index_;

    std::vector<double> spatial_distance_by_index_;

    // dp st configuration
    DpStSpeedOptimizerConfig gridded_path_time_graph_config_;

    // obstacles based on the current reference line
    const math::IndexedList<int, Obstacle>& obstacles_;

    // initial status
    TrajectoryPoint init_point_;

    // cost utility with configuration;
    DpStCost dp_st_cost_;

    double total_length_t_ = 0.0;
    double unit_t_ = 0.0;
    uint32_t dimension_t_ = 0;

    double total_length_s_ = 0.0;
    double dense_unit_s_ = 0.0;
    double sparse_unit_s_ = 0.0;
    uint32_t dense_dimension_s_ = 0;
    uint32_t sparse_dimension_s_ = 0;
    uint32_t dimension_s_ = 0;

    double max_acceleration_ = 0.0;
    double max_deceleration_ = 0.0;

    // cost_table_[t][s]
    // row: s, col: t --- NOTICE: Please do NOT change.
    std::vector<std::vector<StGraphPoint>> cost_table_;
};

}  // namespace planning
}  // namespace arcsoft
