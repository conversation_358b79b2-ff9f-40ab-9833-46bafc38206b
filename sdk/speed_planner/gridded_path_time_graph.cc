/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

/**
 * @file gridded_path_time_graph.cc
 **/

#include "gridded_path_time_graph.h"

#include <algorithm>
#include <limits>
#include <string>

// #include "modules/common_msgs/basic_msgs/pnc_point.pb.h"

// #include "cyber/common/log.h"
// #include "cyber/task/task.h"
// #include "modules/common/math/vec2d.h"
// #include "modules/common/util/point_factory.h"
// #include "modules/planning/planning_base/common/util/print_debug_info.h"
// #include "modules/planning/planning_base/gflags/planning_gflags.h"

namespace arcsoft {
namespace planning {

namespace {

static constexpr double kDoubleEpsilon = 1.0e-6;

// Continuous-time collision check using linear interpolation as closed-loop
// dynamics
bool CheckOverlapOnDpStGraph(const std::vector<STBoundary>& boundaries,
                             const StGraphPoint& p1, const StGraphPoint& p2) {
    for (const auto boundary : boundaries) {
        if (boundary.boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {
            continue;
        }
        // Check collision between a polygon and a line segment
        if (boundary.HasOverlap({p1.point(), p2.point()})) {
            return true;
        }
    }
    return false;
}
}  // namespace

GriddedPathTimeGraph::GriddedPathTimeGraph(
    const StGraphData& st_graph_data, const DpStSpeedOptimizerConfig& dp_config,
    const math::IndexedList<int, Obstacle>& obstacles,
    const TrajectoryPoint& init_point, const double curr_follow_distance)
    : st_graph_data_(st_graph_data)
    , gridded_path_time_graph_config_(dp_config)
    , obstacles_(obstacles)
    , init_point_(init_point)
    , dp_st_cost_(dp_config, st_graph_data_.total_time_by_conf(),
                  st_graph_data_.path_length(), obstacles, init_point_,
                  curr_follow_distance) {
    total_length_t_ = st_graph_data_.total_time_by_conf();
    unit_t_ = gridded_path_time_graph_config_.unit_t();
    total_length_s_ = st_graph_data_.path_length();
    dense_unit_s_ = gridded_path_time_graph_config_.dense_unit_s();
    sparse_unit_s_ = gridded_path_time_graph_config_.sparse_unit_s();
    dense_dimension_s_ = gridded_path_time_graph_config_.dense_dimension_s();
    // Safety approach preventing unreachable acceleration/deceleration
    const auto& vehicle_config =
        common::VehicleConfigHelper::Instance().GetVehicleParam();
    max_acceleration_ =
        std::min(std::abs(vehicle_config.max_acceleration),
                 std::abs(gridded_path_time_graph_config_.max_acceleration()));
    max_deceleration_ =
        -1.0 *
        std::min(std::abs(vehicle_config.max_deceleration),
                 std::abs(gridded_path_time_graph_config_.max_deceleration()));
}

bool GriddedPathTimeGraph::Search(
    SpeedData* speed_data,
    std::vector<std::vector<double>>& lowest_highest_record) {
    static constexpr double kBounadryEpsilon = 1e-2;
    for (const auto& boundary : st_graph_data_.st_boundaries()) {
        // KeepClear obstacles not considered in Dp St decision
        if (boundary.boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {
            continue;
        }
        // If init point in collision with obstacle, return speed fallback
        if (boundary.IsPointInBoundary({0.0, 0.0}) ||
            (std::fabs(boundary.min_t()) < kBounadryEpsilon &&
             std::fabs(boundary.min_s()) < kBounadryEpsilon)) {
            dimension_t_ =
                static_cast<uint32_t>(
                    std::ceil(total_length_t_ / static_cast<double>(unit_t_))) +
                1;
            std::vector<SpeedPoint> speed_profile;
            double t = 0.0;
            SpeedPoint temp(0.0, 0.0, 0.0, 0.0, 0.0);
            for (uint32_t i = 0; i < dimension_t_; ++i, t += unit_t_) {
                temp.t = t;
                speed_profile.push_back(temp);
            }
            *speed_data = SpeedData(speed_profile);
            return true;
        }
    }

    if (!InitCostTable()) {
        const std::string msg = "Initialize cost table failed.";
        AERROR << msg;
        return false;
    }

    if (!InitSpeedLimitLookUp()) {
        const std::string msg = "Initialize speed limit lookup table failed.";
        AERROR << msg;
        return false;
    }

    if (!CalculateTotalCost(lowest_highest_record)) {
        const std::string msg = "Calculate total cost failed.";
        AERROR << msg;
        return false;
    }
    // AINFO << "cost_table_ (2D table format - row:s, col:t):";
    // AINFO << "s\\t |";

    // // 打印表头 (t 索引)
    // std::ostringstream t_header;
    // t_header << "      ";
    // for (size_t t = 0; t < cost_table_.size(); ++t) {
    //     t_header << std::setw(12) << "t=" << t << " ";
    // }
    // AINFO << t_header.str();
    // // 打印分隔线
    // std::ostringstream separator;
    // separator << "-----+";
    // for (size_t t = 0; t < cost_table_.size(); ++t) {
    //     separator << std::setw(16) << std::setfill('-') << "";
    // }
    // separator << std::setfill(' ');
    // AINFO << separator.str();
    // // 打印表格内容 (s从上往下递减)
    // for (int s = cost_table_[0].size() - 1; s >= 0; --s) {
    //     std::ostringstream row;
    //     row << "s=" << std::setw(2) << s << " |";

    //     for (size_t t = 0; t < cost_table_.size(); ++t) {
    //         const auto& point = cost_table_[t][s];
    //         row << std::fixed << std::setprecision(2) << "("
    //             << point.point().s() << "," << point.point().t() << ")"
    //             << std::setw(4) << point.total_cost() << " "
    //             << point.obstacle_cost() << " "
    //             << point.spatial_potential_cost() << " " <<
    //             point.speed_cost()
    //             << " " << point.GetOptimalSpeed() << ";";
    //     }
    //     AINFO << row.str();
    // }

    if (!RetrieveSpeedProfile(speed_data)) {
        const std::string msg = "Retrieve best speed profile failed.";
        AERROR << msg;
        return false;
    }
    return true;
}

bool GriddedPathTimeGraph::InitCostTable() {
    // Time dimension is homogeneous while Spatial dimension has two
    // resolutions, dense and sparse with dense resolution coming first in the
    // spatial horizon

    // Sanity check for numerical stability
    if (unit_t_ < kDoubleEpsilon) {
        const std::string msg = "unit_t is smaller than the kDoubleEpsilon.";
        AERROR << msg;
        return false;
    }

    // Sanity check on s dimension setting
    if (dense_dimension_s_ < 1) {
        const std::string msg = "dense_dimension_s is at least 1.";
        AERROR << msg;
        return false;
    }

    dimension_t_ = static_cast<uint32_t>(std::ceil(
                       total_length_t_ / static_cast<double>(unit_t_))) +
                   1;

    double sparse_length_s =
        total_length_s_ -
        static_cast<double>(dense_dimension_s_ - 1) * dense_unit_s_;
    sparse_dimension_s_ =
        sparse_length_s > std::numeric_limits<double>::epsilon()
            ? static_cast<uint32_t>(std::ceil(sparse_length_s / sparse_unit_s_))
            : 0;
    dense_dimension_s_ =
        sparse_length_s > std::numeric_limits<double>::epsilon()
            ? dense_dimension_s_
            : static_cast<uint32_t>(
                  std::ceil(total_length_s_ / dense_unit_s_)) +
                  1;
    dimension_s_ = dense_dimension_s_ + sparse_dimension_s_;
    // Sanity Check
    if (dimension_t_ < 1 || dimension_s_ < 1) {
        const std::string msg = "Dp st cost table size incorrect.";
        AERROR << msg;
        return false;
    }

    cost_table_ = std::vector<std::vector<StGraphPoint>>(
        dimension_t_, std::vector<StGraphPoint>(dimension_s_, StGraphPoint()));

    double curr_t = 0.0;
    for (uint32_t i = 0; i < cost_table_.size(); ++i, curr_t += unit_t_) {
        auto& cost_table_i = cost_table_[i];
        double curr_s = 0.0;
        for (uint32_t j = 0; j < dense_dimension_s_;
             ++j, curr_s += dense_unit_s_) {
            cost_table_i[j].Init(i, j, STPoint(curr_s, curr_t));
        }
        curr_s = static_cast<double>(dense_dimension_s_ - 1) * dense_unit_s_ +
                 sparse_unit_s_;
        for (uint32_t j = dense_dimension_s_; j < cost_table_i.size();
             ++j, curr_s += sparse_unit_s_) {
            cost_table_i[j].Init(i, j, STPoint(curr_s, curr_t));
        }
    }

    const auto& cost_table_0 = cost_table_[0];
    spatial_distance_by_index_ = std::vector<double>(cost_table_0.size(), 0.0);
    for (uint32_t i = 0; i < cost_table_0.size(); ++i) {
        spatial_distance_by_index_[i] = cost_table_0[i].point().s();
    }
    return true;
}

bool GriddedPathTimeGraph::InitSpeedLimitLookUp() {
    speed_limit_by_index_.clear();

    speed_limit_by_index_.resize(dimension_s_);
    const auto& speed_limit = st_graph_data_.speed_limit();

    for (uint32_t i = 0; i < dimension_s_; ++i) {
        // speed_limit_by_index_[i] = std::fmin(
        //     speed_limit.GetSpeedLimitByCurvature(cost_table_[0][i].point().s()),
        //     speed_limit.GetSpeedLimitByS(cost_table_[0][i].point().s()));
        speed_limit_by_index_[i] =
            std::fmin(speed_limit.GetSpeedLimitBySoftConstraints(
                          cost_table_[0][i].point().s()),
                      speed_limit.GetSpeedLimitByHardConstraints(
                          cost_table_[0][i].point().s()));
        // AINFO << "[sz add] speed_limit_by_index " << i << " : "
        //       << speed_limit_by_index_[i];
    }
    AINFO << "speedLimitBySoftConstraints: "
          << speed_limit.GetMinSpeedLimitBySoftConstraints();
    return true;
}

bool GriddedPathTimeGraph::CalculateTotalCost(
    std::vector<std::vector<double>>& lowest_highest_record) {
    // col and row are for STGraph
    // t corresponding to col
    // s corresponding to row
    size_t next_highest_row = 0;
    size_t next_lowest_row = 0;
    lowest_highest_record.clear();
    lowest_highest_record.resize(cost_table_.size());

    for (size_t c = 0; c < cost_table_.size(); ++c) {
        size_t highest_row = 0;
        size_t lowest_row = cost_table_.back().size() - 1;

        int count = static_cast<int>(next_highest_row) -
                    static_cast<int>(next_lowest_row) + 1;
        if (count > 0) {
            for (size_t r = next_lowest_row; r <= next_highest_row; ++r) {
                auto msg = std::make_shared<StGraphMessage>(
                    static_cast<uint32_t>(c), static_cast<int32_t>(r));
                CalculateCostAt(msg);
            }
        }

        for (size_t r = next_lowest_row; r <= next_highest_row; ++r) {
            const auto& cost_cr = cost_table_[c][r];
            if (cost_cr.total_cost() <
                std::numeric_limits<double>::infinity()) {
                size_t h_r = 0;
                size_t l_r = 0;
                GetRowRange(cost_cr, &h_r, &l_r);
                highest_row = std::max(highest_row, h_r);
                lowest_row = std::min(lowest_row, l_r);
            }
        }
        for (size_t i = next_lowest_row; i <= next_highest_row; i++) {
            lowest_highest_record[c].push_back(spatial_distance_by_index_[i]);
            if (next_lowest_row == 0 && next_highest_row == 0) {
                lowest_highest_record[c].push_back(
                    cost_table_.back().back().point().s());
            }
        }
        next_highest_row = highest_row;
        next_lowest_row = lowest_row;
    }

    return true;
}

void GriddedPathTimeGraph::GetRowRange(const StGraphPoint& point,
                                       size_t* next_highest_row,
                                       size_t* next_lowest_row) {
    double v0 = 0.0;
    // TODO(all): Record speed information in StGraphPoint and deprecate this.
    // A scaling parameter for DP range search due to the lack of accurate
    // information of the current velocity (set to 1 by default since we use
    // past 1 second's average v as approximation)
    double acc_coeff = 0.5;
    if (!point.pre_point()) {
        v0 = init_point_.v;
    } else {
        v0 = point.GetOptimalSpeed();
    }

    const auto max_s_size = dimension_s_ - 1;
    const double t_squared = unit_t_ * unit_t_;
    const double s_upper_bound = v0 * unit_t_ +
                                 acc_coeff * max_acceleration_ * t_squared +
                                 point.point().s();
    const auto next_highest_itr =
        std::lower_bound(spatial_distance_by_index_.begin(),
                         spatial_distance_by_index_.end(), s_upper_bound);
    if (next_highest_itr == spatial_distance_by_index_.end()) {
        *next_highest_row = max_s_size;
    } else {
        *next_highest_row =
            std::distance(spatial_distance_by_index_.begin(), next_highest_itr);
    }

    const double s_lower_bound =
        std::fmax(0.0,
                  v0 * unit_t_ + acc_coeff * max_deceleration_ * t_squared) +
        point.point().s();
    const auto next_lowest_itr =
        std::lower_bound(spatial_distance_by_index_.begin(),
                         spatial_distance_by_index_.end(), s_lower_bound);
    if (next_lowest_itr == spatial_distance_by_index_.end()) {
        *next_lowest_row = max_s_size;
    } else {
        *next_lowest_row =
            std::distance(spatial_distance_by_index_.begin(), next_lowest_itr);
    }
}

void GriddedPathTimeGraph::CalculateCostAt(
    const std::shared_ptr<StGraphMessage>& msg) {
    const uint32_t c = msg->c;
    const uint32_t r = msg->r;
    auto& cost_cr = cost_table_[c][r];

    cost_cr.SetObstacleCost(dp_st_cost_.GetObstacleCost(cost_cr));
    if (cost_cr.obstacle_cost() > std::numeric_limits<double>::max()) {
        return;
    }

    cost_cr.SetSpatialPotentialCost(
        dp_st_cost_.GetSpatialPotentialCost(cost_cr));

    const auto& cost_init = cost_table_[0][0];
    if (c == 0) {
        // DCHECK_EQ(r, 0U) << "Incorrect. Row should be 0 with col = 0. row: "
        //                  << r;
        cost_cr.SetTotalCost(0.0);
        cost_cr.SetOptimalSpeed(init_point_.v);
        return;
    }

    const double speed_limit = speed_limit_by_index_[r];
    const double cruise_speed = st_graph_data_.cruise_speed();
    // The mininal s to model as constant acceleration formula
    // default: 0.25 * 7 = 1.75 m
    const double min_s_consider_speed = dense_unit_s_ * dimension_t_;

    if (c == 1) {
        const double acc =
            2 * (cost_cr.point().s() / unit_t_ - init_point_.v) / unit_t_;
        if (acc < max_deceleration_ || acc > max_acceleration_) {
            return;
        }

        if (init_point_.v + acc * unit_t_ < -kDoubleEpsilon &&
            cost_cr.point().s() > min_s_consider_speed) {
            return;
        }

        if (CheckOverlapOnDpStGraph(st_graph_data_.st_boundaries(), cost_cr,
                                    cost_init)) {
            return;
        }
        double speed_cost_temp =
            CalculateEdgeCostForSecondCol(r, speed_limit, cruise_speed);
        cost_cr.SetTotalCost(cost_cr.obstacle_cost() +
                             cost_cr.spatial_potential_cost() +
                             cost_init.total_cost() + speed_cost_temp);
        cost_cr.SetPrePoint(cost_init);
        cost_cr.SetOptimalSpeed(init_point_.v + acc * unit_t_);
        cost_cr.SetSpeedCost(speed_cost_temp);
        return;
    }

    static constexpr double kSpeedRangeBuffer = 0.20;
    // constexpr double planning_upper_speed_limit = 31.3;
    const double pre_lowest_s =
        cost_cr.point().s() -
        planning_slack_speed_limit * (1 + kSpeedRangeBuffer) * unit_t_;
    const auto pre_lowest_itr =
        std::lower_bound(spatial_distance_by_index_.begin(),
                         spatial_distance_by_index_.end(), pre_lowest_s);
    uint32_t r_low = 0;
    if (pre_lowest_itr == spatial_distance_by_index_.end()) {
        r_low = dimension_s_ - 1;
    } else {
        r_low = static_cast<uint32_t>(
            std::distance(spatial_distance_by_index_.begin(), pre_lowest_itr));
    }
    const uint32_t r_pre_size = r - r_low + 1;
    const auto& pre_col = cost_table_[c - 1];
    double curr_speed_limit = speed_limit;

    if (c == 2) {
        for (uint32_t i = 0; i < r_pre_size; ++i) {
            uint32_t r_pre = r - i;
            if (std::isinf(pre_col[r_pre].total_cost()) ||
                pre_col[r_pre].pre_point() == nullptr) {
                continue;
            }
            // TODO(Jiaxuan): Calculate accurate acceleration by recording speed
            // data in ST point.
            // Use curr_v = (point.s - pre_point.s) / unit_t as current v
            // Use pre_v = (pre_point.s - prepre_point.s) / unit_t as previous v
            // Current acc estimate: curr_a = (curr_v - pre_v) / unit_t
            // = (point.s + prepre_point.s - 2 * pre_point.s) / (unit_t *
            // unit_t)
            const double curr_a =
                2 *
                ((cost_cr.point().s() - pre_col[r_pre].point().s()) / unit_t_ -
                 pre_col[r_pre].GetOptimalSpeed()) /
                unit_t_;
            if (curr_a < max_deceleration_ || curr_a > max_acceleration_) {
                continue;
            }

            if (pre_col[r_pre].GetOptimalSpeed() + curr_a * unit_t_ <
                    -kDoubleEpsilon &&
                cost_cr.point().s() > min_s_consider_speed) {
                continue;
            }

            // Filter out continuous-time node connection which is in collision
            // with obstacle
            if (CheckOverlapOnDpStGraph(st_graph_data_.st_boundaries(), cost_cr,
                                        pre_col[r_pre])) {
                continue;
            }
            curr_speed_limit =
                std::fmin(curr_speed_limit, speed_limit_by_index_[r_pre]);
            double speed_cost_temp = CalculateEdgeCostForThirdCol(
                r, r_pre, curr_speed_limit, cruise_speed);
            const double cost = cost_cr.obstacle_cost() +
                                cost_cr.spatial_potential_cost() +
                                pre_col[r_pre].total_cost() + speed_cost_temp;

            if (cost < cost_cr.total_cost()) {
                cost_cr.SetTotalCost(cost);
                cost_cr.SetPrePoint(pre_col[r_pre]);
                cost_cr.SetOptimalSpeed(pre_col[r_pre].GetOptimalSpeed() +
                                        curr_a * unit_t_);
                cost_cr.SetSpeedCost(speed_cost_temp);
            }
        }
        return;
    }

    for (uint32_t i = 0; i < r_pre_size; ++i) {
        uint32_t r_pre = r - i;
        if (std::isinf(pre_col[r_pre].total_cost()) ||
            pre_col[r_pre].pre_point() == nullptr) {
            continue;
        }
        // Use curr_v = (point.s - pre_point.s) / unit_t as current v
        // Use pre_v = (pre_point.s - prepre_point.s) / unit_t as previous v
        // Current acc estimate: curr_a = (curr_v - pre_v) / unit_t
        // = (point.s + prepre_point.s - 2 * pre_point.s) / (unit_t * unit_t)
        const double curr_a =
            2 *
            ((cost_cr.point().s() - pre_col[r_pre].point().s()) / unit_t_ -
             pre_col[r_pre].GetOptimalSpeed()) /
            unit_t_;
        if (curr_a > max_acceleration_ || curr_a < max_deceleration_) {
            continue;
        }

        if (pre_col[r_pre].GetOptimalSpeed() + curr_a * unit_t_ <
                -kDoubleEpsilon &&
            cost_cr.point().s() > min_s_consider_speed) {
            continue;
        }

        if (CheckOverlapOnDpStGraph(st_graph_data_.st_boundaries(), cost_cr,
                                    pre_col[r_pre])) {
            continue;
        }

        uint32_t r_prepre = pre_col[r_pre].pre_point()->index_s();
        const StGraphPoint& prepre_graph_point = cost_table_[c - 2][r_prepre];
        if (std::isinf(prepre_graph_point.total_cost())) {
            continue;
        }

        if (!prepre_graph_point.pre_point()) {
            continue;
        }
        const STPoint& triple_pre_point =
            prepre_graph_point.pre_point()->point();
        const STPoint& prepre_point = prepre_graph_point.point();
        const STPoint& pre_point = pre_col[r_pre].point();
        const STPoint& curr_point = cost_cr.point();
        curr_speed_limit =
            std::fmin(curr_speed_limit, speed_limit_by_index_[r_pre]);
        double speed_cost_temp =
            CalculateEdgeCost(triple_pre_point, prepre_point, pre_point,
                              curr_point, curr_speed_limit, cruise_speed);
        double cost = cost_cr.obstacle_cost() +
                      cost_cr.spatial_potential_cost() +
                      pre_col[r_pre].total_cost() + speed_cost_temp;

        if (cost < cost_cr.total_cost()) {
            cost_cr.SetTotalCost(cost);
            cost_cr.SetPrePoint(pre_col[r_pre]);
            cost_cr.SetOptimalSpeed(pre_col[r_pre].GetOptimalSpeed() +
                                    curr_a * unit_t_);
            cost_cr.SetSpeedCost(speed_cost_temp);
        }
    }
}

bool GriddedPathTimeGraph::RetrieveSpeedProfile(SpeedData* speed_data) {
    double min_cost = std::numeric_limits<double>::infinity();
    const StGraphPoint* best_end_point = nullptr;
    for (const StGraphPoint& cur_point : cost_table_.back()) {
        if (!std::isinf(cur_point.total_cost()) &&
            cur_point.total_cost() < min_cost) {
            best_end_point = &cur_point;
            min_cost = cur_point.total_cost();
        }
    }

    for (const auto& row : cost_table_) {
        const StGraphPoint& cur_point = row.back();
        if (!std::isinf(cur_point.total_cost()) &&
            cur_point.total_cost() < min_cost) {
            best_end_point = &cur_point;
            min_cost = cur_point.total_cost();
        }
    }

    if (best_end_point == nullptr) {
        const std::string msg = "Fail to find the best feasible trajectory.";
        AERROR << msg;
        return false;
    }

    std::vector<SpeedPoint> speed_profile;
    const StGraphPoint* cur_point = best_end_point;
    while (cur_point != nullptr) {
        SpeedPoint speed_point(cur_point->point().s(), cur_point->point().t(),
                               0.0, 0.0, 0.0);
        speed_profile.push_back(speed_point);
        cur_point = cur_point->pre_point();
    }

    std::reverse(speed_profile.begin(), speed_profile.end());

    static constexpr double kEpsilon = std::numeric_limits<double>::epsilon();
    if (speed_profile.front().t > kEpsilon ||
        speed_profile.front().s > kEpsilon) {
        const std::string msg = "Fail to retrieve speed profile.";
        AERROR << msg;
        return false;
    }

    for (size_t i = 0; i < speed_profile.size() - 1; ++i) {
        const double v = (speed_profile[i + 1].s - speed_profile[i].s) /
                         (speed_profile[i + 1].t - speed_profile[i].t + 1e-3);
        speed_profile[i + 1].v = v;
        speed_profile[0].v = init_point_.v;
    }

    *speed_data = SpeedData(speed_profile);
    return true;
}

double GriddedPathTimeGraph::CalculateEdgeCost(
    const STPoint& first, const STPoint& second, const STPoint& third,
    const STPoint& forth, const double speed_limit, const double cruise_speed) {
    // AINFO << "t == 3/4: SpeedCost: "
    //       << dp_st_cost_.GetSpeedCost(third, forth, speed_limit,
    //       cruise_speed)
    //       << " AccelCostByThreePoints: "
    //       << dp_st_cost_.GetAccelCostByThreePoints(second, third, forth)
    //       << " JerkCostByFourPoints: "
    //       << dp_st_cost_.GetJerkCostByFourPoints(first, second, third,
    //       forth);
    return dp_st_cost_.GetSpeedCost(third, forth, speed_limit, cruise_speed) +
           dp_st_cost_.GetAccelCostByThreePoints(second, third, forth) +
           dp_st_cost_.GetJerkCostByFourPoints(first, second, third, forth);
}

double GriddedPathTimeGraph::CalculateEdgeCostForSecondCol(
    const uint32_t row, const double speed_limit, const double cruise_speed) {
    double init_speed = init_point_.v;
    double init_acc = init_point_.a;
    const STPoint& pre_point = cost_table_[0][0].point();
    const STPoint& curr_point = cost_table_[1][row].point();
    // AINFO << "t == 1, "
    //       << " s: " << curr_point.s() << " SpeedCost: "
    //       << dp_st_cost_.GetSpeedCost(pre_point, curr_point, speed_limit,
    //                                   cruise_speed)
    //       << " AccelCostByTwoPoints: "
    //       << dp_st_cost_.GetAccelCostByTwoPoints(init_speed, pre_point,
    //                                              curr_point)
    //       << " JerkCostByTwoPoints: "
    //       << dp_st_cost_.GetJerkCostByTwoPoints(init_speed, init_acc,
    //       pre_point,
    //                                             curr_point);
    return dp_st_cost_.GetSpeedCost(pre_point, curr_point, speed_limit,
                                    cruise_speed) +
           dp_st_cost_.GetAccelCostByTwoPoints(init_speed, pre_point,
                                               curr_point) +
           dp_st_cost_.GetJerkCostByTwoPoints(init_speed, init_acc, pre_point,
                                              curr_point);
}

double GriddedPathTimeGraph::CalculateEdgeCostForThirdCol(
    const uint32_t curr_row, const uint32_t pre_row, const double speed_limit,
    const double cruise_speed) {
    double init_speed = init_point_.v;
    const STPoint& first = cost_table_[0][0].point();
    const STPoint& second = cost_table_[1][pre_row].point();
    const STPoint& third = cost_table_[2][curr_row].point();
    // AINFO << "t == 2:"
    //       << " curr_s: " << third.s() << " last_s: " << second.s()
    //       << " SpeedCost: "
    //       << dp_st_cost_.GetSpeedCost(second, third, speed_limit,
    //       cruise_speed)
    //       << " AccelCostByThreePoints:  "
    //       << dp_st_cost_.GetAccelCostByThreePoints(first, second, third)
    //       << " JerkCostByThreePoints: "
    //       << dp_st_cost_.GetJerkCostByThreePoints(init_speed, first, second,
    //                                               third);
    return dp_st_cost_.GetSpeedCost(second, third, speed_limit, cruise_speed) +
           dp_st_cost_.GetAccelCostByThreePoints(first, second, third) +
           dp_st_cost_.GetJerkCostByThreePoints(init_speed, first, second,
                                                third);
}

}  // namespace planning
}  // namespace arcsoft
