/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

/**
 * @file
 **/
#include "dp_st_cost.h"

#include <algorithm>
#include <limits>

// #include "modules/common/configs/vehicle_config_helper.h"
// #include "modules/planning/planning_base/common/speed/st_point.h"
// #include "modules/planning/planning_base/gflags/planning_gflags.h"
// #include
// "modules/planning/planning_interface_base/task_base/utils/st_gap_estimator.h"

namespace arcsoft {
namespace planning {
namespace {
constexpr double kInf = std::numeric_limits<double>::infinity();
constexpr double kTimeCompensate = 2.0;
constexpr double kMinTimeGap = 0.7;
}  // namespace

DpStCost::DpStCost(const DpStSpeedOptimizerConfig& config, const double total_t,
                   const double total_s,
                   const math::IndexedList<int, Obstacle>& obstacles,
                   const TrajectoryPoint& init_point,
                   const double curr_follow_distance)
    : config_(config)
    , obstacles_(obstacles)
    , init_point_(init_point)
    , unit_t_(config_.unit_t())
    , total_s_(total_s)
    , curr_follow_distance_(curr_follow_distance) {
    int index = 0;

    for (const auto& obstacle : obstacles.Items()) {
        boundary_map_[obstacle->path_st_boundary().id()] = index++;
    }

    AddToKeepClearRange(obstacles);

    const auto dimension_t = static_cast<uint32_t>(std::ceil(
                                 total_t / static_cast<double>(unit_t_))) +
                             1;
    boundary_cost_.resize(obstacles_.Size());
    for (auto& vec : boundary_cost_) {
        vec.resize(dimension_t, std::make_pair(-1.0, -1.0));
    }
    accel_cost_.fill(-1.0);
    jerk_cost_.fill(-1.0);
}

void DpStCost::AddToKeepClearRange(
    const math::IndexedList<int, Obstacle>& obstacles) {
    for (const auto& obstacle : obstacles.Items()) {
        if (obstacle->path_st_boundary().IsEmpty()) {
            continue;
        }
        if (obstacle->path_st_boundary().boundary_type() !=
            STBoundary::BoundaryType::KEEP_CLEAR) {
            continue;
        }

        double start_s = obstacle->path_st_boundary().min_s();
        double end_s = obstacle->path_st_boundary().max_s();
        keep_clear_range_.emplace_back(start_s, end_s);
    }
    SortAndMergeRange(&keep_clear_range_);
}

void DpStCost::SortAndMergeRange(
    std::vector<std::pair<double, double>>* keep_clear_range) {
    if (!keep_clear_range || keep_clear_range->empty()) {
        return;
    }
    std::sort(keep_clear_range->begin(), keep_clear_range->end());
    size_t i = 0;
    size_t j = i + 1;
    while (j < keep_clear_range->size()) {
        if (keep_clear_range->at(i).second < keep_clear_range->at(j).first) {
            ++i;
            ++j;
        } else {
            keep_clear_range->at(i).second = std::max(
                keep_clear_range->at(i).second, keep_clear_range->at(j).second);
            ++j;
        }
    }
    keep_clear_range->resize(i + 1);
}

bool DpStCost::InKeepClearRange(double s) const {
    for (const auto& p : keep_clear_range_) {
        if (p.first <= s && p.second >= s) {
            return true;
        }
    }
    return false;
}

double DpStCost::LookUpTable(const double ego_speed_ratio) const {
    // 检查边界情况
    if (ego_speed_ratio <= ratio_table.front()) {
        return percent_table.front();
    }
    if (ego_speed_ratio >= ratio_table.back()) {
        return percent_table.back();
    }

    auto it = std::lower_bound(ratio_table.begin(), ratio_table.end(),
                               ego_speed_ratio);
    size_t upper_idx = it - ratio_table.begin();
    size_t lower_idx = upper_idx - 1;

    // 使用提供的lerp函数进行线性插值
    return math::lerp(percent_table[lower_idx], ratio_table[lower_idx],
                      percent_table[upper_idx], ratio_table[upper_idx],
                      ego_speed_ratio);
}

double DpStCost::GetObstacleCost(const StGraphPoint& st_graph_point) {
    const double s = st_graph_point.point().s();
    const double t = st_graph_point.point().t();
    const double init_v = init_point_.v;

    double cost = 0.0;

    for (const auto& obstacle : obstacles_.Items()) {
        // Not applying obstacle approaching cost to virtual obstacle like
        // created stop fences if (obstacle->IsVirtual()) {
        //   continue;
        // }

        // // Stop obstacles are assumed to have a safety margin when mapping
        // them out,
        // // so repelling force in dp st is not needed as it is designed to
        // have adc
        // // stop right at the stop distance we design in prior mapping process
        // if (obstacle->LongitudinalDecision().has_stop()) {
        //   continue;
        // }

        auto boundary = obstacle->path_st_boundary();

        if (t < boundary.min_t() || t > boundary.max_t()) {
            continue;
        }
        if (boundary.IsPointInBoundary(st_graph_point.point())) {
            return kInf;
        }
        double s_upper = 0.0;
        double s_lower = 0.0;

        int boundary_index = boundary_map_[boundary.id()];
        if (boundary_cost_[boundary_index][st_graph_point.index_t()].first <
            0.0) {
            boundary.GetBoundarySRange(t, &s_upper, &s_lower);
            boundary_cost_[boundary_index][st_graph_point.index_t()] =
                std::make_pair(s_upper, s_lower);
        } else {
            s_upper =
                boundary_cost_[boundary_index][st_graph_point.index_t()].first;
            s_lower =
                boundary_cost_[boundary_index][st_graph_point.index_t()].second;
        }
        if (s < s_lower) {
            // 跟车距离计算更新：速度差+查表
            const double obstacle_speed =
                obstacle.get()->GetObstacleTrajectoryPointByTime(t).v;
            double speed_error = init_v - obstacle_speed;
            double ego_speed_ratio = speed_error / std::max(init_v, 0.1);
            ego_speed_ratio = std::max(std::min(ego_speed_ratio, 1.0), -1.0);
            const double ego_percent = LookUpTable(ego_speed_ratio);
            const double fusion_speed = std::max(
                (1.0 - ego_percent) * obstacle_speed + ego_percent * init_v,
                0.1);
            double follow_distance_s =
                std::max(std::min(config_.hmi_time_gap_ * fusion_speed +
                                      config_.safe_distance(),
                                  curr_follow_distance_),
                         kMinTimeGap * fusion_speed + config_.safe_distance());
            // AINFO << "obstalce_id: " << obstacle->id()
            //       << " speed: " << obstacle_speed
            //       << " acc: " << obstacle->acceleration()
            //       << " s: " << obstacle->center_x()
            //       << " s1: " << obstacle->trajectory()[10].x
            //       << " v1: " << obstacle->trajectory()[10].v
            //       << " s2: " << obstacle->trajectory()[20].x
            //       << " v2: " << obstacle->trajectory()[20].v
            //       << " s3: " << obstacle->trajectory()[30].x
            //       << " v3: " << obstacle->trajectory()[30].v
            //       << " s4: " << obstacle->trajectory()[40].x
            //       << " v4: " << obstacle->trajectory()[40].v
            //       << " ego_percent: " << ego_percent
            //       << " fusion_speed: " << fusion_speed
            //       << " follow_distance_s: " << follow_distance_s;

            if (s + follow_distance_s < s_lower) {
                continue;
            } else {
                auto s_diff = follow_distance_s - s_lower + s;
                cost += config_.obstacle_weight() *
                        config_.default_obstacle_cost() *
                        (s_diff / follow_distance_s);
            }
        } else if (s > s_upper) {
            const double overtake_distance_s = 20;
            if (s >
                s_upper + overtake_distance_s) {  // or calculated from velocity
                continue;
            } else {
                auto s_diff = overtake_distance_s + s_upper - s;
                cost += config_.obstacle_weight() *
                        config_.default_obstacle_cost() * s_diff * s_diff;
            }
        }
    }
    return cost * unit_t_;
}

double DpStCost::GetSpatialPotentialCost(const StGraphPoint& point) {
    double det_spatial = (total_s_ - point.point().s()) / total_s_;
    return det_spatial * config_.spatial_potential_penalty();
}

double DpStCost::GetReferenceCost(const STPoint& point,
                                  const STPoint& reference_point) const {
    return config_.reference_weight() * (point.s() - reference_point.s()) *
           (point.s() - reference_point.s()) * unit_t_;
}

double DpStCost::GetSpeedCost(const STPoint& first, const STPoint& second,
                              const double speed_limit,
                              const double cruise_speed) const {
    double cost = 0.0;
    const double speed = (second.s() - first.s()) / unit_t_;
    if (speed < 0) {
        return kInf;
    }

    const double max_adc_stop_speed = 0.5;
    if (speed < max_adc_stop_speed && InKeepClearRange(second.s())) {
        // first.s in range
        cost += config_.keep_clear_low_speed_penalty() * unit_t_ *
                config_.default_speed_cost();
    }

    double det_speed = (speed - speed_limit) / speed_limit;
    if (det_speed > 0) {
        cost += config_.exceed_speed_penalty() * config_.default_speed_cost() *
                (det_speed * det_speed) * unit_t_;
    } else if (det_speed < 0) {
        cost += config_.low_speed_penalty() * config_.default_speed_cost() *
                -det_speed * unit_t_;
    }
    // AINFO << "[speed cost] s: " << second.s() << ", t: " << second.t()
    //       << ", cost : " << cost << ", det_speed: " << det_speed;

    if (config_.enable_dp_reference_speed()) {
        double diff_speed = speed - cruise_speed;
        cost += config_.reference_speed_penalty() *
                config_.default_speed_cost() * fabs(diff_speed) * unit_t_;
    }

    return cost;
}

double DpStCost::GetAccelCost(const double accel) {
    double cost = 0.0;
    static constexpr double kEpsilon = 0.1;
    static constexpr size_t kShift = 100;
    const size_t accel_key =
        static_cast<size_t>(accel / kEpsilon + 0.5 + kShift);
    // DCHECK_LT(accel_key, accel_cost_.size());
    if (accel_key >= accel_cost_.size()) {
        return kInf;
    }

    if (accel_cost_.at(accel_key) < 0.0) {
        const double accel_sq = accel * accel;
        double max_acc = config_.max_acceleration();
        double max_dec = config_.max_deceleration();
        double accel_penalty = config_.accel_penalty();
        double decel_penalty = config_.decel_penalty();

        if (accel > 0.0) {
            cost = accel_penalty * accel_sq;
        } else {
            cost = decel_penalty * accel_sq;
        }
        cost += accel_sq * decel_penalty * decel_penalty /
                    (1 + std::exp(1.0 * (accel - max_dec))) +
                accel_sq * accel_penalty * accel_penalty /
                    (1 + std::exp(-1.0 * (accel - max_acc)));
        accel_cost_.at(accel_key) = cost;
    } else {
        cost = accel_cost_.at(accel_key);
    }
    return cost * unit_t_;
}

double DpStCost::GetAccelCostByThreePoints(const STPoint& first,
                                           const STPoint& second,
                                           const STPoint& third) {
    double accel =
        (first.s() + third.s() - 2 * second.s()) / (unit_t_ * unit_t_);
    return GetAccelCost(accel);
}

double DpStCost::GetAccelCostByTwoPoints(const double pre_speed,
                                         const STPoint& pre_point,
                                         const STPoint& curr_point) {
    double current_speed = (curr_point.s() - pre_point.s()) / unit_t_;
    double accel = (current_speed - pre_speed) / unit_t_;
    return GetAccelCost(accel);
}

double DpStCost::JerkCost(const double jerk) {
    double cost = 0.0;
    static constexpr double kEpsilon = 0.1;
    static constexpr size_t kShift = 200;
    const size_t jerk_key = static_cast<size_t>(jerk / kEpsilon + 0.5 + kShift);
    if (jerk_key >= jerk_cost_.size()) {
        return kInf;
    }

    if (jerk_cost_.at(jerk_key) < 0.0) {
        double jerk_sq = jerk * jerk;
        if (jerk > 0) {
            cost = config_.positive_jerk_coeff() * jerk_sq * unit_t_;
        } else {
            cost = config_.negative_jerk_coeff() * jerk_sq * unit_t_;
        }
        jerk_cost_.at(jerk_key) = cost;
    } else {
        cost = jerk_cost_.at(jerk_key);
    }

    // TODO(All): normalize to unit_t_
    return cost;
}

double DpStCost::GetJerkCostByFourPoints(const STPoint& first,
                                         const STPoint& second,
                                         const STPoint& third,
                                         const STPoint& fourth) {
    double jerk = (fourth.s() - 3 * third.s() + 3 * second.s() - first.s()) /
                  (unit_t_ * unit_t_ * unit_t_);
    return JerkCost(jerk);
}

double DpStCost::GetJerkCostByTwoPoints(const double pre_speed,
                                        const double pre_acc,
                                        const STPoint& pre_point,
                                        const STPoint& curr_point) {
    const double curr_speed = (curr_point.s() - pre_point.s()) / unit_t_;
    const double curr_accel = (curr_speed - pre_speed) / unit_t_;
    const double jerk = (curr_accel - pre_acc) / unit_t_;
    return JerkCost(jerk);
}

double DpStCost::GetJerkCostByThreePoints(const double first_speed,
                                          const STPoint& first,
                                          const STPoint& second,
                                          const STPoint& third) {
    const double pre_speed = (second.s() - first.s()) / unit_t_;
    const double pre_acc = (pre_speed - first_speed) / unit_t_;
    const double curr_speed = (third.s() - second.s()) / unit_t_;
    const double curr_acc = (curr_speed - pre_speed) / unit_t_;
    const double jerk = (curr_acc - pre_acc) / unit_t_;
    return JerkCost(jerk);
}

}  // namespace planning
}  // namespace arcsoft
