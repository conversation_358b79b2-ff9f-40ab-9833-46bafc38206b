/******************************************************************************
 * Copyright(c) ArcSoft, All right reserved.
 * NOA Planning Module
 *****************************************************************************/

#include "speed_planner.h"

namespace arcsoft {
namespace planning {

void SpeedPlanner::Init() {}

bool SpeedPlanner::SearchPathTimeGraph(SpeedData& speed_data) {
    // 计算当前跟车距离
    double curr_follow_distance = std::numeric_limits<double>::max();
    for (const auto& boundary : st_graph_data_.st_boundaries()) {
        if (boundary.min_t() < 0.1) {
            AINFO << "[sz add!!!!] boundary_s: " << boundary.min_s()
                  << " min_t: " << boundary.min_t();
            curr_follow_distance = boundary.min_s();
        }
    }

    GriddedPathTimeGraph st_graph(st_graph_data_, DpStSpeedOptimizerConfig_,
                                  session_->mutable_obstacles(), init_point_,
                                  curr_follow_distance);

    std::vector<std::vector<double>> lowest_highest_record;
    auto& data_manager = session_->data_manager();

    if (!st_graph.Search(&speed_data, lowest_highest_record)) {
        AERROR << "failed to search graph with dynamic programming.";
        data_manager.store_array("lowest_highest_bound_st",
                                 lowest_highest_record);

        DLOG_ARRAY_BEGIN("lowest_highest_bound_st");
        for (size_t i = 0; i < lowest_highest_record.size(); i++) {
            DLOG_ARRAY_BEGIN();
            for (auto temp : lowest_highest_record[i]) {
                DLOG_ARRAY_ITEM(temp);
            }
            DLOG_ARRAY_END();
        }
        DLOG_ARRAY_END("lowest_highest_bound_st");
        return false;
    }

    DLOG_ARRAY_BEGIN("SpeedData");
    for (auto temp : speed_data) {
        AINFO << "SpeedData.t:  " << temp.t << "  SpeedData.s  " << temp.s
              << "  SpeedData.v  " << temp.v << "  SpeedData.a  " << temp.a
              << "  SpeedData.da  " << temp.da;
        DLOG_DICT_BEGIN();
        DLOG_DICT_ITEM("t", temp.t);
        DLOG_DICT_ITEM("s", temp.s);
        DLOG_DICT_ITEM("v", temp.v);
        DLOG_DICT_ITEM("a", temp.a);
        DLOG_DICT_ITEM("da", temp.da);
        DLOG_DICT_END();
    }
    DLOG_ARRAY_END("SpeedData");

    data_manager.store_array("SpeedPlanData", speed_data);
    data_manager.store_array("lowest_highest_bound_st", lowest_highest_record);
    DLOG_ARRAY_BEGIN("lowest_highest_bound_st");
    for (size_t i = 0; i < lowest_highest_record.size(); i++) {
        DLOG_ARRAY_BEGIN();
        for (auto temp : lowest_highest_record[i]) {
            DLOG_ARRAY_ITEM(temp);
        }
        DLOG_ARRAY_END();
    }
    DLOG_ARRAY_END("lowest_highest_bound_st");
    return true;
}

// 输入：起始点的 [v, a]，笛卡尔坐标系下的path
// 输出：速度集
bool SpeedPlanner::Process(const size_t candidate_transition_context_id) {
    init_point_ = session_->planning_start_point();
    auto& speed_data_map = session_->mutable_speed_data_map();
    speed_data_map.clear();

    std::vector<StGraphData>* st_graph_data_sets;
    auto& st_graph_data_sets_map = session_->mutable_st_graph_data_map();
    auto st_graph_data_it =
        st_graph_data_sets_map.find(candidate_transition_context_id);
    if (st_graph_data_it != st_graph_data_sets_map.end()) {
        st_graph_data_sets = &st_graph_data_it->second;
    } else {
        AERROR << "candidate_transition_context_id: "
               << candidate_transition_context_id << " path_data is nullptr! ";
        return false;
    }
    // 载入人为设置时距
    DpStSpeedOptimizerConfig_.hmi_time_gap_ =
        session_->local_view().vehicle_chassis.hmi_time_gap;

    std::vector<SpeedData> speed_data_vec;
    // 前期该vector中仅有一条path
    for (auto& st_graph_data : *st_graph_data_sets) {
        // TODO 速度规划
        SpeedData speed_data;
        st_graph_data_ = st_graph_data;
        auto st_boundarys = st_graph_data_.st_boundaries();
        auto& data_manager = session_->data_manager();
        std::vector<std::string> obstacle_id_sets;

        DLOG_ARRAY_BEGIN("obstacle_id_sets");
        for (size_t i = 0; i < st_boundarys.size(); i++) {
            // AINFO << "num of st_boundarys:" <<
            // st_boundarys.size();
            auto st_boundary = st_boundarys.at(i);
            auto lower_points = st_boundary.lower_points();
            auto upper_points = st_boundary.upper_points();
            data_manager.store_array("lower_points_" + std::to_string(i),
                                     lower_points);
            data_manager.store_array("upper_points_" + std::to_string(i),
                                     upper_points);
            DLOG_DICT_BEGIN();
            DLOG_DICT_ITEM("i", i);
            DLOG_DICT_ITEM("st_boundary_id", st_boundary.id());
            DLOG_DICT_ITEM("lower_points_[0].s()", lower_points[0].s());
            DLOG_DICT_ITEM("lower_points_[0].t()", lower_points[0].t());
            DLOG_DICT_ITEM("lower_points_[1].s()", lower_points[1].s());
            DLOG_DICT_ITEM("lower_points_[1].t()", lower_points[1].t());
            DLOG_DICT_ITEM("upper_points_[0].s()", upper_points[0].s());
            DLOG_DICT_ITEM("upper_points_[0].t()", upper_points[0].t());
            DLOG_DICT_ITEM("upper_points_[1].s()", upper_points[1].s());
            DLOG_DICT_ITEM("upper_points_[1].t()", upper_points[1].t());

            DLOG_DICT_END();
            obstacle_id_sets.emplace_back(st_boundary.id());
            // AINFO << "i: " << i << "  st_boundary_id:" << st_boundary.id();
        }
        DLOG_ARRAY_END("obstacle_id_sets");

        data_manager.store_array("obstacle_id_sets", obstacle_id_sets);
        if (!SearchPathTimeGraph(speed_data)) {
            return false;
        }
        speed_data_vec.emplace_back(speed_data);

        for (size_t j = 0; j < st_graph_data.st_boundaries().size(); j++) {
            auto mutable_st_boundary = st_graph_data.st_boundaries();
            auto lower_points_st = mutable_st_boundary[j].lower_points();
            if (mutable_st_boundary[j].lower_points().size() > 0) {
                double low_point_t = lower_points_st.at(0).t();
                double low_point_s = lower_points_st.at(0).s();
                double path_data_point_compare_s;
                for (size_t i = 1; i < speed_data.size(); i++) {
                    if (speed_data[i].t > low_point_t) {
                        path_data_point_compare_s =
                            (low_point_t - speed_data[i - 1].t) *
                                (speed_data[i].s - speed_data[i - 1].s) +
                            speed_data[i - 1].s;
                        break;
                    }
                }
                if (low_point_s >= path_data_point_compare_s) {
                    st_graph_data.mutable_st_boundaries()[j].SetBoundaryType(
                        STBoundary::BoundaryType::YIELD);
                } else {
                    st_graph_data.mutable_st_boundaries()[j].SetBoundaryType(
                        STBoundary::BoundaryType::OVERTAKE);
                }
            }
        }
    }
    speed_data_map.emplace(candidate_transition_context_id, speed_data_vec);
    return true;
}

}  // namespace planning
}  // namespace arcsoft