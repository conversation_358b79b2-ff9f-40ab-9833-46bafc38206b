#pragma once

#include <arcsoft_ads_planning.h>

#include <Eigen/Eigen>
#include <algorithm>
#include <deque>
#include <fstream>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include "auto_drive_mode_state_machine/auto_drive_mode_state_machine.h"
#include "behavior_generator/behavior_generator.h"
#include "common/log.h"
#include "common/vehicle_config_helper.h"
#include "framework/session.h"
#include "framework/task_factory.h"
#include "lane_change_decider/lane_change_decider.h"
#include "obstacle_manager/obstacle_manager.h"
#include "path_optimizer/path_optimizer.h"
#include "reference_line_manager/reference_line_manager.h"
#include "scenario/scenario_state_machine.h"
#include "speed_optimizer/piecewise_jerk_speed_optimizer.h"
#include "speed_planner/speed_planner.h"
#include "st_graph_generator/st_graph_generator.h"
#include "trajectory_evaluator/trajectory_evaluator.h"
#include "trajectory_stitcher/publishable_trajectory.h"
#include "trajectory_stitcher/trajectory_stitcher.h"

// #define NOMINMAX
#define WIN32_LEAN_AND_MEAN
#include <ghc/filesystem.hpp>
namespace fs = ghc::filesystem;

namespace ads {
namespace pln {

using namespace arcsoft::planning;

class Planning {
public:
    Planning();
    ~Planning();

    ArcAdsStatus Init();
    ArcAdsStatus Process(
        ArcAdsPlanningTrajectory* out_trajectory,
        const ArcAdsEgoMotionMeasurement* ego_motion,
        const ArcAdsObjectMeasurements* objects,
        const ArcAdsLocalMapMeasurementV2* local_map,
        const ArcAdsTrafficLightLogicalAssembly* traffic_light_logical_assembly,
        const ArcAdsSpeedLimits* speed_limits,
        const ArcAdsNavReferenceLines* nav_reference_lines,
        const ArcAdsMultiModalPrediction* multi_modal_prediction);
    // ArcAdsStatus LoadEgoMotion(const ArcAdsEgoMotionMeasurement* ego_motion);
    ArcAdsStatus LoadVehicleCanSignal(
        const ArcAdsVehicleCanSignal* vehicle_can_signal);
    ArcAdsStatus LoadAutoDriveMode(const ArcAdsAutoDriveMode auto_drive_mode);
    ArcAdsStatus LoadHMISpeedLimit(const float speed_limit);
    ArcAdsStatus LoadHMITimeGap(const float time_gap);
    ArcAdsStatus Reset();

private:
    bool update_reference_lines(
        const ArcAdsNavReferenceLines* nav_reference_lines);

    bool update_obstacles_info(
        const ArcAdsObjectMeasurements* objects,
        const ArcAdsMultiModalPrediction* multi_modal_prediction);

    bool update_ego_pose(const ArcAdsEgoMotionMeasurement* ego_motion);

    bool update_trajectory_info(ArcAdsPlanningTrajectory* out_trajectory);

    void transform_trajectory(const ArcAdsGlobalPose3d& current_ego_pose);

    TrajectoryPoint trajectory_stitcher();

    ArcAdsVehicleLightFlag UpdateTurnSignal(
        const ArcAdsVehicleLightFlags& light_flag_raw);

private:
    std::shared_ptr<arcsoft::framework::Session> session_ = nullptr;
    std::shared_ptr<arcsoft::planning::ReferenceLineManager>
        reference_line_manager_ = nullptr;
    std::shared_ptr<arcsoft::planning::ObstacleManager> obstacle_manager_ =
        nullptr;
    std::shared_ptr<arcsoft::planning::LaneChangeDecider> lane_change_decider_ =
        nullptr;
    std::shared_ptr<arcsoft::planning::ScenarioStateMachine>
        scenario_state_machine_ = nullptr;
    std::shared_ptr<arcsoft::planning::AutoDriveModeStateMachine>
        auto_drive_mode_state_machine_ = nullptr;

    int cnt_ = 0;

    // Coordinate Transform
    TrajectoryPoints last_trajectory_current_frame_;
    double last_trajectory_header_;
    ArcAdsGlobalPose3d last_ego_pose_;
    std::vector<TrajectoryPoint> stitching_trajectory_;

    // Turn Light Filter
    std::deque<ArcAdsVehicleLightFlags> light_flag_queue_;
    uint32_t MAX_QUEUE_SIZE_ = 50;
};

}  // namespace pln
}  // namespace ads