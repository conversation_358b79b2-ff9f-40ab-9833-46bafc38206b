# coding: utf-8
# author: <PERSON> <<EMAIL>>
# created: 2024-09-14

"""
Jenkins client for small delivery and push

https://arcconfluence.arcsoft.com.cn:9443/pages/viewpage.action?pageId=147696419

This script sends a request to <PERSON> to create a small delivery record or push the documentation to the target project.
It should be run in gitlab-CI pipeline, with the following environment variables defined (in the gitlab-CI pipeline settings):
- ARTIFACTORY_USER : Artifactory user, e.g. zz9555
- JENKINS_TOKEN    : Jenkins token for authentication, get it from https://abs01.arcsoft.com.cn/jenkins/user/{user_id}/configure
"""

import os
import sys
import mimetypes
import base64
import argparse
import glob
import re


# Compatible import for Python 2 and 3
if sys.version_info[0] == 3:
    import urllib.request as urllib_request
    import urllib.parse as urllib_parse
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
elif sys.version_info[0] == 2:
    import urllib2 as urllib_request
    import urllib as urllib_parse


ARTIFACTORY_URL = os.environ.get("ARTIFACTORY_URL", "https://artifactory.arcsoft.com.cn/artifactory")
ARTIFACTORY_REPO = os.environ.get("ARTIFACTORY_REPO", "generic")


def log(msg):
    print('jenkins_client.py: ' + msg)


# Helper function to encode multipart form data
def encode_multipart_formdata(fields, filepath):
    boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW'
    crlf = b'\r\n'  # Bytes type for CRLF
    lines = []
    is_local_package = True

    # use artifactory zip
    if filepath.startswith('https://artifactory.arcsoft.com.cn'):
        fields['ARTIFACTORY_PACKAGE'] = filepath
        is_local_package = False

    for (key, value) in fields.items():
        lines.append('--' + boundary)
        lines.append('Content-Disposition: form-data; name="{}"'.format(key))
        lines.append('')
        if isinstance(value, str):  # Check for str in both Python 2 and 3
            lines.append(value.encode('utf-8'))
        elif isinstance(value, unicode):  # Specifically for Python 2
            lines.append(value.encode('utf-8'))
        else:
            lines.append(value)

    # use local zip
    if is_local_package:
        filename = os.path.basename(filepath)
        lines.append('--' + boundary)
        lines.append('Content-Disposition: form-data; name="DLIVERY_PACKAGE"; filename="{}"'.format(filename))
        mime_type = mimetypes.guess_type(filepath)[0] or 'application/octet-stream'
        lines.append('Content-Type: {}'.format(mime_type))
        lines.append(b'')  # Empty byte string for file content separator

        with open(filepath, 'rb') as f:
            lines.append(f.read())

    # end of the form data
    lines.append('--' + boundary + '--')
    lines.append(b'')

    # Convert lines to bytes
    body = crlf.join(line if isinstance(line, bytes) else line.encode('utf-8') for line in lines)
    content_type = 'multipart/form-data; boundary={}'.format(boundary)
    return body, content_type


def encode_pm_notes(pm_notes):
    # Encode Chinese text to ensure it displays correctly in the file form in the HTTP request
    if sys.version_info[0] < 3:
        if isinstance(pm_notes, str):
            pm_notes = pm_notes.decode('utf-8')  # Convert to unicode
        encoded_pm_notes = urllib_parse.quote(pm_notes.encode('utf-8'))
    else:
        encoded_pm_notes = urllib_parse.quote(pm_notes)
    return encoded_pm_notes


def log_multi_line_message(message, title):
    message_lines = message.split('\n')
    if len(message_lines) > 1:
        log("{}:".format(title))
        for line in message_lines:
            print(line)
    else:
        log("{}: {}".format(title, message))


class DeliveryInfo(object):
    def __init__(self, user_id, jenkins_token, doc_project_id, code_url, version_number, git_commit_id, pm_notes, package_path):
        self.user_id = user_id
        self.jenkins_token = jenkins_token
        self.doc_project_id = doc_project_id
        self.code_url = code_url
        self.version_number = version_number
        self.git_commit_id = git_commit_id
        self.pm_notes = pm_notes
        self.package_path = package_path

    def print_info(self):
        log("Delivery Info:")
        log("user_id: " + self.user_id)
        log("jenkins_token: " + self.jenkins_token)
        log("doc_project_id: " + self.doc_project_id)
        log("code_url: " + self.code_url)
        log("version_number: " + self.version_number)
        log("git_commit_id: " + self.git_commit_id)
        log_multi_line_message(self.pm_notes, "pm_notes")
        log("package_path: " + self.package_path)

    def validate(self):
        Validator.validate_path(self.package_path)
        Validator.validate_project_id(self.doc_project_id)
        Validator.validate_version_number_with_package_path(self.version_number, self.package_path)


def load_env_variable(var_name, hint=None, exit_on_fail=True):

    """Fetching environment variables with better error handling"""
    value = os.environ.get(var_name, None)
    if not value:
        if hint:
            log("{} environment variable is not set. {}".format(var_name, hint))
        else:
            log("{} environment variable is not set".format(var_name))
        if exit_on_fail:
            sys.exit(1)
    return value


def get_delivery_info(doc_project_id, package_path):
    """
    @param doc_project_id: e.g. "23425-9". Please get it from doc-server
    @param package_path: local zip file path or artifactory zip file path
    """
    log("Getting delivery info...")

    user_id = load_env_variable('ARTIFACTORY_USER', "e.g. zz9555")
    jenkins_token = load_env_variable('JENKINS_TOKEN', "Please get it from https://abs01.arcsoft.com.cn/jenkins/user/{}/configure".format(user_id))
    code_url = load_env_variable('CI_PROJECT_URL', "Automatically set by gitlab CI, e.g. https://git.arcsoft.com.cn/ads/arcsoft_ads")
    version_number = load_env_variable('CI_COMMIT_TAG', "Automatically set by gitlab CI, e.g. 0.3.5-alpha")
    git_commit_id = load_env_variable('CI_COMMIT_SHA', "Automatically set by gitlab CI. locally get it by running `git rev-parse HEAD`")
    pm_notes = load_env_variable('CI_COMMIT_TAG_MESSAGE', "Automatically set by gitlab CI. locally get it by running `git for-each-ref refs/tags/<tag-name> --format='%(contents:subject)'`")

    return DeliveryInfo(user_id, jenkins_token, doc_project_id, code_url, version_number, git_commit_id, pm_notes, package_path)


def send_delivery_request(info):
    """
    @type info: DeliveryInfo
    """
    info.validate()

    log("Sending delivery request to Jenkins...")

    url = "https://abs01.arcsoft.com.cn/jenkins/job/GitLab_CI_SmallDelivery/buildWithParameters"
    fields = {
        'PROJECT_ID': info.doc_project_id,
        'VERSION_NUMBER': info.version_number,
        'CODE_PATH': info.code_url,
        'CODE_COMMIT_ID': info.git_commit_id,
        'HZ_PM_Notes': encode_pm_notes(info.pm_notes)
    }

    # Create the request object
    req = urllib_request.Request(url)

    auth = "{}:{}".format(info.user_id, info.jenkins_token)
    encoded_auth = base64.b64encode(auth.encode('utf-8')).decode('utf-8')
    req.add_header('Authorization', 'Basic {}'.format(encoded_auth))

    body, content_type = encode_multipart_formdata(fields, info.package_path)
    req.add_header('Content-Type', content_type)
    req.add_header('Content-Length', str(len(body)))
    req.data = body

    # Send the request and get the response
    try:
        response = urllib_request.urlopen(req)
        log("Response Code: {}".format(response.getcode()))

        resp_body = response.read().decode('utf-8').strip()
        log_multi_line_message(resp_body, "Response Body")

        log("Delivery request to Jenkins successful")
    except urllib_request.HTTPError as e:
        log("HTTPError: {} - {}".format(e.code, e.reason))
    except urllib_request.URLError as e:
        log("URLError: {}".format(e.reason))


class PushInfo(object):
    def __init__(self, user_id, jenkins_token, doc_project_id, version_number, doc_push_target_project_id, pm_notes, package_path):
        self.user_id = user_id
        self.jenkins_token = jenkins_token
        self.doc_project_id = doc_project_id
        self.version_number = version_number
        self.doc_push_target_project_id = doc_push_target_project_id
        self.pm_notes = pm_notes
        self.package_path = package_path

    def print_info(self):
        log("Push Info:")
        log("user_id: " + self.user_id)
        log("jenkins_token: " + self.jenkins_token)
        log("doc_project_id: " + self.doc_project_id)
        log("version_number: " + self.version_number)
        log("doc_push_target_project_id: " + self.doc_push_target_project_id)
        log_multi_line_message(self.pm_notes, "pm_notes")
        log("package_path: " + self.package_path)

    def validate(self):
        Validator.validate_path(self.package_path)
        Validator.validate_project_id(self.doc_project_id)
        Validator.validate_push_target_project_id(self.doc_push_target_project_id)
        Validator.validate_version_number_with_package_path(self.version_number, self.package_path)


def match_version_in_path(version_string, zip_path):
    """
    Match the version number in the zip path
    @param version_string: <major>.<minor>.<build>[extra]  e.g. 0.3.5-test
    @param zip_path:
        - format1: e.g. ads_demo-0.3.5-test-qnx710-aarch64.zip , containts exact <major>.<minor>.<build>[extra]
        - format2: e.g. ARCSOFT_ADS_0.3.40023.5-TEST_QNX710_AARCH64_SHARED_09192024.zip , which contains <major>.<minor>.<build>.<build>[extra]
    """
    # Get the lower case of the version string and zip path
    version_string = version_string.lower()
    zip_path = os.path.basename(zip_path).lower()

    # Split the version string into major, minor, build, and suffix
    regex = r'(\d+)\.(\d+)\.(\d+)-([a-z]+)'

    # Match the input version string
    match = re.match(regex, version_string)
    if not match:
        return False

    # Extract major, minor, build, and suffix
    major, minor, build, suffix = match.groups()

    # Manually escape backslashes in the pattern
    pattern = r'{}\.{}\.\d*\.{}-{}'.format(re.escape(major), re.escape(minor), re.escape(build), re.escape(suffix))

    # Search for the pattern in the zip_path
    result = re.search(pattern, zip_path)

    return bool(result)


class Validator(object):
    @staticmethod
    def validate_path(package_path):
        ext = package_path.split('.')[-1]
        if ext.lower() != 'zip':
            log("Invalid package path: {}. It should ends with .zip or .ZIP".format(package_path))
            sys.exit(1)

    @staticmethod
    def validate_project_id(project_id):
        if len(project_id) < 5:
            log("Invalid doc_project_id: {}".format(project_id))
            sys.exit(1)

    @staticmethod
    def validate_push_target_project_id(push_target_project_id):
        if len(push_target_project_id) < 5:
            log("Invalid doc_push_target_project_id: {}".format(push_target_project_id))
            sys.exit(1)

    @staticmethod
    def validate_version_number_with_package_path(version_number, package_path):
        if version_number in package_path:
            return True

        if match_version_in_path(version_number, package_path):
            log("Version number matched in package path.")
        else:
            log("Version number not matched in package path: {} and {}".format(version_number, package_path))
            sys.exit(1)


def send_push_request(info):
    """
    @type info: PushInfo
    """
    info.validate()

    log("Sending push request to Jenkins...")

    url = "https://abs01.arcsoft.com.cn/jenkins/job/GitLab_CI_SmallDelivery/buildWithParameters"
    fields = {
        'PROJECT_ID': info.doc_project_id,
        'VERSION_NUMBER': info.version_number,
        'SmallDelivery': 'false',
        'AutoPush': 'true',
        'PushTarget': info.doc_push_target_project_id,
        'HZ_PM_Notes': encode_pm_notes(info.pm_notes)
    }

    # Create the request object
    req = urllib_request.Request(url)

    auth = "{}:{}".format(info.user_id, info.jenkins_token)
    encoded_auth = base64.b64encode(auth.encode('utf-8')).decode('utf-8')
    req.add_header('Authorization', 'Basic {}'.format(encoded_auth))

    body, content_type = encode_multipart_formdata(fields, info.package_path)
    req.add_header('Content-Type', content_type)
    req.add_header('Content-Length', str(len(body)))
    req.data = body

    # Send the request and get the response
    try:
        response = urllib_request.urlopen(req)
        log("Response Code: {}".format(response.getcode()))
        log("Response Body: ")
        log(response.read().decode('utf-8'))
        log("Push request to Jenkins successful")
    except urllib_request.HTTPError as e:
        log("HTTPError: {} - {}".format(e.code, e.reason))
    except urllib_request.URLError as e:
        log("URLError: {}".format(e.reason))


def get_push_info(doc_project_id, package_path, doc_push_target_project_id):
    """
    @param doc_project_id: e.g. "23425-9". Please get it from doc-server
    @param package_path: local zip file path or artifactory zip file path
    @param doc_push_target_project_id: target project id, e.g. "23425-9"
    """
    def log(message):
        print(message)  # Replace with proper logging as needed

    log("Getting push info...")

    user_id = load_env_variable('ARTIFACTORY_USER', "e.g. zz9555")
    jenkins_token = load_env_variable('JENKINS_TOKEN', "Please get it from https://abs01.arcsoft.com.cn/jenkins/user/{}/configure".format(user_id))
    version_number = load_env_variable('CI_COMMIT_TAG', "Automatically set by gitlab CI, e.g. 0.3.5-alpha")
    pm_notes = load_env_variable('CI_COMMIT_TAG_MESSAGE', "Automatically set by gitlab CI. locally get it by running `git for-each-ref refs/tags/<tag-name> --format='%(contents:subject)'`")

    return PushInfo(user_id, jenkins_token, doc_project_id, version_number, doc_push_target_project_id, pm_notes, package_path)


def get_remote_url(pkg_name, pkg_user, pkg_version, platform, arch, build_type):
    extra = ''
    if build_type:
        extra = '-' + build_type
    zipname = '{}-{}-{}-{}{}.zip'.format(pkg_name, pkg_version, platform, arch, extra)
    remote_path = '{}/{}/{}/{}'.format(pkg_user, pkg_name, pkg_version, zipname)
    url = '{}/{}/{}'.format(ARTIFACTORY_URL, ARTIFACTORY_REPO, remote_path)

    return url


def get_package_path(build_dir):
    zip_files = glob.glob(os.path.join(build_dir, '*.zip')) + glob.glob(os.path.join(build_dir, '*.ZIP'))
    if len(zip_files) == 1:
        package_path = zip_files[0]
    else:
        print("number of zip file is ", len(zip_files))
        log("Error: number of zip file is not 1")
        sys.exit(1)
    return package_path


def delivery_arcpkg(args):
    """
    Usage:
    for arcpkg lib pkg:
        -p qnx710 -a aarch64 -b build --target_type lib --library_type shared --pkg_name arcsoft_ads_detection --pkg_user ads
    for arcpkg demo exe pkg:
        -p qnx710 -a aarch64 -b build --target_type exe --pkg_name ads_detection --pkg_user ads
    """

    platform = args.platform
    arch = args.arch
    if args.target_type == 'lib':
        build_type = args.library_type
    else:
        build_type = None

    pkg_version = os.environ.get('CI_COMMIT_TAG', 'main')
    if pkg_version == 'main':
        log("CI_COMMIT_TAG environment variable is not set")
        sys.exit(1)

    package_path = get_remote_url(args.pkg_name, args.pkg_user, pkg_version, platform, arch, build_type)
    delivery_info = get_delivery_info(args.project_id, package_path)
    delivery_info.print_info()
    send_delivery_request(delivery_info)


def push_arcpkg(args):
    """
    Usage:
    for arcpkg lib pkg:
        -p qnx710 -a aarch64 -b build --target_type lib --library_type shared --pkg_name arcsoft_ads_detection --pkg_user ads
    for arcpkg demo exe pkg:
        -p qnx710 -a aarch64 -b build --target_type exe --pkg_name ads_detection --pkg_user ads
    """
    platform = args.platform
    arch = args.arch
    if args.target_type == 'lib':
        build_type = args.library_type
    else:
        build_type = None

    pkg_version = os.environ.get('CI_COMMIT_TAG', 'main')
    if pkg_version == 'main':
        log("CI_COMMIT_TAG environment variable is not set")
        sys.exit(1)

    package_path = get_remote_url(args.pkg_name, args.pkg_user, pkg_version, platform, arch, build_type)
    push_info = get_push_info(args.project_id, package_path, args.push_target_project_id)
    push_info.print_info()
    send_push_request(push_info)


def delivery_arcsdk(args):
    package_path = get_package_path(args.build_dir)
    delivery_info = get_delivery_info(args.project_id, package_path)

    delivery_info.print_info()
    send_delivery_request(delivery_info)


def push_arcsdk(args):
    package_path = get_package_path(args.build_dir)
    push_info = get_push_info(args.project_id, package_path, args.push_target_project_id)

    push_info.print_info()
    send_push_request(push_info)


def main():
    if os.environ.get('CI_COMMIT_TAG', None) is None:
        log("Delivery and Push is only supported for tagged commits")
        return

    parser = argparse.ArgumentParser(description="Jenkins Client for Small Delivery and Push")
    parser.add_argument('task', choices=['delivery_demo_exe', 'push_demo_exe', 'delivery_sdk', 'push_sdk'], help="Task type")
    args, remaining_args = parser.parse_known_args()

    if args.task == 'delivery_demo_exe':
        task_parser = argparse.ArgumentParser(description="Delivery Demo EXE Task")
        task_parser.add_argument('--project_id', help='doc project id, e.g. "23186-8". Please get it from doc-server', required=True)
        task_parser.add_argument('-p', '--platform', help='platform', required=True)
        task_parser.add_argument('-a', '--arch', help='architecture')
        task_parser.add_argument('-b', '--build_dir', help='build directory')
        task_parser.add_argument('--target_type', choices=['exe', 'lib'], help='target type', default='exe')
        task_parser.add_argument('-l', '--library_type', choices=['static', 'shared'], help='choose library type: static or shared', default='static')
        task_parser.add_argument('--pkg_name', help='package name', required=True)
        task_parser.add_argument('--pkg_user', help='package user', required=True)
        task_args = task_parser.parse_args(remaining_args)
        delivery_arcpkg(task_args)

    elif args.task == 'push_demo_exe':
        task_parser = argparse.ArgumentParser(description="Push Demo EXE Task")
        task_parser.add_argument('--project_id', help='doc project id, e.g. "23186-8". Please get it from doc-server', required=True)
        task_parser.add_argument('--push_target_project_id', help='push target doc project id, e.g. "23217-4". Please get it from doc-server', required=True)
        task_parser.add_argument('-p', '--platform', help='platform', required=True)
        task_parser.add_argument('-a', '--arch', help='architecture')
        task_parser.add_argument('-b', '--build_dir', help='build directory')
        task_parser.add_argument('--target_type', choices=['exe', 'lib'], help='target type', default='exe')
        task_parser.add_argument('-l', '--library_type', choices=['static', 'shared'], help='choose library type: static or shared', default='static')
        task_parser.add_argument('--pkg_name', help='package name', required=True)
        task_parser.add_argument('--pkg_user', help='package user', required=True)
        task_args = task_parser.parse_args(remaining_args)
        push_arcpkg(task_args)

    elif args.task == 'delivery_sdk':
        task_parser = argparse.ArgumentParser(description="Delivery SDK Task")
        task_parser.add_argument('--project_id', help='doc project id, e.g. "23186-8". Please get it from doc-server', required=True)
        task_parser.add_argument('-b', '--build_dir', help='build directory')
        task_args = task_parser.parse_args(remaining_args)
        delivery_arcsdk(task_args)

    elif args.task == 'push_sdk':
        task_parser = argparse.ArgumentParser(description="Push SDK Task")
        task_parser.add_argument('--project_id', help='doc project id, e.g. "23186-8". Please get it from doc-server', required=True)
        task_parser.add_argument('--push_target_project_id', help='push target doc project id, e.g. "23217-4". Please get it from doc-server', required=True)
        task_parser.add_argument('-b', '--build_dir', help='build directory')
        task_args = task_parser.parse_args(remaining_args)
        push_arcsdk(task_args)


if __name__ == '__main__':
    main()
